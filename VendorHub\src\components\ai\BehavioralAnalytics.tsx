import React, { createContext, useContext, useRef, useCallback, useEffect } from 'react';
import { AppState } from 'react-native';
import { usePersonalizationEngine } from './PersonalizationEngine';
import { useIntelligentCache } from '../performance/IntelligentCache';
import { useAuth } from '../../contexts/AuthContext';

interface UserSession {
  id: string;
  userId: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  screenViews: ScreenView[];
  interactions: UserInteraction[];
  searchQueries: SearchQuery[];
  purchases: Purchase[];
  deviceInfo: DeviceInfo;
  locationInfo?: LocationInfo;
}

interface ScreenView {
  screenName: string;
  timestamp: number;
  duration?: number;
  scrollDepth?: number;
  exitMethod?: 'back' | 'navigation' | 'app_close';
}

interface UserInteraction {
  type: 'tap' | 'swipe' | 'pinch' | 'long_press' | 'shake';
  target: string;
  timestamp: number;
  coordinates?: { x: number; y: number };
  metadata?: Record<string, any>;
}

interface SearchQuery {
  query: string;
  timestamp: number;
  resultsCount: number;
  selectedResult?: string;
  filters?: Record<string, any>;
}

interface Purchase {
  productId: string;
  vendorId: string;
  amount: number;
  timestamp: number;
  paymentMethod?: string;
  category?: string;
}

interface DeviceInfo {
  platform: 'ios' | 'android';
  version: string;
  screenSize: { width: number; height: number };
  language: string;
  timezone: string;
}

interface LocationInfo {
  country: string;
  city: string;
  coordinates?: { latitude: number; longitude: number };
}

interface BehaviorPattern {
  id: string;
  name: string;
  description: string;
  frequency: number;
  confidence: number;
  triggers: string[];
  outcomes: string[];
  timePattern?: {
    preferredHours: number[];
    preferredDays: string[];
  };
}

interface UserSegment {
  id: string;
  name: string;
  description: string;
  criteria: Record<string, any>;
  userCount: number;
  characteristics: string[];
}

interface BehavioralInsight {
  type: 'pattern' | 'anomaly' | 'trend' | 'opportunity';
  title: string;
  description: string;
  confidence: number;
  impact: 'low' | 'medium' | 'high';
  actionable: boolean;
  recommendations: string[];
  data: any;
}

interface BehavioralAnalyticsContextType {
  startSession: () => string;
  endSession: (sessionId: string) => Promise<void>;
  trackScreenView: (screenName: string) => () => void;
  trackInteraction: (interaction: Omit<UserInteraction, 'timestamp'>) => Promise<void>;
  trackSearch: (query: SearchQuery) => Promise<void>;
  trackPurchase: (purchase: Purchase) => Promise<void>;
  getUserPatterns: (userId?: string) => Promise<BehaviorPattern[]>;
  getUserSegment: (userId?: string) => Promise<UserSegment | null>;
  getInsights: (timeRange?: number) => Promise<BehavioralInsight[]>;
  getSessionAnalytics: (sessionId: string) => Promise<UserSession | null>;
  exportUserData: (userId?: string) => Promise<string>;
  generatePersonalityProfile: (userId?: string) => Promise<any>;
}

const BehavioralAnalyticsContext = createContext<BehavioralAnalyticsContextType | undefined>(undefined);

export const useBehavioralAnalytics = (): BehavioralAnalyticsContextType => {
  const context = useContext(BehavioralAnalyticsContext);
  if (!context) {
    throw new Error('useBehavioralAnalytics must be used within a BehavioralAnalyticsProvider');
  }
  return context;
};

interface BehavioralAnalyticsProviderProps {
  children: React.ReactNode;
}

export const BehavioralAnalyticsProvider: React.FC<BehavioralAnalyticsProviderProps> = ({
  children,
}) => {
  const personalizationEngine = usePersonalizationEngine();
  const cache = useIntelligentCache();
  const { user } = useAuth();
  
  // Current session tracking
  const currentSession = useRef<UserSession | null>(null);
  const currentScreenView = useRef<ScreenView | null>(null);
  const sessionStartTime = useRef<number>(0);
  
  // Analytics data
  const userSessions = useRef<Map<string, UserSession>>(new Map());
  const behaviorPatterns = useRef<Map<string, BehaviorPattern>>(new Map());
  const userSegments = useRef<Map<string, UserSegment>>(new Map());

  useEffect(() => {
    initializeAnalytics();
    setupAppStateListener();
    
    return () => {
      if (currentSession.current) {
        endSession(currentSession.current.id);
      }
    };
  }, [user?.id]);

  const initializeAnalytics = async () => {
    try {
      // Load existing analytics data
      const sessions = await cache.get<Record<string, UserSession>>('user-sessions');
      if (sessions) {
        userSessions.current = new Map(Object.entries(sessions));
      }
      
      const patterns = await cache.get<Record<string, BehaviorPattern>>('behavior-patterns');
      if (patterns) {
        behaviorPatterns.current = new Map(Object.entries(patterns));
      }
      
      const segments = await cache.get<Record<string, UserSegment>>('user-segments');
      if (segments) {
        userSegments.current = new Map(Object.entries(segments));
      }
      
      // Start initial session
      if (user?.id) {
        startSession();
      }
    } catch (error) {
      console.warn('Failed to initialize behavioral analytics:', error);
    }
  };

  const setupAppStateListener = () => {
    const handleAppStateChange = (nextAppState: string) => {
      if (nextAppState === 'background' && currentSession.current) {
        endSession(currentSession.current.id);
      } else if (nextAppState === 'active' && user?.id && !currentSession.current) {
        startSession();
      }
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);
    return () => subscription?.remove();
  };

  const getDeviceInfo = (): DeviceInfo => {
    // In a real implementation, this would use react-native-device-info
    return {
      platform: 'ios', // Platform.OS
      version: '1.0.0', // DeviceInfo.getVersion()
      screenSize: { width: 375, height: 812 }, // Dimensions.get('window')
      language: 'en', // I18nManager.getConstants().localeIdentifier
      timezone: 'Asia/Bahrain', // DeviceInfo.getTimezone()
    };
  };

  const startSession = useCallback((): string => {
    if (!user?.id) return '';
    
    const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const now = Date.now();
    
    const session: UserSession = {
      id: sessionId,
      userId: user.id,
      startTime: now,
      screenViews: [],
      interactions: [],
      searchQueries: [],
      purchases: [],
      deviceInfo: getDeviceInfo(),
    };
    
    currentSession.current = session;
    sessionStartTime.current = now;
    userSessions.current.set(sessionId, session);
    
    return sessionId;
  }, [user?.id]);

  const endSession = useCallback(async (sessionId: string): Promise<void> => {
    const session = userSessions.current.get(sessionId);
    if (!session) return;
    
    const now = Date.now();
    session.endTime = now;
    session.duration = now - session.startTime;
    
    // End current screen view
    if (currentScreenView.current) {
      currentScreenView.current.duration = now - currentScreenView.current.timestamp;
      session.screenViews.push(currentScreenView.current);
      currentScreenView.current = null;
    }
    
    // Save session data
    try {
      await cache.set(`session:${sessionId}`, session, {
        ttl: 30 * 24 * 60 * 60 * 1000, // 30 days
        tags: ['analytics', 'session'],
      });
      
      // Update user sessions cache
      await cache.set('user-sessions', Object.fromEntries(userSessions.current), {
        ttl: 30 * 24 * 60 * 60 * 1000, // 30 days
        tags: ['analytics'],
      });
      
      // Analyze session for patterns
      await analyzeSessionPatterns(session);
    } catch (error) {
      console.warn('Failed to save session:', error);
    }
    
    currentSession.current = null;
  }, [cache]);

  const trackScreenView = useCallback((screenName: string) => {
    const now = Date.now();
    
    // End previous screen view
    if (currentScreenView.current && currentSession.current) {
      currentScreenView.current.duration = now - currentScreenView.current.timestamp;
      currentSession.current.screenViews.push(currentScreenView.current);
    }
    
    // Start new screen view
    currentScreenView.current = {
      screenName,
      timestamp: now,
    };
    
    // Track behavior
    personalizationEngine.trackBehavior({
      action: 'view',
      entityType: 'vendor', // Simplified
      entityId: screenName,
      metadata: {
        screenName,
        sessionId: currentSession.current?.id,
      },
    });
    
    // Return function to end screen view
    return () => {
      if (currentScreenView.current && currentScreenView.current.screenName === screenName) {
        currentScreenView.current.duration = Date.now() - currentScreenView.current.timestamp;
        if (currentSession.current) {
          currentSession.current.screenViews.push(currentScreenView.current);
        }
        currentScreenView.current = null;
      }
    };
  }, [personalizationEngine]);

  const trackInteraction = useCallback(async (
    interaction: Omit<UserInteraction, 'timestamp'>
  ): Promise<void> => {
    if (!currentSession.current) return;
    
    const fullInteraction: UserInteraction = {
      ...interaction,
      timestamp: Date.now(),
    };
    
    currentSession.current.interactions.push(fullInteraction);
    
    // Track in personalization engine
    await personalizationEngine.trackBehavior({
      action: interaction.type === 'tap' ? 'view' : 'filter',
      entityType: 'vendor', // Simplified
      entityId: interaction.target,
      metadata: {
        interactionType: interaction.type,
        sessionId: currentSession.current.id,
        ...interaction.metadata,
      },
    });
  }, [personalizationEngine]);

  const trackSearch = useCallback(async (query: SearchQuery): Promise<void> => {
    if (!currentSession.current) return;
    
    currentSession.current.searchQueries.push(query);
    
    // Track in personalization engine
    await personalizationEngine.trackBehavior({
      action: 'search',
      entityType: 'vendor', // Simplified
      entityId: query.query,
      metadata: {
        searchQuery: query.query,
        resultsCount: query.resultsCount,
        sessionId: currentSession.current.id,
      },
    });
  }, [personalizationEngine]);

  const trackPurchase = useCallback(async (purchase: Purchase): Promise<void> => {
    if (!currentSession.current) return;
    
    currentSession.current.purchases.push(purchase);
    
    // Track in personalization engine
    await personalizationEngine.trackBehavior({
      action: 'purchase',
      entityType: 'product',
      entityId: purchase.productId,
      metadata: {
        vendorId: purchase.vendorId,
        amount: purchase.amount,
        category: purchase.category,
        sessionId: currentSession.current.id,
      },
    });
  }, [personalizationEngine]);

  const analyzeSessionPatterns = async (session: UserSession) => {
    try {
      // Analyze session duration patterns
      if (session.duration) {
        const durationPattern = categorizeDuration(session.duration);
        updateBehaviorPattern(`duration_${durationPattern}`, {
          name: `${durationPattern} Session Duration`,
          description: `User tends to have ${durationPattern} app sessions`,
          triggers: ['app_open'],
          outcomes: [`session_duration_${durationPattern}`],
        });
      }
      
      // Analyze screen flow patterns
      if (session.screenViews.length > 1) {
        const screenFlow = session.screenViews.map(sv => sv.screenName).join(' -> ');
        updateBehaviorPattern(`flow_${screenFlow.replace(/\s/g, '_')}`, {
          name: 'Screen Navigation Pattern',
          description: `Common navigation path: ${screenFlow}`,
          triggers: [session.screenViews[0].screenName],
          outcomes: [session.screenViews[session.screenViews.length - 1].screenName],
        });
      }
      
      // Analyze interaction patterns
      const interactionTypes = session.interactions.map(i => i.type);
      const dominantInteraction = getMostFrequent(interactionTypes);
      if (dominantInteraction) {
        updateBehaviorPattern(`interaction_${dominantInteraction}`, {
          name: `${dominantInteraction} Interaction Preference`,
          description: `User prefers ${dominantInteraction} interactions`,
          triggers: ['user_interaction'],
          outcomes: [dominantInteraction],
        });
      }
      
      // Analyze time patterns
      const sessionHour = new Date(session.startTime).getHours();
      const timeCategory = categorizeTime(sessionHour);
      updateBehaviorPattern(`time_${timeCategory}`, {
        name: `${timeCategory} Usage Pattern`,
        description: `User is active during ${timeCategory} hours`,
        triggers: ['time_based'],
        outcomes: [`${timeCategory}_activity`],
        timePattern: {
          preferredHours: [sessionHour],
          preferredDays: [new Date(session.startTime).toLocaleDateString('en-US', { weekday: 'long' })],
        },
      });
    } catch (error) {
      console.warn('Failed to analyze session patterns:', error);
    }
  };

  const updateBehaviorPattern = (patternId: string, patternData: Partial<BehaviorPattern>) => {
    const existing = behaviorPatterns.current.get(patternId);
    
    if (existing) {
      existing.frequency += 1;
      existing.confidence = Math.min(existing.confidence + 0.1, 1);
    } else {
      behaviorPatterns.current.set(patternId, {
        id: patternId,
        frequency: 1,
        confidence: 0.5,
        triggers: [],
        outcomes: [],
        ...patternData,
      } as BehaviorPattern);
    }
  };

  const categorizeDuration = (duration: number): string => {
    const minutes = duration / (1000 * 60);
    if (minutes < 2) return 'brief';
    if (minutes < 10) return 'short';
    if (minutes < 30) return 'medium';
    return 'long';
  };

  const categorizeTime = (hour: number): string => {
    if (hour >= 6 && hour < 12) return 'morning';
    if (hour >= 12 && hour < 17) return 'afternoon';
    if (hour >= 17 && hour < 22) return 'evening';
    return 'night';
  };

  const getMostFrequent = <T,>(array: T[]): T | null => {
    if (array.length === 0) return null;

    const frequency = array.reduce((acc, item) => {
      acc[item as string] = (acc[item as string] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return Object.entries(frequency).reduce((a, b) =>
      frequency[a[0]] > frequency[b[0]] ? a : b
    )[0] as T;
  };

  const getUserPatterns = useCallback(async (userId?: string): Promise<BehaviorPattern[]> => {
    const targetUserId = userId || user?.id;
    if (!targetUserId) return [];
    
    // Filter patterns for specific user (simplified - in production, patterns would be user-specific)
    return Array.from(behaviorPatterns.current.values())
      .sort((a, b) => b.confidence - a.confidence);
  }, [user?.id]);

  const getUserSegment = useCallback(async (userId?: string): Promise<UserSegment | null> => {
    const targetUserId = userId || user?.id;
    if (!targetUserId) return null;
    
    // Simplified segmentation logic
    const patterns = await getUserPatterns(targetUserId);
    const preferences = await personalizationEngine.getUserPreferences();
    
    // Determine segment based on behavior patterns
    let segmentId = 'casual_browser';
    
    if (patterns.some(p => p.name.includes('purchase'))) {
      segmentId = 'active_buyer';
    } else if (patterns.some(p => p.name.includes('search'))) {
      segmentId = 'researcher';
    } else if (patterns.some(p => p.name.includes('long'))) {
      segmentId = 'engaged_user';
    }
    
    return userSegments.current.get(segmentId) || {
      id: segmentId,
      name: 'Casual Browser',
      description: 'Users who browse casually without specific intent',
      criteria: {},
      userCount: 1,
      characteristics: ['Browsing focused', 'Low purchase intent'],
    };
  }, [user?.id, personalizationEngine]);

  const getInsights = useCallback(async (timeRange = 7): Promise<BehavioralInsight[]> => {
    const insights: BehavioralInsight[] = [];
    const cutoffTime = Date.now() - (timeRange * 24 * 60 * 60 * 1000);
    
    // Analyze recent sessions
    const recentSessions = Array.from(userSessions.current.values())
      .filter(session => session.startTime > cutoffTime);
    
    if (recentSessions.length === 0) return insights;
    
    // Session duration insight
    const avgDuration = recentSessions.reduce((sum, session) => 
      sum + (session.duration || 0), 0) / recentSessions.length;
    
    if (avgDuration > 10 * 60 * 1000) { // More than 10 minutes
      insights.push({
        type: 'pattern',
        title: 'High Engagement Detected',
        description: `User shows high engagement with average session duration of ${Math.round(avgDuration / 60000)} minutes`,
        confidence: 0.8,
        impact: 'high',
        actionable: true,
        recommendations: ['Show premium features', 'Offer personalized recommendations'],
        data: { avgDuration },
      });
    }
    
    // Search pattern insight
    const totalSearches = recentSessions.reduce((sum, session) => 
      sum + session.searchQueries.length, 0);
    
    if (totalSearches > 5) {
      insights.push({
        type: 'opportunity',
        title: 'Search-Heavy User',
        description: 'User performs many searches, indicating specific needs',
        confidence: 0.7,
        impact: 'medium',
        actionable: true,
        recommendations: ['Improve search suggestions', 'Show related products'],
        data: { totalSearches },
      });
    }
    
    return insights;
  }, []);

  const getSessionAnalytics = useCallback(async (sessionId: string): Promise<UserSession | null> => {
    return userSessions.current.get(sessionId) || null;
  }, []);

  const exportUserData = useCallback(async (userId?: string): Promise<string> => {
    const targetUserId = userId || user?.id;
    if (!targetUserId) return '{}';
    
    const userSessions = Array.from(userSessions.current.values())
      .filter(session => session.userId === targetUserId);
    
    const patterns = await getUserPatterns(targetUserId);
    const segment = await getUserSegment(targetUserId);
    const insights = await getInsights();
    
    const exportData = {
      userId: targetUserId,
      sessions: userSessions,
      patterns,
      segment,
      insights,
      exportDate: new Date().toISOString(),
    };
    
    return JSON.stringify(exportData, null, 2);
  }, [user?.id, getUserPatterns, getUserSegment, getInsights]);

  const generatePersonalityProfile = useCallback(async (userId?: string): Promise<any> => {
    const patterns = await getUserPatterns(userId);
    const preferences = await personalizationEngine.getUserPreferences();
    const segment = await getUserSegment(userId);
    
    // Generate personality traits based on behavior
    const traits = {
      explorative: patterns.filter(p => p.name.includes('search')).length / patterns.length,
      decisive: patterns.filter(p => p.name.includes('purchase')).length / patterns.length,
      social: patterns.filter(p => p.name.includes('share')).length / patterns.length,
      patient: patterns.filter(p => p.name.includes('long')).length / patterns.length,
      focused: patterns.filter(p => p.name.includes('specific')).length / patterns.length,
    };
    
    return {
      traits,
      segment: segment?.name,
      topCategories: Object.entries(preferences.categories)
        .sort(([, a], [, b]) => b - a)
        .slice(0, 3)
        .map(([category]) => category),
      behaviorSummary: `${segment?.description || 'Active user'} with ${patterns.length} identified patterns`,
    };
  }, [getUserPatterns, personalizationEngine, getUserSegment]);

  const contextValue: BehavioralAnalyticsContextType = {
    startSession,
    endSession,
    trackScreenView,
    trackInteraction,
    trackSearch,
    trackPurchase,
    getUserPatterns,
    getUserSegment,
    getInsights,
    getSessionAnalytics,
    exportUserData,
    generatePersonalityProfile,
  };

  return (
    <BehavioralAnalyticsContext.Provider value={contextValue}>
      {children}
    </BehavioralAnalyticsContext.Provider>
  );
};
