import React, { useRef, useEffect } from 'react';
import { RefreshControl, Animated, StyleSheet } from 'react-native';
import { RTLView } from '../RTL';
import * as Haptics from 'expo-haptics';
import { useThemedStyles } from '../../hooks';
import { SPACING } from '../../constants/theme';
import type ThemeColors  from '../../contexts/ThemeContext';

export interface EnhancedRefreshControlProps {
  refreshing: boolean;
  onRefresh: () => void;
  colors?: string[];
  tintColor?: string;
  title?: string;
  titleColor?: string;
  size?: 'default' | 'large';
  progressBackgroundColor?: string;
  enabled?: boolean;
}

export const EnhancedRefreshControl: React.FC<EnhancedRefreshControlProps> = ({
  refreshing,
  onRefresh,
  colors,
  tintColor,
  title,
  titleColor,
  size = 'default',
  progressBackgroundColor,
  enabled = true,
}) => {
  const styles = useThemedStyles(createStyles);
  const rotationAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const hasTriggeredHaptic = useRef(false);

  useEffect(() => {
    if (refreshing) {
      // Trigger haptic feedback when refresh starts
      if (!hasTriggeredHaptic.current) {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
        hasTriggeredHaptic.current = true;
      }

      // Start rotation animation
      Animated.loop(
        Animated.timing(rotationAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        })
      ).start();

      // Pulse animation
      Animated.loop(
        Animated.sequence([
          Animated.timing(scaleAnim, {
            toValue: 1.1,
            duration: 500,
            useNativeDriver: true,
          }),
          Animated.timing(scaleAnim, {
            toValue: 1,
            duration: 500,
            useNativeDriver: true,
          }),
        ])
      ).start();
    } else {
      // Reset animations when refresh completes
      rotationAnim.stopAnimation();
      scaleAnim.stopAnimation();
      rotationAnim.setValue(0);
      scaleAnim.setValue(1);
      hasTriggeredHaptic.current = false;

      // Trigger completion haptic
      if (hasTriggeredHaptic.current) {
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      }
    }
  }, [refreshing, rotationAnim, scaleAnim]);

  const handleRefresh = () => {
    if (!enabled) return;
    
    // Trigger immediate haptic feedback
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    onRefresh();
  };

  const rotation = rotationAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  return (
    <RefreshControl
      refreshing={refreshing}
      onRefresh={handleRefresh}
      colors={colors || ['#1E3A8A', '#3B82F6']}
      tintColor={tintColor || '#1E3A8A'}
      title={title}
      titleColor={titleColor}
      size={size === 'large' ? 1 : 0}
      progressBackgroundColor={progressBackgroundColor}
      enabled={enabled}
      // Add custom styling for enhanced visual feedback
      style={styles.refreshControl}
    />
  );
};

// Hook for managing refresh state with enhanced feedback
export const useEnhancedRefresh = (
  refreshFunction: () => Promise<void> | void,
  dependencies: any[] = []
) => {
  const [refreshing, setRefreshing] = React.useState(false);

  const onRefresh = React.useCallback(async () => {
    setRefreshing(true);
    
    try {
      await refreshFunction();
      
      // Add a minimum refresh time for better UX
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Success haptic feedback
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    } catch (error) {
      // Error haptic feedback
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
      console.error('Refresh failed:', error);
    } finally {
      setRefreshing(false);
    }
  }, [refreshFunction, ...dependencies]);

  return { refreshing, onRefresh };
};

// Component for custom pull-to-refresh indicator
export const CustomRefreshIndicator: React.FC<{
  refreshing: boolean;
  progress?: number;
}> = ({ refreshing, progress = 0 }) => {
  const styles = useThemedStyles(createStyles);
  const spinValue = useRef(new Animated.Value(0)).current;
  const scaleValue = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (refreshing) {
      // Start spinning animation
      Animated.loop(
        Animated.timing(spinValue, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        })
      ).start();

      // Scale in animation
      Animated.spring(scaleValue, {
        toValue: 1,
        useNativeDriver: true,
        tension: 300,
        friction: 10,
      }).start();
    } else {
      // Scale out animation
      Animated.timing(scaleValue, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }).start(() => {
        spinValue.setValue(0);
      });
    }
  }, [refreshing, spinValue, scaleValue]);

  const spin = spinValue.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  if (!refreshing && progress === 0) return null;

  return (
    <RTLView style={styles.customIndicatorContainer}>
      <Animated.View
        style={[
          styles.customIndicator,
          {
            transform: [
              { rotate: spin },
              { scale: scaleValue },
            ],
          },
        ]}
      >
        <RTLView style={styles.indicatorDot} />
        <RTLView style={[styles.indicatorDot, styles.indicatorDot2]} />
        <RTLView style={[styles.indicatorDot, styles.indicatorDot3]} />
      </Animated.View>
    </RTLView>
  );
};

const createStyles = (colors: ThemeColors) => StyleSheet.create({
  refreshControl: {
    // Custom styling for refresh control
  },
  customIndicatorContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: SPACING.md,
  },
  customIndicator: {
    width: 30,
    height: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  indicatorDot: {
    position: 'absolute',
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: colors.primary,
  },
  indicatorDot2: {
    transform: [{ rotate: '120deg' }, { translateY: -10 }],
    backgroundColor: colors.primary,
    opacity: 0.7,
  },
  indicatorDot3: {
    transform: [{ rotate: '240deg' }, { translateY: -10 }],
    backgroundColor: colors.primary,
    opacity: 0.4,
  },
});

// Utility function to add haptic feedback to any refresh action
export const withHapticRefresh = (refreshFunction: () => void | Promise<void>) => {
  return async () => {
    // Trigger haptic feedback before refresh
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    
    try {
      await refreshFunction();
      // Success haptic
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    } catch (error) {
      // Error haptic
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
      throw error;
    }
  };
};

// Enhanced scroll-to-top functionality with haptic feedback
export const useScrollToTop = (scrollRef: React.RefObject<any>) => {
  const scrollToTop = React.useCallback(() => {
    if (scrollRef.current) {
      // Haptic feedback
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      
      // Smooth scroll to top
      scrollRef.current.scrollToOffset?.({ offset: 0, animated: true });
      scrollRef.current.scrollTo?.({ y: 0, animated: true });
    }
  }, [scrollRef]);

  return scrollToTop;
};
