import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  Animated,
  Dimensions } from 'react-native';
import NetInfo from '@react-native-community/netinfo';
import { useThemedStyles } from '../hooks';
import { RTLView, RTLText, RTLIcon, RTLTouchableOpacity } from './RTL';
import OfflineService from '../services/OfflineService';
import {
  SPACING,
  FONT_SIZES,
  FONT_WEIGHTS,
  BORDER_RADIUS } from '../constants/theme';
import type ThemeColors  from '../contexts/ThemeContext';

interface OfflineIndicatorProps {
  style?: any;
  showPendingActions?: boolean;
}

const { width: screenWidth } = Dimensions.get('window');

export const OfflineIndicator: React.FC<OfflineIndicatorProps> = ({
  style,
  showPendingActions = true,
}) => {
  const styles = useThemedStyles(createStyles);
  const [isOnline, setIsOnline] = useState(true);
  const [pendingActionsCount, setPendingActionsCount] = useState(0);
  const [slideAnim] = useState(new Animated.Value(-100));
  const [showDetails, setShowDetails] = useState(false);

  useEffect(() => {
    // Monitor network connectivity
    const unsubscribe = NetInfo.addEventListener(state => {
      const online = state.isConnected ?? false;
      setIsOnline(online);
      
      if (online) {
        // Hide indicator when back online
        hideIndicator();
      } else {
        // Show indicator when offline
        showIndicator();
      }
    });

    // Update pending actions count
    const updatePendingCount = () => {
      setPendingActionsCount(OfflineService.getPendingActionsCount());
    };

    // Initial check
    updatePendingCount();
    
    // Update every 5 seconds
    const interval = setInterval(updatePendingCount, 5000);

    return () => {
      unsubscribe();
      clearInterval(interval);
    };
  }, []);

  const showIndicator = () => {
    Animated.spring(slideAnim, {
      toValue: 0,
      useNativeDriver: true,
      tension: 100,
      friction: 8,
    }).start();
  };

  const hideIndicator = () => {
    Animated.spring(slideAnim, {
      toValue: -100,
      useNativeDriver: true,
      tension: 100,
      friction: 8,
    }).start();
  };

  const handlePress = () => {
    setShowDetails(!showDetails);
  };

  const getStatusText = () => {
    if (isOnline) {
      return pendingActionsCount > 0 
        ? `Syncing ${pendingActionsCount} changes...`
        : 'Connected';
    }
    return 'No internet connection';
  };

  const getStatusIcon = () => {
    if (isOnline) {
      return pendingActionsCount > 0 ? 'sync-outline' : 'wifi-outline';
    }
    return 'wifi-off-outline';
  };

  const getStatusColor = () => {
    if (isOnline) {
      return pendingActionsCount > 0 ? '#FF9800' : '#4CAF50';
    }
    return '#F44336';
  };

  if (isOnline && pendingActionsCount === 0) {
    return null; // Don't show anything when online and no pending actions
  }

  return (
    <Animated.View
      style={[
        styles.container,
        { transform: [{ translateY: slideAnim }] },
        style,
      ]}
    >
      <RTLTouchableOpacity
        style={[styles.indicator, { backgroundColor: getStatusColor() }]}
        onPress={handlePress}
        activeOpacity={0.8}
      >
        <RTLView style={styles.content}>
          <RTLIcon
            name={getStatusIcon() as any}
            size={16}
            color="#FFFFFF"
          />
          <RTLText style={styles.statusText}>{getStatusText()}</RTLText>
          {showPendingActions && pendingActionsCount > 0 && (
            <RTLView style={styles.badge}>
              <RTLText style={styles.badgeText}>{pendingActionsCount}</RTLText>
            </RTLView>
          )}
          <RTLIcon
            name={showDetails ? "chevron-up" : "chevron-down"}
            size={16}
            color="#FFFFFF"
          />
        </RTLView>
      </RTLTouchableOpacity>

      {showDetails && (
        <RTLView style={styles.details}>
          <RTLView style={styles.detailRow}>
            <RTLIcon name="information-circle-outline" size={16} color="#666" />
            <RTLText style={styles.detailText}>
              {isOnline
                ? 'You are connected to the internet'
                : 'You are currently offline. Changes will be saved and synced when you reconnect.'
              }
            </RTLText>
          </RTLView>

          {pendingActionsCount > 0 && (
            <RTLView style={styles.detailRow}>
              <RTLIcon name="time-outline" size={16} color="#666" />
              <RTLText style={styles.detailText}>
                {pendingActionsCount} change{pendingActionsCount !== 1 ? 's' : ''} waiting to sync
              </RTLText>
            </RTLView>
          )}

          {!isOnline && (
            <RTLView style={styles.detailRow}>
              <RTLIcon name="shield-checkmark-outline" size={16} color="#666" />
              <RTLText style={styles.detailText}>
                You can still browse cached content and make changes
              </RTLText>
            </RTLView>
          )}
        </RTLView>
      )}
    </Animated.View>
  );
};

const createStyles = (colors: ThemeColors) => StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 1000,
    elevation: 1000,
  },
  indicator: {
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    marginHorizontal: SPACING.md,
    marginTop: SPACING.sm,
    borderRadius: BORDER_RADIUS.md,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  content: {
    flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
    alignItems: 'center',
    justifyContent: 'center',
    gap: SPACING.sm,
  },
  statusText: {
    fontSize: FONT_SIZES.sm,
    fontWeight: FONT_WEIGHTS.medium,
    color: '#FFFFFF',
    flex: 1,
    textAlign: 'center',
  },
  badge: {
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: BORDER_RADIUS.full,
    paddingHorizontal: SPACING.xs,
    paddingVertical: 2,
    minWidth: 20,
    alignItems: 'center',
  },
  badgeText: {
    fontSize: FONT_SIZES.xs,
    fontWeight: FONT_WEIGHTS.bold,
    color: '#FFFFFF',
  },
  details: {
    backgroundColor: colors.surface,
    marginHorizontal: SPACING.md,
    marginTop: SPACING.xs,
    borderRadius: BORDER_RADIUS.md,
    padding: SPACING.md,
    borderWidth: 1,
    borderColor: colors.border,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 3,
  },
  detailRow: {
    flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
    alignItems: 'flex-start',
    marginBottom: SPACING.sm,
    gap: SPACING.sm,
  },
  detailText: {
    fontSize: FONT_SIZES.sm,
    color: colors.textSecondary,
    flex: 1,
    lineHeight: 18,
  },
});
