import React, { useState } from 'react';
import { StyleSheet, Alert, Image, Dimensions } from 'react-native';
import { RTLView, RTLText, RTLTouchableOpacity, RTLIcon, RTLScrollView } from '../RTL';
import * as ImagePickerExpo from 'expo-image-picker';
import { useThemedStyles } from '../../hooks';
import { Button } from '../Button';

import {
  SPACING,
  FONT_SIZES,
  FONT_WEIGHTS,
  BORDER_RADIUS } from '../../constants/theme';
import type ThemeColors  from '../../contexts/ThemeContext';

const { width: screenWidth } = Dimensions.get('window');
const IMAGE_SIZE = (screenWidth - SPACING.lg * 2 - SPACING.md * 2) / 3;

export interface ImagePickerProps {
  images: string[];
  onImagesChange: (images: string[]) => void;
  maxImages?: number;
  title?: string;
  subtitle?: string;
  style?: any;
}

export const ImagePicker: React.FC<ImagePickerProps> = ({
  images,
  onImagesChange,
  maxImages = 5,
  title = "Product Images",
  subtitle = "Add up to 5 images",
  style,
}) => {
  const styles = useThemedStyles(createStyles);
  const [isLoading, setIsLoading] = useState(false);

  const requestPermissions = async () => {
    const { status: cameraStatus } = await ImagePickerExpo.requestCameraPermissionsAsync();
    const { status: mediaStatus } = await ImagePickerExpo.requestMediaLibraryPermissionsAsync();
    
    if (cameraStatus !== 'granted' || mediaStatus !== 'granted') {
      Alert.alert(
        'Permissions Required',
        'Please grant camera and photo library permissions to upload images.',
        [{ text: 'OK' }]
      );
      return false;
    }
    return true;
  };

  const showImagePickerOptions = () => {
    Alert.alert(
      'Select Image',
      'Choose how you want to add an image',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Camera', onPress: openCamera },
        { text: 'Photo Library', onPress: openImageLibrary },
      ]
    );
  };

  const openCamera = async () => {
    const hasPermission = await requestPermissions();
    if (!hasPermission) return;

    setIsLoading(true);
    try {
      const result = await ImagePickerExpo.launchCameraAsync({
        mediaTypes: ImagePickerExpo.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        const newImages = [...images, result.assets[0].uri];
        onImagesChange(newImages);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to take photo. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const openImageLibrary = async () => {
    const hasPermission = await requestPermissions();
    if (!hasPermission) return;

    setIsLoading(true);
    try {
      const result = await ImagePickerExpo.launchImageLibraryAsync({
        mediaTypes: ImagePickerExpo.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
        allowsMultipleSelection: true,
        selectionLimit: maxImages - images.length,
      });

      if (!result.canceled && result.assets.length > 0) {
        const newImageUris = result.assets.map(asset => asset.uri);
        const updatedImages = [...images, ...newImageUris].slice(0, maxImages);
        onImagesChange(updatedImages);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to select images. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const removeImage = (index: number) => {
    Alert.alert(
      'Remove Image',
      'Are you sure you want to remove this image?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Remove',
          style: 'destructive',
          onPress: () => {
            const newImages = images.filter((_, i) => i !== index);
            onImagesChange(newImages);
          },
        },
      ]
    );
  };

  const reorderImages = (fromIndex: number, toIndex: number) => {
    const newImages = [...images];
    const [movedImage] = newImages.splice(fromIndex, 1);
    newImages.splice(toIndex, 0, movedImage);
    onImagesChange(newImages);
  };

  const renderImageItem = (uri: string, index: number) => (
    <RTLView key={index} style={styles.imageContainer}>
      <Image source={{ uri }} style={styles.image} />
      
      {/* Primary Badge */}
      {index === 0 && (
        <RTLView style={styles.primaryBadge}>
          <RTLText style={styles.primaryText}>Primary</RTLText>
        </RTLView>
      )}

      {/* Remove Button */}
      <RTLTouchableOpacity
        style={styles.removeButton}
        onPress={() => removeImage(index)}
      >
        <RTLIcon name="close-circle" size={24} color="#FF6B6B" />
      </RTLTouchableOpacity>

      {/* Reorder Buttons */}
      {images.length > 1 && (
        <RTLView style={styles.reorderButtons}>
          {index > 0 && (
            <RTLTouchableOpacity
              style={styles.reorderButton}
              onPress={() => reorderImages(index, index - 1)}
            >
              <RTLIcon name="chevron-up" size={16} color="#FFFFFF" />
            </RTLTouchableOpacity>
          )}
          {index < images.length - 1 && (
            <RTLTouchableOpacity
              style={styles.reorderButton}
              onPress={() => reorderImages(index, index + 1)}
            >
              <RTLIcon name="chevron-down" size={16} color="#FFFFFF" />
            </RTLTouchableOpacity>
          )}
        </RTLView>
      )}
    </RTLView>
  );

  const renderAddButton = () => (
    <RTLTouchableOpacity
      style={styles.addButton}
      onPress={showImagePickerOptions}
      disabled={isLoading || images.length >= maxImages}
    >
      <RTLIcon
        name="add"
        size={32}
        color={images.length >= maxImages ? "#CCCCCC" : "#667eea"}
      />
      <RTLText style={[
        styles.addButtonText,
        images.length >= maxImages && styles.addButtonTextDisabled
      ]}>
        Add Image
      </RTLText>
    </RTLTouchableOpacity>
  );

  return (
    <RTLView style={[styles.container, style]}>
      {/* Header */}
      <RTLView style={styles.header}>
        <RTLText style={styles.title}>{title}</RTLText>
        <RTLText style={styles.subtitle}>
          {subtitle} ({images.length}/{maxImages})
        </RTLText>
      </RTLView>

      {/* Images Grid */}
      <RTLScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.imagesContainer}
        enableRTLScrolling={true}
      >
        {images.map((uri, index) => renderImageItem(uri, index))}

        {images.length < maxImages && renderAddButton()}
      </RTLScrollView>

      {/* Helper Text */}
      {images.length === 0 && (
        <RTLView style={styles.helperContainer}>
          <RTLIcon name="camera-outline" size={48} color="#CCCCCC" />
          <RTLText style={styles.helperText}>
            Tap "Add Image" to upload product photos
          </RTLText>
          <RTLText style={styles.helperSubtext}>
            The first image will be used as the primary product image
          </RTLText>
        </RTLView>
      )}

      {/* Bulk Actions */}
      {images.length > 0 && (
        <RTLView style={styles.actionsContainer}>
          <Button
            title="Clear All"
            onPress={() => {
              Alert.alert(
                'Clear All Images',
                'Are you sure you want to remove all images?',
                [
                  { text: 'Cancel', style: 'cancel' },
                  {
                    text: 'Clear All',
                    style: 'destructive',
                    onPress: () => onImagesChange([]),
                  },
                ]
              );
            }}
            variant="outline"
            size="small"
            style={styles.clearButton}
          />
          
          <RTLText style={styles.tipText}>
            💡 Tip: Drag images to reorder them
          </RTLText>
        </RTLView>
      )}
    </RTLView>
  );
};

const createStyles = (colors: ThemeColors) =>
  StyleSheet.create({
    container: {
      marginBottom: SPACING.md,
    },
    header: {
      marginBottom: SPACING.md,
    },
    title: {
      fontSize: FONT_SIZES.md,
      fontWeight: FONT_WEIGHTS.medium,
      color: colors.textPrimary,
      marginBottom: SPACING.xs,
    },
    subtitle: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
    },
    imagesContainer: {
      paddingHorizontal: SPACING.xs,
    },
    imageContainer: {
      width: IMAGE_SIZE,
      height: IMAGE_SIZE,
      marginHorizontal: SPACING.xs,
      position: 'relative',
      borderRadius: BORDER_RADIUS.md,
      overflow: 'hidden',
    },
    image: {
      width: '100%',
      height: '100%',
      borderRadius: BORDER_RADIUS.md,
    },
    primaryBadge: {
      position: 'absolute',
      top: SPACING.xs,
      left: SPACING.xs,
      backgroundColor: '#4CAF50',
      paddingHorizontal: SPACING.xs,
      paddingVertical: 2,
      borderRadius: BORDER_RADIUS.sm,
    },
    primaryText: {
      fontSize: FONT_SIZES.xs,
      fontWeight: FONT_WEIGHTS.bold,
      color: '#FFFFFF',
    },
    removeButton: {
      position: 'absolute',
      top: SPACING.xs,
      right: SPACING.xs,
      backgroundColor: 'rgba(255, 255, 255, 0.9)',
      borderRadius: 12,
    },
    reorderButtons: {
      position: 'absolute',
      bottom: SPACING.xs,
      right: SPACING.xs,
      flexDirection: 'column',
    },
    reorderButton: {
      backgroundColor: 'rgba(0, 0, 0, 0.7)',
      borderRadius: 12,
      padding: 2,
      marginVertical: 1,
    },
    addButton: {
      width: IMAGE_SIZE,
      height: IMAGE_SIZE,
      marginHorizontal: SPACING.xs,
      borderWidth: 2,
      borderColor: colors.border,
      borderStyle: 'dashed',
      borderRadius: BORDER_RADIUS.md,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: colors.surface,
    },
    addButtonText: {
      fontSize: FONT_SIZES.sm,
      color: '#667eea',
      fontWeight: FONT_WEIGHTS.medium,
      marginTop: SPACING.xs,
    },
    addButtonTextDisabled: {
      color: '#CCCCCC',
    },
    helperContainer: {
      alignItems: 'center',
      paddingVertical: SPACING.xl,
    },
    helperText: {
      fontSize: FONT_SIZES.md,
      color: colors.textSecondary,
      textAlign: 'center',
      marginTop: SPACING.sm,
      marginBottom: SPACING.xs,
    },
    helperSubtext: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
      textAlign: 'center',
      opacity: 0.8,
    },
    actionsContainer: {
      flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
      justifyContent: 'space-between',
      alignItems: 'center',
      marginTop: SPACING.md,
    },
    clearButton: {
      minWidth: 80,
    },
    tipText: {
      fontSize: FONT_SIZES.xs,
      color: colors.textSecondary,
      fontStyle: 'italic',
    },
  });
