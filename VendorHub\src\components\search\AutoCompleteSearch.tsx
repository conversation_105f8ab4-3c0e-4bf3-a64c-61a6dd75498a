import React, { useState, useRef, useEffect } from 'react';
import { StyleSheet, Animated, Keyboard } from 'react-native';
import { RTLView, RTLText, RTLTouchableOpacity, RTLIcon, RTLInput } from '../RTL';
import { useThemedStyles, useVendors, useProducts, useI18n } from '../../hooks';
import { SPACING, FONT_SIZES, FONT_WEIGHTS, BORDER_RADIUS } from '../../constants/theme';
import { FadeInView, AnimatedPressable } from '../visual/MicroAnimations';
import { GlassmorphismCard } from '../visual/GlassmorphismCard';
import type { ThemeColors } from '../../contexts/ThemeContext';

interface AutoCompleteSuggestion {
  id: string;
  text: string;
  type: 'vendor' | 'product' | 'category' | 'completion';
  icon: string;
  data?: any;
  score: number;
}

interface AutoCompleteSearchProps {
  onSearch: (query: string) => void;
  onSuggestionSelect: (suggestion: AutoCompleteSuggestion) => void;
  placeholder?: string;
  maxSuggestions?: number;
  minQueryLength?: number;
  debounceMs?: number;
  style?: any;
}

export const AutoCompleteSearch: React.FC<AutoCompleteSearchProps> = ({
  onSearch,
  onSuggestionSelect,
  placeholder,
  maxSuggestions = 6,
  minQueryLength = 2,
  debounceMs = 300,
  style,
}) => {
  const styles = useThemedStyles(createStyles);
  const { getApprovedVendors } = useVendors();
  const { getAllProducts } = useProducts();
  const { t } = useI18n();

  const [query, setQuery] = useState('');
  const [suggestions, setSuggestions] = useState<AutoCompleteSuggestion[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);

  const inputRef = useRef<any>(null);
  const debounceTimer = useRef<NodeJS.Timeout>();
  const suggestionHeight = useRef(new Animated.Value(0)).current;

  const vendors = getApprovedVendors();
  const products = getAllProducts();

  // Fuzzy search algorithm
  const fuzzyMatch = (text: string, query: string): number => {
    const textLower = text.toLowerCase();
    const queryLower = query.toLowerCase();
    
    // Exact match gets highest score
    if (textLower === queryLower) return 100;
    
    // Starts with query gets high score
    if (textLower.startsWith(queryLower)) return 90;
    
    // Contains query gets medium score
    if (textLower.includes(queryLower)) return 70;
    
    // Fuzzy matching for typos
    let score = 0;
    let queryIndex = 0;
    
    for (let i = 0; i < textLower.length && queryIndex < queryLower.length; i++) {
      if (textLower[i] === queryLower[queryIndex]) {
        score += 10;
        queryIndex++;
      }
    }
    
    // Bonus for matching all characters
    if (queryIndex === queryLower.length) {
      score += 20;
    }
    
    return Math.min(score, 60);
  };

  // Generate auto-complete suggestions
  const generateSuggestions = (searchQuery: string): AutoCompleteSuggestion[] => {
    if (searchQuery.length < minQueryLength) return [];

    const suggestions: AutoCompleteSuggestion[] = [];
    const query = searchQuery.toLowerCase().trim();

    // Vendor suggestions
    vendors.forEach(vendor => {
      const nameScore = fuzzyMatch(vendor.businessName, query);
      const descScore = vendor.businessDescription ? 
        fuzzyMatch(vendor.businessDescription, query) * 0.7 : 0;
      
      const maxScore = Math.max(nameScore, descScore);
      if (maxScore > 30) {
        suggestions.push({
          id: `vendor-${vendor.id}`,
          text: vendor.businessName,
          type: 'vendor',
          icon: 'storefront',
          data: vendor,
          score: maxScore + (vendor.rating * 5), // Boost by rating
        });
      }
    });

    // Product suggestions
    products.forEach(product => {
      const nameScore = fuzzyMatch(product.name, query);
      const descScore = product.description ? 
        fuzzyMatch(product.description, query) * 0.6 : 0;
      
      const maxScore = Math.max(nameScore, descScore);
      if (maxScore > 30) {
        suggestions.push({
          id: `product-${product.id}`,
          text: product.name,
          type: 'product',
          icon: 'cube-outline',
          data: product,
          score: maxScore + (product.isActive ? 10 : 0),
        });
      }
    });

    // Category suggestions
    const categories = [...new Set(products.map(p => p.category).filter(Boolean))];
    categories.forEach(category => {
      const score = fuzzyMatch(category, query);
      if (score > 40) {
        const categoryProducts = products.filter(p => p.category === category);
        suggestions.push({
          id: `category-${category}`,
          text: category,
          type: 'category',
          icon: 'grid-outline',
          data: { category, productCount: categoryProducts.length },
          score: score + categoryProducts.length,
        });
      }
    });

    // Auto-completion suggestions (complete the word)
    const words = query.split(' ');
    const lastWord = words[words.length - 1];
    
    if (lastWord.length >= 2) {
      const completions = new Set<string>();
      
      // Find completions from vendor names
      vendors.forEach(vendor => {
        const words = vendor.businessName.toLowerCase().split(' ');
        words.forEach(word => {
          if (word.startsWith(lastWord) && word.length > lastWord.length) {
            completions.add(word);
          }
        });
      });
      
      // Find completions from product names
      products.forEach(product => {
        const words = product.name.toLowerCase().split(' ');
        words.forEach(word => {
          if (word.startsWith(lastWord) && word.length > lastWord.length) {
            completions.add(word);
          }
        });
      });
      
      Array.from(completions).slice(0, 3).forEach(completion => {
        const newQuery = [...words.slice(0, -1), completion].join(' ');
        suggestions.push({
          id: `completion-${completion}`,
          text: newQuery,
          type: 'completion',
          icon: 'text',
          score: 50,
        });
      });
    }

    // Sort by score and limit results
    return suggestions
      .sort((a, b) => b.score - a.score)
      .slice(0, maxSuggestions);
  };

  // Debounced suggestion generation
  useEffect(() => {
    if (debounceTimer.current) {
      clearTimeout(debounceTimer.current);
    }

    if (query.length >= minQueryLength) {
      setIsLoading(true);
      debounceTimer.current = setTimeout(() => {
        const newSuggestions = generateSuggestions(query);
        setSuggestions(newSuggestions);
        setIsLoading(false);
        setSelectedIndex(-1);
      }, debounceMs);
    } else {
      setSuggestions([]);
      setIsLoading(false);
      setSelectedIndex(-1);
    }

    return () => {
      if (debounceTimer.current) {
        clearTimeout(debounceTimer.current);
      }
    };
  }, [query]);

  // Animate suggestions
  useEffect(() => {
    Animated.spring(suggestionHeight, {
      toValue: showSuggestions && suggestions.length > 0 ? suggestions.length * 50 : 0,
      useNativeDriver: false,
      tension: 100,
      friction: 8,
    }).start();
  }, [showSuggestions, suggestions.length]);

  const handleQueryChange = (text: string) => {
    setQuery(text);
    setShowSuggestions(true);
  };

  const handleSuggestionPress = (suggestion: AutoCompleteSuggestion) => {
    setQuery(suggestion.text);
    setShowSuggestions(false);
    onSuggestionSelect(suggestion);
    Keyboard.dismiss();
  };

  const handleSubmit = () => {
    if (query.trim()) {
      onSearch(query.trim());
      setShowSuggestions(false);
      Keyboard.dismiss();
    }
  };

  const handleKeyPress = (event: any) => {
    if (!showSuggestions || suggestions.length === 0) return;

    switch (event.nativeEvent.key) {
      case 'ArrowDown':
        event.preventDefault();
        setSelectedIndex(prev => 
          prev < suggestions.length - 1 ? prev + 1 : prev
        );
        break;
      case 'ArrowUp':
        event.preventDefault();
        setSelectedIndex(prev => prev > 0 ? prev - 1 : -1);
        break;
      case 'Enter':
        event.preventDefault();
        if (selectedIndex >= 0) {
          handleSuggestionPress(suggestions[selectedIndex]);
        } else {
          handleSubmit();
        }
        break;
      case 'Escape':
        setShowSuggestions(false);
        setSelectedIndex(-1);
        break;
    }
  };

  const highlightMatch = (text: string, query: string) => {
    if (!query) return <RTLText>{text}</RTLText>;

    const regex = new RegExp(`(${query})`, 'gi');
    const parts = text.split(regex);

    return parts.map((part, index) =>
      regex.test(part) ? (
        <RTLText key={index} style={styles.highlightedText}>{part}</RTLText>
      ) : (
        <RTLText key={index}>{part}</RTLText>
      )
    );
  };

  const renderSuggestion = (suggestion: AutoCompleteSuggestion, index: number) => (
    <FadeInView key={suggestion.id} duration={200} delay={index * 50}>
      <AnimatedPressable
        style={[
          styles.suggestionItem,
          selectedIndex === index && styles.suggestionItemSelected,
        ]}
        onPress={() => handleSuggestionPress(suggestion)}
      >
        <RTLView style={styles.suggestionContent}>
          <RTLView style={[styles.suggestionIcon, { backgroundColor: getSuggestionColor(suggestion.type) }]}>
            <RTLIcon name={suggestion.icon} size={16} color="#FFFFFF" />
          </RTLView>
          
          <RTLView style={styles.suggestionText}>
            <RTLText style={styles.suggestionTitle}>
              {highlightMatch(suggestion.text, query)}
            </RTLText>
            {suggestion.type === 'product' && suggestion.data && suggestion.data.price && (
              <RTLText style={styles.suggestionSubtitle}>
                BHD {suggestion.data.price.toFixed(2)}
              </RTLText>
            )}
            {suggestion.type === 'category' && suggestion.data && (
              <RTLText style={styles.suggestionSubtitle}>
                {suggestion.data.productCount} {t('search.products')}
              </RTLText>
            )}
          </RTLView>
          
          <RTLIcon name="arrow-forward" size={14} color="rgba(255, 255, 255, 0.5)" />
        </RTLView>
      </AnimatedPressable>
    </FadeInView>
  );

  const getSuggestionColor = (type: string) => {
    switch (type) {
      case 'vendor': return '#3B82F6';
      case 'product': return '#10B981';
      case 'category': return '#F59E0B';
      case 'completion': return '#8B5CF6';
      default: return '#6B7280';
    }
  };

  return (
    <RTLView style={[styles.container, style]}>
      {/* Search Input */}
      <GlassmorphismCard intensity="medium" style={styles.inputCard}>
        <RTLView style={styles.inputContainer}>
          <RTLIcon name="search" size={20} color="rgba(255, 255, 255, 0.7)" />
          
          <RTLInput
            ref={inputRef}
            style={styles.input}
            placeholder={placeholder || t('search.searchPlaceholder')}
            placeholderTextColor="rgba(255, 255, 255, 0.5)"
            value={query}
            onChangeText={handleQueryChange}
            onSubmitEditing={handleSubmit}
            onFocus={() => setShowSuggestions(true)}
            onKeyPress={handleKeyPress}
            returnKeyType="search"
            autoCorrect={false}
            autoCapitalize="none"
          />
          
          {query.length > 0 && (
            <RTLTouchableOpacity
              style={styles.clearButton}
              onPress={() => {
                setQuery('');
                setShowSuggestions(false);
                inputRef.current?.focus();
              }}
            >
              <RTLIcon name="close-circle" size={20} color="rgba(255, 255, 255, 0.5)" />
            </RTLTouchableOpacity>
          )}
        </RTLView>
      </GlassmorphismCard>

      {/* Suggestions */}
      {showSuggestions && (
        <Animated.View style={[styles.suggestionsContainer, { height: suggestionHeight }]}>
          <GlassmorphismCard intensity="strong" style={styles.suggestionsCard}>
            {isLoading ? (
              <RTLView style={styles.loadingContainer}>
                <RTLText style={styles.loadingText}>{t('search.searching')}</RTLText>
              </RTLView>
            ) : (
              suggestions.map((suggestion, index) => renderSuggestion(suggestion, index))
            )}
          </GlassmorphismCard>
        </Animated.View>
      )}
    </RTLView>
  );
};

const createStyles = (colors: ThemeColors) => StyleSheet.create({
  container: {
    zIndex: 1000,
  },
  inputCard: {
    marginBottom: SPACING.sm,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.md,
  },
  input: {
    flex: 1,
    fontSize: FONT_SIZES.md,
    color: '#FFFFFF',
    marginHorizontal: SPACING.md,
    fontWeight: FONT_WEIGHTS.medium,
  },
  clearButton: {
    padding: SPACING.xs,
  },

  // Suggestions
  suggestionsContainer: {
    overflow: 'hidden',
    maxHeight: 300,
  },
  suggestionsCard: {
    flex: 1,
  },
  suggestionItem: {
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.md,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
  },
  suggestionItemSelected: {
    backgroundColor: 'rgba(59, 130, 246, 0.2)',
  },
  suggestionContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  suggestionIcon: {
    width: 28,
    height: 28,
    borderRadius: 14,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: SPACING.md,
  },
  suggestionText: {
    flex: 1,
  },
  suggestionTitle: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.medium,
    color: '#FFFFFF',
    marginBottom: SPACING.xs,
  },
  suggestionSubtitle: {
    fontSize: FONT_SIZES.sm,
    color: 'rgba(255, 255, 255, 0.7)',
  },
  highlightedText: {
    fontWeight: FONT_WEIGHTS.bold,
    color: '#3B82F6',
  },
  loadingContainer: {
    padding: SPACING.lg,
    alignItems: 'center',
  },
  loadingText: {
    fontSize: FONT_SIZES.md,
    color: 'rgba(255, 255, 255, 0.7)',
    fontWeight: FONT_WEIGHTS.medium,
  },
});
