import React, { useRef, useState, useCallback, useMemo } from 'react';
import { StyleSheet, Animated, Dimensions, InteractionManager, Platform } from 'react-native';
import { RTLView, RTLScrollView } from '../RTL';
import { useThemedStyles } from '../../hooks';
import type { ThemeColors } from '../../contexts/ThemeContext';

const { height: screenHeight } = Dimensions.get('window');
const SCROLL_THRESHOLD = 16; // 60fps target
const MOMENTUM_THRESHOLD = 0.5;
const DECELERATION_RATE = Platform.OS === 'ios' ? 0.998 : 0.985;

interface UltraSmoothScrollViewProps {
  children: React.ReactNode;
  onScroll?: (event: any) => void;
  onMomentumScrollBegin?: () => void;
  onMomentumScrollEnd?: () => void;
  onScrollBeginDrag?: () => void;
  onScrollEndDrag?: () => void;
  refreshControl?: React.ReactElement;
  showsVerticalScrollIndicator?: boolean;
  contentContainerStyle?: any;
  style?: any;
  enableVirtualization?: boolean;
  itemHeight?: number;
  windowSize?: number;
  maxToRenderPerBatch?: number;
  updateCellsBatchingPeriod?: number;
  removeClippedSubviews?: boolean;
  scrollEventThrottle?: number;
  bounces?: boolean;
  bouncesZoom?: boolean;
  alwaysBounceVertical?: boolean;
  decelerationRate?: number;
  enableMomentumScrolling?: boolean;
  snapToInterval?: number;
  snapToAlignment?: 'start' | 'center' | 'end';
  pagingEnabled?: boolean;
}

export const UltraSmoothScrollView: React.FC<UltraSmoothScrollViewProps> = ({
  children,
  onScroll,
  onMomentumScrollBegin,
  onMomentumScrollEnd,
  onScrollBeginDrag,
  onScrollEndDrag,
  refreshControl,
  showsVerticalScrollIndicator = false,
  contentContainerStyle,
  style,
  enableVirtualization = false,
  itemHeight = 100,
  windowSize = 10,
  maxToRenderPerBatch = 10,
  updateCellsBatchingPeriod = 50,
  removeClippedSubviews = true,
  scrollEventThrottle = SCROLL_THRESHOLD,
  bounces = true,
  bouncesZoom = false,
  alwaysBounceVertical = true,
  decelerationRate = DECELERATION_RATE,
  enableMomentumScrolling = true,
  snapToInterval,
  snapToAlignment = 'start',
  pagingEnabled = false,
}) => {
  const styles = useThemedStyles(createStyles);
  
  const scrollViewRef = useRef<any>(null);
  const scrollY = useRef(new Animated.Value(0)).current;
  const [isScrolling, setIsScrolling] = useState(false);
  const [scrollVelocity, setScrollVelocity] = useState(0);
  const [contentHeight, setContentHeight] = useState(0);
  const [scrollViewHeight, setScrollViewHeight] = useState(0);
  
  // Performance tracking
  const lastScrollTime = useRef(Date.now());
  const lastScrollOffset = useRef(0);
  const frameDropCount = useRef(0);
  const scrollMetrics = useRef({
    averageFPS: 60,
    frameDrops: 0,
    totalFrames: 0,
  });

  // Optimized scroll handler with performance monitoring
  const handleScroll = useCallback((event: any) => {
    const currentTime = Date.now();
    const currentOffset = event.nativeEvent.contentOffset.y;
    const timeDelta = currentTime - lastScrollTime.current;
    const offsetDelta = currentOffset - lastScrollOffset.current;
    
    // Calculate scroll velocity
    const velocity = timeDelta > 0 ? Math.abs(offsetDelta / timeDelta) : 0;
    setScrollVelocity(velocity);
    
    // Performance monitoring
    if (timeDelta > 0) {
      const fps = 1000 / timeDelta;
      scrollMetrics.current.totalFrames++;
      
      if (fps < 55) { // Frame drop detection
        scrollMetrics.current.frameDrops++;
        frameDropCount.current++;
      }
      
      // Update average FPS
      scrollMetrics.current.averageFPS = 
        (scrollMetrics.current.averageFPS * 0.9) + (fps * 0.1);
    }
    
    lastScrollTime.current = currentTime;
    lastScrollOffset.current = currentOffset;
    
    // Call external scroll handler
    onScroll?.(event);
  }, [onScroll]);

  // Optimized momentum handlers
  const handleMomentumScrollBegin = useCallback(() => {
    setIsScrolling(true);
    onMomentumScrollBegin?.();
    
    // Reduce background processing during momentum scrolling
    InteractionManager.runAfterInteractions(() => {
      // Defer non-critical operations
    });
  }, [onMomentumScrollBegin]);

  const handleMomentumScrollEnd = useCallback(() => {
    setIsScrolling(false);
    onMomentumScrollEnd?.();
    
    // Resume normal processing after scrolling ends
    InteractionManager.runAfterInteractions(() => {
      // Resume deferred operations
    });
  }, [onMomentumScrollEnd]);

  const handleScrollBeginDrag = useCallback(() => {
    setIsScrolling(true);
    onScrollBeginDrag?.();
  }, [onScrollBeginDrag]);

  const handleScrollEndDrag = useCallback(() => {
    // Don't set isScrolling to false here if momentum is enabled
    // as momentum scrolling might continue
    if (!enableMomentumScrolling) {
      setIsScrolling(false);
    }
    onScrollEndDrag?.();
  }, [onScrollEndDrag, enableMomentumScrolling]);

  // Content size change handler
  const handleContentSizeChange = useCallback((width: number, height: number) => {
    setContentHeight(height);
  }, []);

  // Layout handler
  const handleLayout = useCallback((event: any) => {
    setScrollViewHeight(event.nativeEvent.layout.height);
  }, []);

  // Virtualization logic for large lists
  const virtualizedChildren = useMemo(() => {
    if (!enableVirtualization || !Array.isArray(children)) {
      return children;
    }

    const scrollOffset = scrollY._value || 0;
    const visibleStart = Math.max(0, Math.floor(scrollOffset / itemHeight) - windowSize);
    const visibleEnd = Math.min(
      children.length,
      Math.ceil((scrollOffset + scrollViewHeight) / itemHeight) + windowSize
    );

    return children.slice(visibleStart, visibleEnd).map((child, index) => (
      <RTLView key={visibleStart + index} style={{ height: itemHeight }}>
        {child}
      </RTLView>
    ));
  }, [children, enableVirtualization, itemHeight, windowSize, scrollViewHeight, scrollY._value]);

  // Optimized scroll event configuration
  const scrollEventConfig = useMemo(() => ({
    listener: handleScroll,
    useNativeDriver: true,
  }), [handleScroll]);

  // Performance-optimized props
  const optimizedProps = useMemo(() => ({
    removeClippedSubviews: removeClippedSubviews && Platform.OS === 'android',
    maxToRenderPerBatch,
    updateCellsBatchingPeriod,
    windowSize: enableVirtualization ? windowSize : undefined,
    scrollEventThrottle,
    bounces,
    bouncesZoom,
    alwaysBounceVertical,
    decelerationRate,
    snapToInterval,
    snapToAlignment,
    pagingEnabled,
    showsVerticalScrollIndicator,
  }), [
    removeClippedSubviews,
    maxToRenderPerBatch,
    updateCellsBatchingPeriod,
    enableVirtualization,
    windowSize,
    scrollEventThrottle,
    bounces,
    bouncesZoom,
    alwaysBounceVertical,
    decelerationRate,
    snapToInterval,
    snapToAlignment,
    pagingEnabled,
    showsVerticalScrollIndicator,
  ]);

  // Performance indicator (development only)
  const renderPerformanceIndicator = () => {
    if (__DEV__ && scrollMetrics.current.totalFrames > 100) {
      const frameDropPercentage = (scrollMetrics.current.frameDrops / scrollMetrics.current.totalFrames) * 100;
      const performanceColor = frameDropPercentage < 5 ? '#10B981' : 
                              frameDropPercentage < 15 ? '#F59E0B' : '#EF4444';
      
      return (
        <RTLView style={[styles.performanceIndicator, { backgroundColor: performanceColor }]}>
          <RTLView style={styles.performanceText}>
            FPS: {Math.round(scrollMetrics.current.averageFPS)}
          </RTLView>
        </RTLView>
      );
    }
    return null;
  };

  return (
    <RTLView style={[styles.container, style]}>
      <Animated.ScrollView
        ref={scrollViewRef}
        style={styles.scrollView}
        contentContainerStyle={[styles.contentContainer, contentContainerStyle]}
        onScroll={Animated.event(
          [{ nativeEvent: { contentOffset: { y: scrollY } } }],
          scrollEventConfig
        )}
        onMomentumScrollBegin={handleMomentumScrollBegin}
        onMomentumScrollEnd={handleMomentumScrollEnd}
        onScrollBeginDrag={handleScrollBeginDrag}
        onScrollEndDrag={handleScrollEndDrag}
        onContentSizeChange={handleContentSizeChange}
        onLayout={handleLayout}
        refreshControl={refreshControl}
        {...optimizedProps}
      >
        {virtualizedChildren}
      </Animated.ScrollView>
      
      {/* Performance indicator for development */}
      {renderPerformanceIndicator()}
      
      {/* Scroll velocity indicator */}
      {__DEV__ && isScrolling && (
        <RTLView style={styles.velocityIndicator}>
          <RTLView style={styles.velocityText}>
            Velocity: {scrollVelocity.toFixed(2)}
          </RTLView>
        </RTLView>
      )}
    </RTLView>
  );
};

const createStyles = (colors: ThemeColors) => StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  contentContainer: {
    flexGrow: 1,
  },
  performanceIndicator: {
    position: 'absolute',
    top: 50,
    right: 10,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    opacity: 0.8,
  },
  performanceText: {
    fontSize: 10,
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  velocityIndicator: {
    position: 'absolute',
    top: 80,
    right: 10,
    backgroundColor: 'rgba(59, 130, 246, 0.8)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  velocityText: {
    fontSize: 10,
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
});
