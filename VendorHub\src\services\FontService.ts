import { Platform } from 'react-native';
import { useFonts } from 'expo-font';
import { SupportedLanguage } from './I18nService';

export interface FontConfig {
  regular: string;
  medium: string;
  bold: string;
  light?: string;
}

export interface FontFamily {
  en: FontConfig;
  ar: FontConfig;
}

// Font configurations for different languages
export const FONT_FAMILIES: FontFamily = {
  en: {
    regular: Platform.select({
      ios: 'System',
      android: 'Roboto',
      web: 'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
    }) as string,
    medium: Platform.select({
      ios: 'System',
      android: 'Roboto-Medium',
      web: 'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
    }) as string,
    bold: Platform.select({
      ios: 'System',
      android: 'Roboto-Bold',
      web: 'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
    }) as string,
    light: Platform.select({
      ios: 'System',
      android: 'Roboto-Light',
      web: 'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
    }) as string,
  },
  ar: {
    // Enhanced Arabic fonts with better fallbacks
    regular: Platform.select({
      ios: 'Geeza Pro',
      android: 'Noto Sans Arabic',
      web: '"Tajawal", "Cairo", "Noto Sans Arabic", "Segoe UI Arabic", "Tahoma", "Arabic Typesetting", "Geeza Pro", "Traditional Arabic", "Times New Roman", serif',
    }) as string,
    medium: Platform.select({
      ios: 'Geeza Pro',
      android: 'Noto Sans Arabic Medium',
      web: '"Tajawal", "Cairo", "Noto Sans Arabic", "Segoe UI Arabic", "Tahoma", "Arabic Typesetting", "Geeza Pro", "Traditional Arabic", "Times New Roman", serif',
    }) as string,
    bold: Platform.select({
      ios: 'Geeza Pro Bold',
      android: 'Noto Sans Arabic Bold',
      web: '"Tajawal", "Cairo", "Noto Sans Arabic", "Segoe UI Arabic", "Tahoma", "Arabic Typesetting", "Geeza Pro", "Traditional Arabic", "Times New Roman", serif',
    }) as string,
    light: Platform.select({
      ios: 'Geeza Pro',
      android: 'Noto Sans Arabic Light',
      web: '"Tajawal", "Cairo", "Noto Sans Arabic", "Segoe UI Arabic", "Tahoma", "Arabic Typesetting", "Geeza Pro", "Traditional Arabic", "Times New Roman", serif',
    }) as string,
  },
};

// Font loading configuration for custom fonts (if needed)
export const CUSTOM_FONTS = {
  // Add custom Arabic fonts here when available
  // 'Dubai-Regular': require('../../assets/fonts/Dubai-Regular.ttf'),
  // 'Dubai-Medium': require('../../assets/fonts/Dubai-Medium.ttf'),
  // 'Dubai-Bold': require('../../assets/fonts/Dubai-Bold.ttf'),
  // 'Tajawal-Regular': require('../../assets/fonts/Tajawal-Regular.ttf'),
  // 'Tajawal-Medium': require('../../assets/fonts/Tajawal-Medium.ttf'),
  // 'Tajawal-Bold': require('../../assets/fonts/Tajawal-Bold.ttf'),
};

// Web font loading for Arabic fonts
export const WEB_FONTS = {
  // Google Fonts Arabic fonts that can be loaded dynamically
  tajawal: 'https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap',
  amiri: 'https://fonts.googleapis.com/css2?family=Amiri:ital,wght@0,400;0,700;1,400;1,700&display=swap',
  notoSansArabic: 'https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@100;200;300;400;500;600;700;800;900&display=swap',
  cairo: 'https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap',
};

export class FontService {
  private static instance: FontService;
  private webFontsLoaded: Set<string> = new Set();

  private constructor() {
    // Initialize web font loading for web platform
    if (Platform.OS === 'web') {
      this.loadWebFonts();
    }
  }

  public static getInstance(): FontService {
    if (!FontService.instance) {
      FontService.instance = new FontService();
    }
    return FontService.instance;
  }

  public getFontFamily(language: SupportedLanguage, weight: keyof FontConfig = 'regular'): string {
    const isArabic = language === 'ar';
    const fontFamily = isArabic ? FONT_FAMILIES.ar : FONT_FAMILIES.en;
    
    return fontFamily[weight] || fontFamily.regular;
  }

  public getTextStyle(language: SupportedLanguage, weight: keyof FontConfig = 'regular') {
    const isArabic = language === 'ar';
    const fontFamily = this.getFontFamily(language, weight);

    return {
      fontFamily,
      // Arabic text typically needs more line height for better readability
      lineHeight: isArabic ? 1.7 : 1.4,
      // Arabic text direction
      writingDirection: isArabic ? 'rtl' as const : 'ltr' as const,
      textAlign: isArabic ? 'right' as const : 'left' as const,
      // Better letter spacing for Arabic
      letterSpacing: isArabic ? 0.3 : 0,
      // Ensure proper text rendering for Arabic
      ...(isArabic && {
        includeFontPadding: false, // Android specific - removes extra padding
        textAlignVertical: 'center' as const,
      }),
    };
  }

  public getArabicTextStyle(weight: keyof FontConfig = 'regular') {
    return {
      fontFamily: FONT_FAMILIES.ar[weight] || FONT_FAMILIES.ar.regular,
      lineHeight: 1.7,
      writingDirection: 'rtl' as const,
      textAlign: 'right' as const, // RTL: Will be automatically flipped by RTLText component
      letterSpacing: 0.3,
      includeFontPadding: false, // Android specific
      textAlignVertical: 'center' as const,
    };
  }

  public getEnglishTextStyle(weight: keyof FontConfig = 'regular') {
    return {
      fontFamily: FONT_FAMILIES.en[weight] || FONT_FAMILIES.en.regular,
      lineHeight: 1.4,
      writingDirection: 'ltr' as const,
      textAlign: 'left' as const, // RTL: Will be automatically flipped by RTLText component
    };
  }

  /**
   * Get adjusted font size for Arabic text
   * Arabic text often needs slightly larger font sizes for better readability
   */
  public getAdjustedFontSize(baseFontSize: number, language: SupportedLanguage): number {
    const isArabic = language === 'ar';
    // Increase Arabic font size by 10% for better readability
    return isArabic ? Math.round(baseFontSize * 1.1) : baseFontSize;
  }

  /**
   * Get optimal line height for given font size and language
   */
  public getOptimalLineHeight(fontSize: number, language: SupportedLanguage): number {
    const isArabic = language === 'ar';
    // Arabic needs more line height relative to font size
    const multiplier = isArabic ? 1.7 : 1.4;
    return fontSize * multiplier;
  }

  /**
   * Check if a font family supports Arabic characters
   */
  public supportsArabic(fontFamily: string): boolean {
    const arabicSupportingFonts = [
      'Geeza Pro',
      'Noto Sans Arabic',
      'Tahoma',
      'Arabic Typesetting',
      'Traditional Arabic',
      'Segoe UI Arabic',
      'Times New Roman',
      'Tajawal',
      'Amiri',
      'Cairo'
    ];

    return arabicSupportingFonts.some(font =>
      fontFamily.toLowerCase().includes(font.toLowerCase())
    );
  }

  /**
   * Load web fonts for better Arabic typography (web platform only)
   */
  private loadWebFonts(): void {
    if (Platform.OS !== 'web' || typeof document === 'undefined') {
      return;
    }

    Object.entries(WEB_FONTS).forEach(([fontName, fontUrl]) => {
      if (!this.webFontsLoaded.has(fontName)) {
        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.href = fontUrl;
        link.onload = () => {
          this.webFontsLoaded.add(fontName);
          console.log(`✅ Loaded Arabic font: ${fontName}`);
        };
        link.onerror = () => {
          console.warn(`⚠️ Failed to load Arabic font: ${fontName}`);
        };
        document.head.appendChild(link);
      }
    });
  }

  /**
   * Get enhanced Arabic font family with web font support
   */
  public getEnhancedArabicFontFamily(weight: keyof FontConfig = 'regular'): string {
    if (Platform.OS === 'web') {
      // Use web fonts with fallbacks
      const webFontStack = [
        'Tajawal',
        'Cairo',
        'Noto Sans Arabic',
        'Amiri'
      ].join(', ');

      return `${webFontStack}, ${FONT_FAMILIES.ar[weight]}`;
    }

    return FONT_FAMILIES.ar[weight] || FONT_FAMILIES.ar.regular;
  }

  /**
   * Check if web fonts are loaded (web platform only)
   */
  public areWebFontsLoaded(): boolean {
    return Platform.OS !== 'web' || this.webFontsLoaded.size > 0;
  }
}

export default FontService.getInstance();
