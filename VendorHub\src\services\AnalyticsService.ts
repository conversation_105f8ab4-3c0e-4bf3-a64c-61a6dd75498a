import { EventEmitter } from '../utils/EventEmitter';
import type { Product, Order, Vendor } from '../contexts/DataContext';
import type User  from '../contexts/AuthContext';
import { storage } from '../utils/storage';

export interface AnalyticsEvent {
  id: string;
  type: string;
  category: 'user' | 'product' | 'order' | 'vendor' | 'system';
  timestamp: string;
  userId?: string;
  sessionId: string;
  properties: Record<string, any>;
  metadata?: Record<string, any>;
}

export interface UserSegment {
  id: string;
  name: string;
  description: string;
  criteria: {
    totalOrders?: { min?: number; max?: number };
    totalSpent?: { min?: number; max?: number };
    lastOrderDays?: number;
    favoriteCategories?: string[];
    registrationDays?: { min?: number; max?: number };
  };
  userCount: number;
}

export interface BusinessMetrics {
  revenue: {
    total: number;
    thisMonth: number;
    lastMonth: number;
    growth: number;
  };
  orders: {
    total: number;
    thisMonth: number;
    lastMonth: number;
    averageValue: number;
    conversionRate: number;
  };
  users: {
    total: number;
    active: number;
    new: number;
    retention: number;
  };
  vendors: {
    total: number;
    active: number;
    topPerformers: Array<{ id: string; name: string; revenue: number }>;
  };
  products: {
    total: number;
    topSelling: Array<{ id: string; name: string; sales: number }>;
    lowStock: number;
    outOfStock: number;
  };
}

export interface SalesAnalytics {
  dailySales: Array<{ date: string; revenue: number; orders: number }>;
  monthlySales: Array<{ month: string; revenue: number; orders: number }>;
  categoryPerformance: Array<{ category: string; revenue: number; orders: number; growth: number }>;
  vendorPerformance: Array<{ vendorId: string; vendorName: string; revenue: number; orders: number; rating: number }>;
  productPerformance: Array<{ productId: string; productName: string; sales: number; revenue: number; views: number }>;
}

export interface UserBehaviorAnalytics {
  pageViews: Array<{ page: string; views: number; uniqueUsers: number; avgTime: number }>;
  userJourney: Array<{ step: string; users: number; dropoffRate: number }>;
  searchAnalytics: Array<{ query: string; count: number; resultsFound: number; clickThrough: number }>;
  deviceAnalytics: { platform: string; users: number; percentage: number }[];
  geographicData: Array<{ country: string; users: number; revenue: number }>;
}

class AnalyticsService extends EventEmitter {
  private static instance: AnalyticsService;
  private analyticsEvents: AnalyticsEvent[] = [];
  private sessionId: string;
  private userSegments: UserSegment[] = [];
  private isTracking: boolean = false;

  private constructor() {
    super();
    this.sessionId = this.generateSessionId();
    this.loadStoredData();
    this.initializeDefaultSegments();
  }

  public static getInstance(): AnalyticsService {
    if (!AnalyticsService.instance) {
      AnalyticsService.instance = new AnalyticsService();
    }
    return AnalyticsService.instance;
  }

  // Event Tracking
  public startTracking(): void {
    this.isTracking = true;
    this.trackEvent('session_start', 'system', {});
  }

  public stopTracking(): void {
    this.trackEvent('session_end', 'system', { duration: this.getSessionDuration() });
    this.isTracking = false;
    this.saveData();
  }

  public trackEvent(
    type: string,
    category: AnalyticsEvent['category'],
    properties: Record<string, any>,
    userId?: string,
    metadata?: Record<string, any>
  ): void {
    if (!this.isTracking) return;

    const event: AnalyticsEvent = {
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      type,
      category,
      timestamp: new Date().toISOString(),
      userId,
      sessionId: this.sessionId,
      properties,
      metadata,
    };

    this.analyticsEvents.push(event);
    this.emit('eventTracked', event);

    // Keep only last 10000 events
    if (this.analyticsEvents.length > 10000) {
      this.analyticsEvents.splice(0, this.analyticsEvents.length - 10000);
    }
  }

  // User Behavior Tracking
  public trackPageView(page: string, userId?: string, timeSpent?: number): void {
    this.trackEvent('page_view', 'user', { page, timeSpent }, userId);
  }

  public trackProductView(productId: string, productName: string, category: string, userId?: string): void {
    this.trackEvent('product_view', 'product', { productId, productName, category }, userId);
  }

  public trackSearch(query: string, resultsCount: number, userId?: string): void {
    this.trackEvent('search', 'user', { query, resultsCount }, userId);
  }

  public trackPurchase(orderId: string, totalAmount: number, items: any[], userId?: string): void {
    this.trackEvent('purchase', 'order', { orderId, totalAmount, items, itemCount: items.length }, userId);
  }

  public trackAddToCart(productId: string, productName: string, price: number, userId?: string): void {
    this.trackEvent('add_to_cart', 'product', { productId, productName, price }, userId);
  }

  public trackRemoveFromCart(productId: string, productName: string, userId?: string): void {
    this.trackEvent('remove_from_cart', 'product', { productId, productName }, userId);
  }

  // Business Metrics
  public generateBusinessMetrics(
    orders: Order[],
    users: User[],
    vendors: Vendor[],
    products: Product[]
  ): BusinessMetrics {
    const now = new Date();
    const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);

    // Revenue calculations
    const totalRevenue = orders.reduce((sum, order) => sum + order.totalAmount, 0);
    const thisMonthOrders = orders.filter(order => new Date(order.createdAt) >= thisMonth);
    const lastMonthOrders = orders.filter(order => {
      const orderDate = new Date(order.createdAt);
      return orderDate >= lastMonth && orderDate < thisMonth;
    });

    const thisMonthRevenue = thisMonthOrders.reduce((sum, order) => sum + order.totalAmount, 0);
    const lastMonthRevenue = lastMonthOrders.reduce((sum, order) => sum + order.totalAmount, 0);
    const revenueGrowth = lastMonthRevenue > 0 ? ((thisMonthRevenue - lastMonthRevenue) / lastMonthRevenue) * 100 : 0;

    // Order calculations
    const averageOrderValue = orders.length > 0 ? totalRevenue / orders.length : 0;
    const conversionRate = this.calculateConversionRate();

    // User calculations
    const activeUsers = this.getActiveUsersCount(users);
    const newUsers = users.filter(user => {
      const registrationDate = new Date(user.createdAt || '');
      return registrationDate >= thisMonth;
    }).length;
    const retention = this.calculateUserRetention(users, orders);

    // Vendor calculations
    const activeVendors = vendors.filter(vendor => vendor.status === 'approved').length;
    const topVendors = this.getTopPerformingVendors(vendors, orders);

    // Product calculations
    const topProducts = this.getTopSellingProducts(products, orders);
    const lowStockProducts = products.filter(product => product.inventory > 0 && product.inventory <= 10).length;
    const outOfStockProducts = products.filter(product => product.inventory === 0).length;

    return {
      revenue: {
        total: totalRevenue,
        thisMonth: thisMonthRevenue,
        lastMonth: lastMonthRevenue,
        growth: revenueGrowth,
      },
      orders: {
        total: orders.length,
        thisMonth: thisMonthOrders.length,
        lastMonth: lastMonthOrders.length,
        averageValue: averageOrderValue,
        conversionRate,
      },
      users: {
        total: users.length,
        active: activeUsers,
        new: newUsers,
        retention,
      },
      vendors: {
        total: vendors.length,
        active: activeVendors,
        topPerformers: topVendors,
      },
      products: {
        total: products.length,
        topSelling: topProducts,
        lowStock: lowStockProducts,
        outOfStock: outOfStockProducts,
      },
    };
  }

  // Sales Analytics
  public generateSalesAnalytics(orders: Order[], products: Product[]): SalesAnalytics {
    const dailySales = this.calculateDailySales(orders);
    const monthlySales = this.calculateMonthlySales(orders);
    const categoryPerformance = this.calculateCategoryPerformance(orders, products);
    const vendorPerformance = this.calculateVendorPerformance(orders);
    const productPerformance = this.calculateProductPerformance(orders, products);

    return {
      dailySales,
      monthlySales,
      categoryPerformance,
      vendorPerformance,
      productPerformance,
    };
  }

  // User Behavior Analytics
  public generateUserBehaviorAnalytics(): UserBehaviorAnalytics {
    const pageViews = this.calculatePageViews();
    const userJourney = this.calculateUserJourney();
    const searchAnalytics = this.calculateSearchAnalytics();
    const deviceAnalytics = this.calculateDeviceAnalytics();
    const geographicData = this.calculateGeographicData();

    return {
      pageViews,
      userJourney,
      searchAnalytics,
      deviceAnalytics,
      geographicData,
    };
  }

  // User Segmentation
  public createUserSegment(
    name: string,
    description: string,
    criteria: UserSegment['criteria']
  ): UserSegment {
    const segment: UserSegment = {
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      name,
      description,
      criteria,
      userCount: 0, // Will be calculated when applied
    };

    this.userSegments.push(segment);
    this.saveData();
    return segment;
  }

  public getUsersInSegment(segment: UserSegment, users: User[], orders: Order[]): User[] {
    return users.filter(user => this.userMatchesSegment(user, segment, orders));
  }

  private userMatchesSegment(user: User, segment: UserSegment, orders: Order[]): boolean {
    const userOrders = orders.filter(order => order.customerId === user.id);
    const { criteria } = segment;

    // Check total orders
    if (criteria.totalOrders) {
      const orderCount = userOrders.length;
      if (criteria.totalOrders.min && orderCount < criteria.totalOrders.min) return false;
      if (criteria.totalOrders.max && orderCount > criteria.totalOrders.max) return false;
    }

    // Check total spent
    if (criteria.totalSpent) {
      const totalSpent = userOrders.reduce((sum, order) => sum + order.totalAmount, 0);
      if (criteria.totalSpent.min && totalSpent < criteria.totalSpent.min) return false;
      if (criteria.totalSpent.max && totalSpent > criteria.totalSpent.max) return false;
    }

    // Check last order recency
    if (criteria.lastOrderDays) {
      const lastOrder = userOrders.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())[0];
      if (lastOrder) {
        const daysSinceLastOrder = (Date.now() - new Date(lastOrder.createdAt).getTime()) / (1000 * 60 * 60 * 24);
        if (daysSinceLastOrder > criteria.lastOrderDays) return false;
      } else if (criteria.lastOrderDays > 0) {
        return false; // No orders but criteria requires recent orders
      }
    }

    // Check registration recency
    if (criteria.registrationDays && user.createdAt) {
      const daysSinceRegistration = (Date.now() - new Date(user.createdAt).getTime()) / (1000 * 60 * 60 * 24);
      if (criteria.registrationDays.min && daysSinceRegistration < criteria.registrationDays.min) return false;
      if (criteria.registrationDays.max && daysSinceRegistration > criteria.registrationDays.max) return false;
    }

    return true;
  }

  // Helper Methods
  private calculateConversionRate(): number {
    const pageViewEvents = this.analyticsEvents.filter(e => e.type === 'page_view');
    const purchaseEvents = this.analyticsEvents.filter(e => e.type === 'purchase');
    
    if (pageViewEvents.length === 0) return 0;
    return (purchaseEvents.length / pageViewEvents.length) * 100;
  }

  private getActiveUsersCount(users: User[]): number {
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    const activeUserEvents = this.analyticsEvents.filter(e =>
      e.userId && new Date(e.timestamp) >= thirtyDaysAgo
    );
    const uniqueActiveUsers = new Set(activeUserEvents.map(e => e.userId));
    return uniqueActiveUsers.size;
  }

  private calculateUserRetention(users: User[], orders: Order[]): number {
    const usersWithMultipleOrders = users.filter(user => {
      const userOrders = orders.filter(order => order.customerId === user.id);
      return userOrders.length > 1;
    });
    
    return users.length > 0 ? (usersWithMultipleOrders.length / users.length) * 100 : 0;
  }

  private getTopPerformingVendors(vendors: Vendor[], orders: Order[]): Array<{ id: string; name: string; revenue: number }> {
    const vendorRevenue = new Map<string, number>();
    
    orders.forEach(order => {
      order.items.forEach(item => {
        const currentRevenue = vendorRevenue.get(item.vendorId) || 0;
        vendorRevenue.set(item.vendorId, currentRevenue + item.totalPrice);
      });
    });

    return Array.from(vendorRevenue.entries())
      .map(([vendorId, revenue]) => {
        const vendor = vendors.find(v => v.id === vendorId);
        return {
          id: vendorId,
          name: vendor?.businessName || 'Unknown Vendor',
          revenue,
        };
      })
      .sort((a, b) => b.revenue - a.revenue)
      .slice(0, 5);
  }

  private getTopSellingProducts(products: Product[], orders: Order[]): Array<{ id: string; name: string; sales: number }> {
    const productSales = new Map<string, number>();
    
    orders.forEach(order => {
      order.items.forEach(item => {
        const currentSales = productSales.get(item.productId) || 0;
        productSales.set(item.productId, currentSales + item.quantity);
      });
    });

    return Array.from(productSales.entries())
      .map(([productId, sales]) => {
        const product = products.find(p => p.id === productId);
        return {
          id: productId,
          name: product?.name || 'Unknown Product',
          sales,
        };
      })
      .sort((a, b) => b.sales - a.sales)
      .slice(0, 10);
  }

  private calculateDailySales(orders: Order[]): Array<{ date: string; revenue: number; orders: number }> {
    const dailyData = new Map<string, { revenue: number; orders: number }>();
    
    orders.forEach(order => {
      const date = new Date(order.createdAt).toISOString().split('T')[0];
      const current = dailyData.get(date) || { revenue: 0, orders: 0 };
      dailyData.set(date, {
        revenue: current.revenue + order.totalAmount,
        orders: current.orders + 1,
      });
    });

    return Array.from(dailyData.entries())
      .map(([date, data]) => ({ date, ...data }))
      .sort((a, b) => a.date.localeCompare(b.date))
      .slice(-30); // Last 30 days
  }

  private calculateMonthlySales(orders: Order[]): Array<{ month: string; revenue: number; orders: number }> {
    const monthlyData = new Map<string, { revenue: number; orders: number }>();
    
    orders.forEach(order => {
      const date = new Date(order.createdAt);
      const month = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
      const current = monthlyData.get(month) || { revenue: 0, orders: 0 };
      monthlyData.set(month, {
        revenue: current.revenue + order.totalAmount,
        orders: current.orders + 1,
      });
    });

    return Array.from(monthlyData.entries())
      .map(([month, data]) => ({ month, ...data }))
      .sort((a, b) => a.month.localeCompare(b.month))
      .slice(-12); // Last 12 months
  }

  private calculateCategoryPerformance(orders: Order[], products: Product[]): Array<{ category: string; revenue: number; orders: number; growth: number }> {
    const categoryData = new Map<string, { revenue: number; orders: number }>();
    
    orders.forEach(order => {
      order.items.forEach(item => {
        const product = products.find(p => p.id === item.productId);
        if (product) {
          const current = categoryData.get(product.category) || { revenue: 0, orders: 0 };
          categoryData.set(product.category, {
            revenue: current.revenue + item.totalPrice,
            orders: current.orders + 1,
          });
        }
      });
    });

    return Array.from(categoryData.entries())
      .map(([category, data]) => ({ category, ...data, growth: 0 })) // Growth calculation would need historical data
      .sort((a, b) => b.revenue - a.revenue);
  }

  private calculateVendorPerformance(orders: Order[]): Array<{ vendorId: string; vendorName: string; revenue: number; orders: number; rating: number }> {
    const vendorData = new Map<string, { revenue: number; orders: number }>();
    
    orders.forEach(order => {
      order.items.forEach(item => {
        const current = vendorData.get(item.vendorId) || { revenue: 0, orders: 0 };
        vendorData.set(item.vendorId, {
          revenue: current.revenue + item.totalPrice,
          orders: current.orders + 1,
        });
      });
    });

    return Array.from(vendorData.entries())
      .map(([vendorId, data]) => ({
        vendorId,
        vendorName: 'Vendor Name', // Would need vendor lookup
        ...data,
        rating: 4.5, // Would calculate from reviews
      }))
      .sort((a, b) => b.revenue - a.revenue);
  }

  private calculateProductPerformance(orders: Order[], products: Product[]): Array<{ productId: string; productName: string; sales: number; revenue: number; views: number }> {
    const productData = new Map<string, { sales: number; revenue: number }>();
    
    orders.forEach(order => {
      order.items.forEach(item => {
        const current = productData.get(item.productId) || { sales: 0, revenue: 0 };
        productData.set(item.productId, {
          sales: current.sales + item.quantity,
          revenue: current.revenue + item.totalPrice,
        });
      });
    });

    // Get view counts from events
    const productViews = new Map<string, number>();
    this.analyticsEvents.filter(e => e.type === 'product_view').forEach(event => {
      const productId = event.properties.productId;
      productViews.set(productId, (productViews.get(productId) || 0) + 1);
    });

    return Array.from(productData.entries())
      .map(([productId, data]) => {
        const product = products.find(p => p.id === productId);
        return {
          productId,
          productName: product?.name || 'Unknown Product',
          ...data,
          views: productViews.get(productId) || 0,
        };
      })
      .sort((a, b) => b.revenue - a.revenue);
  }

  private calculatePageViews(): Array<{ page: string; views: number; uniqueUsers: number; avgTime: number }> {
    const pageData = new Map<string, { views: number; users: Set<string>; totalTime: number }>();
    
    this.analyticsEvents.filter(e => e.type === 'page_view').forEach(event => {
      const page = event.properties.page;
      const current = pageData.get(page) || { views: 0, users: new Set(), totalTime: 0 };
      current.views++;
      if (event.userId) current.users.add(event.userId);
      if (event.properties.timeSpent) current.totalTime += event.properties.timeSpent;
      pageData.set(page, current);
    });

    return Array.from(pageData.entries())
      .map(([page, data]) => ({
        page,
        views: data.views,
        uniqueUsers: data.users.size,
        avgTime: data.views > 0 ? data.totalTime / data.views : 0,
      }))
      .sort((a, b) => b.views - a.views);
  }

  private calculateUserJourney(): Array<{ step: string; users: number; dropoffRate: number }> {
    // Simplified user journey calculation
    const steps = ['home', 'product_view', 'add_to_cart', 'checkout', 'purchase'];
    const stepCounts = new Map<string, Set<string>>();

    steps.forEach(step => stepCounts.set(step, new Set()));

    this.analyticsEvents.forEach(event => {
      if (event.userId) {
        switch (event.type) {
          case 'page_view':
            if (event.properties.page === 'home') {
              stepCounts.get('home')?.add(event.userId);
            }
            break;
          case 'product_view':
            stepCounts.get('product_view')?.add(event.userId);
            break;
          case 'add_to_cart':
            stepCounts.get('add_to_cart')?.add(event.userId);
            break;
          case 'checkout':
            stepCounts.get('checkout')?.add(event.userId);
            break;
          case 'purchase':
            stepCounts.get('purchase')?.add(event.userId);
            break;
        }
      }
    });

    return steps.map((step, index) => {
      const users = stepCounts.get(step)?.size || 0;
      const previousUsers = index > 0 ? stepCounts.get(steps[index - 1])?.size || 0 : users;
      const dropoffRate = previousUsers > 0 ? ((previousUsers - users) / previousUsers) * 100 : 0;
      
      return { step, users, dropoffRate };
    });
  }

  private calculateSearchAnalytics(): Array<{ query: string; count: number; resultsFound: number; clickThrough: number }> {
    const searchData = new Map<string, { count: number; resultsFound: number; clickThrough: number }>();
    
    this.analyticsEvents.filter(e => e.type === 'search').forEach(event => {
      const query = event.properties.query.toLowerCase();
      const current = searchData.get(query) || { count: 0, resultsFound: 0, clickThrough: 0 };
      current.count++;
      current.resultsFound += event.properties.resultsCount || 0;
      searchData.set(query, current);
    });

    return Array.from(searchData.entries())
      .map(([query, data]) => ({
        query,
        ...data,
        resultsFound: data.count > 0 ? Math.round(data.resultsFound / data.count) : 0,
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 20);
  }

  private calculateDeviceAnalytics(): Array<{ platform: string; users: number; percentage: number }> {
    const deviceData = new Map<string, Set<string>>();
    
    this.analyticsEvents.forEach(event => {
      if (event.userId && event.metadata?.platform) {
        const platform = event.metadata.platform;
        if (!deviceData.has(platform)) {
          deviceData.set(platform, new Set());
        }
        deviceData.get(platform)?.add(event.userId);
      }
    });

    const totalUsers = new Set(this.analyticsEvents.filter(e => e.userId).map(e => e.userId)).size;
    
    return Array.from(deviceData.entries())
      .map(([platform, users]) => ({
        platform,
        users: users.size,
        percentage: totalUsers > 0 ? (users.size / totalUsers) * 100 : 0,
      }))
      .sort((a, b) => b.users - a.users);
  }

  private calculateGeographicData(): Array<{ country: string; users: number; revenue: number }> {
    // Simplified geographic data - would need actual location data
    return [
      { country: 'United States', users: 150, revenue: 25000 },
      { country: 'Canada', users: 45, revenue: 8500 },
      { country: 'United Kingdom', users: 38, revenue: 7200 },
      { country: 'Germany', users: 32, revenue: 6100 },
      { country: 'France', users: 28, revenue: 5400 },
    ];
  }

  private generateSessionId(): string {
    return Date.now().toString() + Math.random().toString(36).substr(2, 9);
  }

  private getSessionDuration(): number {
    const sessionEvents = this.analyticsEvents.filter(e => e.sessionId === this.sessionId);
    if (sessionEvents.length < 2) return 0;
    
    const firstEvent = sessionEvents[0];
    const lastEvent = sessionEvents[sessionEvents.length - 1];
    
    return new Date(lastEvent.timestamp).getTime() - new Date(firstEvent.timestamp).getTime();
  }

  private initializeDefaultSegments(): void {
    if (this.userSegments.length === 0) {
      this.userSegments = [
        {
          id: 'high-value',
          name: 'High Value Customers',
          description: 'Customers who have spent more than $500',
          criteria: { totalSpent: { min: 500 } },
          userCount: 0,
        },
        {
          id: 'frequent-buyers',
          name: 'Frequent Buyers',
          description: 'Customers with 5 or more orders',
          criteria: { totalOrders: { min: 5 } },
          userCount: 0,
        },
        {
          id: 'new-customers',
          name: 'New Customers',
          description: 'Customers registered in the last 30 days',
          criteria: { registrationDays: { max: 30 } },
          userCount: 0,
        },
        {
          id: 'at-risk',
          name: 'At Risk Customers',
          description: 'Customers who haven\'t ordered in 90 days',
          criteria: { lastOrderDays: 90 },
          userCount: 0,
        },
      ];
    }
  }

  // Storage
  private async loadStoredData(): Promise<void> {
    try {
      const [eventsData, segmentsData] = await Promise.all([
        storage.getItem('analyticsEvents'),
        storage.getItem('userSegments'),
      ]);

      if (eventsData) {
        this.analyticsEvents = JSON.parse(eventsData);
      }

      if (segmentsData) {
        this.userSegments = JSON.parse(segmentsData);
      }
    } catch (error) {
      console.error('Error loading analytics data:', error);
    }
  }

  private async saveData(): Promise<void> {
    try {
      await Promise.all([
        storage.setItem('analyticsEvents', JSON.stringify(this.analyticsEvents)),
        storage.setItem('userSegments', JSON.stringify(this.userSegments)),
      ]);
    } catch (error) {
      console.error('Error saving analytics data:', error);
    }
  }

  // Public API
  public getEvents(category?: AnalyticsEvent['category'], limit?: number): AnalyticsEvent[] {
    let filteredEvents = category ? this.analyticsEvents.filter(e => e.category === category) : this.analyticsEvents;
    return limit ? filteredEvents.slice(-limit) : filteredEvents;
  }

  public getUserSegments(): UserSegment[] {
    return [...this.userSegments];
  }

  public clearData(): void {
    this.analyticsEvents = [];
    this.userSegments = [];
    this.saveData();
  }
}

export default AnalyticsService.getInstance();
