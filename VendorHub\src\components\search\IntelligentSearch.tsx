import React, { useState, useRef, useEffect } from 'react';
import { StyleSheet, Animated, Dimensions, Keyboard } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { RTLView, RTLText, RTLInput, RTLTouchableOpacity, RTLIcon, RTLScrollView } from '../RTL';
import { useThemedStyles, useVendors, useProducts, useI18n } from '../../hooks';
import { SPACING, FONT_SIZES, FONT_WEIGHTS, BORDER_RADIUS } from '../../constants/theme';
import { PREMIUM_GRADIENTS } from '../../constants/advancedColors';
import { FadeInView, AnimatedPressable, ShimmerView } from '../visual/MicroAnimations';
import { GlassmorphismCard } from '../visual/GlassmorphismCard';
import type { ThemeColors } from '../../contexts/ThemeContext';
import type { Vendor, Product } from '../../contexts/DataContext';

const { width: screenWidth } = Dimensions.get('window');

interface SearchSuggestion {
  id: string;
  type: 'vendor' | 'product' | 'category' | 'recent' | 'trending';
  title: string;
  subtitle?: string;
  icon: string;
  data?: Vendor | Product;
  popularity?: number;
}

interface IntelligentSearchProps {
  onSearch: (query: string) => void;
  onSuggestionSelect: (suggestion: SearchSuggestion) => void;
  onFilterChange?: (filters: SearchFilters) => void;
  placeholder?: string;
  showFilters?: boolean;
  autoFocus?: boolean;
}

interface SearchFilters {
  category?: string;
  priceRange?: [number, number];
  rating?: number;
  location?: string;
  sortBy?: 'relevance' | 'rating' | 'price' | 'distance' | 'popularity';
  sortOrder?: 'asc' | 'desc';
}

export const IntelligentSearch: React.FC<IntelligentSearchProps> = ({
  onSearch,
  onSuggestionSelect,
  onFilterChange,
  placeholder,
  showFilters = true,
  autoFocus = false,
}) => {
  const styles = useThemedStyles(createStyles);
  const { getApprovedVendors } = useVendors();
  const { getAllProducts } = useProducts();
  const { t } = useI18n();

  const [query, setQuery] = useState('');
  const [suggestions, setSuggestions] = useState<SearchSuggestion[]>([]);
  const [recentSearches, setRecentSearches] = useState<string[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [filters, setFilters] = useState<SearchFilters>({});
  const [isFilterExpanded, setIsFilterExpanded] = useState(false);

  const inputRef = useRef<any>(null);
  const searchTimeout = useRef<NodeJS.Timeout>();
  const suggestionHeight = useRef(new Animated.Value(0)).current;
  const filterHeight = useRef(new Animated.Value(0)).current;

  const vendors = getApprovedVendors();
  const products = getAllProducts();

  // Generate intelligent suggestions
  const generateSuggestions = (searchQuery: string): SearchSuggestion[] => {
    if (!searchQuery.trim()) {
      return generateDefaultSuggestions();
    }

    const query = searchQuery.toLowerCase();
    const suggestions: SearchSuggestion[] = [];

    // Vendor suggestions
    vendors.forEach(vendor => {
      if (vendor.businessName.toLowerCase().includes(query)) {
        suggestions.push({
          id: `vendor-${vendor.id}`,
          type: 'vendor',
          title: vendor.businessName,
          subtitle: vendor.businessDescription || t('search.vendor'),
          icon: 'storefront',
          data: vendor,
          popularity: vendor.rating * 20,
        });
      }
    });

    // Product suggestions
    products.forEach(product => {
      if (product.name.toLowerCase().includes(query) || 
          product.description?.toLowerCase().includes(query)) {
        suggestions.push({
          id: `product-${product.id}`,
          type: 'product',
          title: product.name,
          subtitle: `BHD ${product.price.toFixed(2)}`,
          icon: 'cube-outline',
          data: product,
          popularity: product.isActive ? 10 : 5,
        });
      }
    });

    // Category suggestions
    const categories = [...new Set(products.map(p => p.category).filter(Boolean))];
    categories.forEach(category => {
      if (category.toLowerCase().includes(query)) {
        const categoryProducts = products.filter(p => p.category === category);
        suggestions.push({
          id: `category-${category}`,
          type: 'category',
          title: category,
          subtitle: t('search.categoryProducts', { count: categoryProducts.length }),
          icon: 'grid-outline',
          popularity: categoryProducts.length,
        });
      }
    });

    // Sort by popularity and relevance
    return suggestions
      .sort((a, b) => (b.popularity || 0) - (a.popularity || 0))
      .slice(0, 8);
  };

  const generateDefaultSuggestions = (): SearchSuggestion[] => {
    const suggestions: SearchSuggestion[] = [];

    // Recent searches
    recentSearches.slice(0, 3).forEach((search, index) => {
      suggestions.push({
        id: `recent-${index}`,
        type: 'recent',
        title: search,
        subtitle: t('search.recentSearch'),
        icon: 'time-outline',
        popularity: 100 - index * 10,
      });
    });

    // Trending categories
    const trendingCategories = ['Electronics', 'Fashion', 'Home & Garden'];
    trendingCategories.forEach((category, index) => {
      suggestions.push({
        id: `trending-${category}`,
        type: 'trending',
        title: category,
        subtitle: t('search.trending'),
        icon: 'trending-up',
        popularity: 90 - index * 5,
      });
    });

    // Top vendors
    const topVendors = vendors
      .sort((a, b) => b.rating - a.rating)
      .slice(0, 3);
    
    topVendors.forEach(vendor => {
      suggestions.push({
        id: `top-vendor-${vendor.id}`,
        type: 'vendor',
        title: vendor.businessName,
        subtitle: t('search.topRated'),
        icon: 'star',
        data: vendor,
        popularity: vendor.rating * 15,
      });
    });

    return suggestions.slice(0, 8);
  };

  // Debounced search
  useEffect(() => {
    if (searchTimeout.current) {
      clearTimeout(searchTimeout.current);
    }

    searchTimeout.current = setTimeout(() => {
      const newSuggestions = generateSuggestions(query);
      setSuggestions(newSuggestions);
      setIsSearching(false);
    }, 300);

    return () => {
      if (searchTimeout.current) {
        clearTimeout(searchTimeout.current);
      }
    };
  }, [query, vendors, products]);

  const handleQueryChange = (text: string) => {
    setQuery(text);
    setIsSearching(true);
    setShowSuggestions(true);
    
    if (text.trim()) {
      animateSuggestions(true);
    } else {
      animateSuggestions(false);
    }
  };

  const handleSearch = (searchQuery?: string) => {
    const finalQuery = searchQuery || query;
    if (finalQuery.trim()) {
      // Add to recent searches
      setRecentSearches(prev => {
        const updated = [finalQuery, ...prev.filter(s => s !== finalQuery)];
        return updated.slice(0, 5);
      });
      
      onSearch(finalQuery);
      setShowSuggestions(false);
      Keyboard.dismiss();
    }
  };

  const handleSuggestionPress = (suggestion: SearchSuggestion) => {
    setQuery(suggestion.title);
    setShowSuggestions(false);
    onSuggestionSelect(suggestion);
    Keyboard.dismiss();
  };

  const animateSuggestions = (show: boolean) => {
    Animated.spring(suggestionHeight, {
      toValue: show ? 300 : 0,
      useNativeDriver: false,
      tension: 100,
      friction: 8,
    }).start();
  };

  const animateFilters = (show: boolean) => {
    Animated.spring(filterHeight, {
      toValue: show ? 200 : 0,
      useNativeDriver: false,
      tension: 100,
      friction: 8,
    }).start();
  };

  const renderSuggestion = (suggestion: SearchSuggestion, index: number) => (
    <FadeInView key={suggestion.id} duration={300} delay={index * 50}>
      <AnimatedPressable
        style={styles.suggestionItem}
        onPress={() => handleSuggestionPress(suggestion)}
      >
        <RTLView style={styles.suggestionContent}>
          <RTLView style={[styles.suggestionIcon, { backgroundColor: getSuggestionColor(suggestion.type) }]}>
            <RTLIcon name={suggestion.icon} size={16} color="#FFFFFF" />
          </RTLView>
          
          <RTLView style={styles.suggestionText}>
            <RTLText style={styles.suggestionTitle} numberOfLines={1}>
              {suggestion.title}
            </RTLText>
            {suggestion.subtitle && (
              <RTLText style={styles.suggestionSubtitle} numberOfLines={1}>
                {suggestion.subtitle}
              </RTLText>
            )}
          </RTLView>
          
          <RTLIcon name="arrow-forward" size={16} color="rgba(255, 255, 255, 0.5)" />
        </RTLView>
      </AnimatedPressable>
    </FadeInView>
  );

  const getSuggestionColor = (type: string) => {
    switch (type) {
      case 'vendor': return '#3B82F6';
      case 'product': return '#10B981';
      case 'category': return '#F59E0B';
      case 'recent': return '#6B7280';
      case 'trending': return '#EF4444';
      default: return '#8B5CF6';
    }
  };

  const renderFilters = () => {
    if (!showFilters) return null;

    return (
      <Animated.View style={[styles.filtersContainer, { height: filterHeight }]}>
        <GlassmorphismCard intensity="light" style={styles.filtersCard}>
          <RTLScrollView horizontal showsHorizontalScrollIndicator={false}>
            <RTLView style={styles.filterChips}>
              {/* Category Filter */}
              <RTLTouchableOpacity style={styles.filterChip}>
                <RTLIcon name="grid-outline" size={16} color="#3B82F6" />
                <RTLText style={styles.filterChipText}>{t('search.category')}</RTLText>
              </RTLTouchableOpacity>
              
              {/* Rating Filter */}
              <RTLTouchableOpacity style={styles.filterChip}>
                <RTLIcon name="star" size={16} color="#FFD700" />
                <RTLText style={styles.filterChipText}>{t('search.rating')}</RTLText>
              </RTLTouchableOpacity>
              
              {/* Price Filter */}
              <RTLTouchableOpacity style={styles.filterChip}>
                <RTLIcon name="pricetag" size={16} color="#10B981" />
                <RTLText style={styles.filterChipText}>{t('search.price')}</RTLText>
              </RTLTouchableOpacity>
              
              {/* Sort Filter */}
              <RTLTouchableOpacity style={styles.filterChip}>
                <RTLIcon name="swap-vertical" size={16} color="#8B5CF6" />
                <RTLText style={styles.filterChipText}>{t('search.sort')}</RTLText>
              </RTLTouchableOpacity>
            </RTLView>
          </RTLScrollView>
        </GlassmorphismCard>
      </Animated.View>
    );
  };

  return (
    <RTLView style={styles.container}>
      {/* Search Input */}
      <GlassmorphismCard intensity="medium" style={styles.searchCard}>
        <RTLView style={styles.searchContainer}>
          <RTLIcon name="search" size={20} color="rgba(255, 255, 255, 0.7)" />
          
          <RTLInput
            ref={inputRef}
            style={styles.searchInput}
            placeholder={placeholder || t('search.searchPlaceholder')}
            placeholderTextColor="rgba(255, 255, 255, 0.5)"
            value={query}
            onChangeText={handleQueryChange}
            onSubmitEditing={() => handleSearch()}
            onFocus={() => setShowSuggestions(true)}
            autoFocus={autoFocus}
            returnKeyType="search"
          />
          
          {query.length > 0 && (
            <RTLTouchableOpacity
              style={styles.clearButton}
              onPress={() => {
                setQuery('');
                setShowSuggestions(false);
                inputRef.current?.focus();
              }}
            >
              <RTLIcon name="close-circle" size={20} color="rgba(255, 255, 255, 0.5)" />
            </RTLTouchableOpacity>
          )}
          
          {showFilters && (
            <RTLTouchableOpacity
              style={styles.filterButton}
              onPress={() => {
                setIsFilterExpanded(!isFilterExpanded);
                animateFilters(!isFilterExpanded);
              }}
            >
              <RTLIcon 
                name="options" 
                size={20} 
                color={isFilterExpanded ? "#3B82F6" : "rgba(255, 255, 255, 0.7)"} 
              />
            </RTLTouchableOpacity>
          )}
        </RTLView>
      </GlassmorphismCard>

      {/* Filters */}
      {renderFilters()}

      {/* Suggestions */}
      {showSuggestions && (
        <Animated.View style={[styles.suggestionsContainer, { height: suggestionHeight }]}>
          <GlassmorphismCard intensity="strong" style={styles.suggestionsCard}>
            {isSearching ? (
              <RTLView style={styles.loadingContainer}>
                <ShimmerView style={styles.loadingShimmer}>
                  <RTLText style={styles.loadingText}>{t('search.searching')}</RTLText>
                </ShimmerView>
              </RTLView>
            ) : (
              <RTLScrollView showsVerticalScrollIndicator={false}>
                {suggestions.map((suggestion, index) => renderSuggestion(suggestion, index))}
              </RTLScrollView>
            )}
          </GlassmorphismCard>
        </Animated.View>
      )}
    </RTLView>
  );
};

const createStyles = (colors: ThemeColors) => StyleSheet.create({
  container: {
    zIndex: 1000,
  },
  searchCard: {
    marginBottom: SPACING.sm,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.md,
  },
  searchInput: {
    flex: 1,
    fontSize: FONT_SIZES.md,
    color: '#FFFFFF',
    marginHorizontal: SPACING.md,
    fontWeight: FONT_WEIGHTS.medium,
  },
  clearButton: {
    padding: SPACING.xs,
  },
  filterButton: {
    padding: SPACING.xs,
    marginLeft: SPACING.sm,
  },

  // Filters
  filtersContainer: {
    overflow: 'hidden',
    marginBottom: SPACING.sm,
  },
  filtersCard: {
    flex: 1,
  },
  filterChips: {
    flexDirection: 'row',
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.md,
    gap: SPACING.sm,
  },
  filterChip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(59, 130, 246, 0.2)',
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    borderRadius: BORDER_RADIUS.lg,
    borderWidth: 1,
    borderColor: 'rgba(59, 130, 246, 0.3)',
  },
  filterChipText: {
    fontSize: FONT_SIZES.sm,
    color: '#3B82F6',
    marginLeft: SPACING.xs,
    fontWeight: FONT_WEIGHTS.medium,
  },

  // Suggestions
  suggestionsContainer: {
    overflow: 'hidden',
  },
  suggestionsCard: {
    flex: 1,
    maxHeight: 300,
  },
  suggestionItem: {
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.md,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
  },
  suggestionContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  suggestionIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: SPACING.md,
  },
  suggestionText: {
    flex: 1,
  },
  suggestionTitle: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.semiBold,
    color: '#FFFFFF',
    marginBottom: SPACING.xs,
  },
  suggestionSubtitle: {
    fontSize: FONT_SIZES.sm,
    color: 'rgba(255, 255, 255, 0.7)',
  },

  // Loading
  loadingContainer: {
    padding: SPACING.xl,
    alignItems: 'center',
  },
  loadingShimmer: {
    width: '100%',
    height: 40,
    borderRadius: BORDER_RADIUS.md,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: FONT_SIZES.md,
    color: 'rgba(255, 255, 255, 0.7)',
    fontWeight: FONT_WEIGHTS.medium,
  },
});
