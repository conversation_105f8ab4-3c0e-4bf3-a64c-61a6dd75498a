import React, { useState, useEffect } from 'react';
import { StyleSheet, Animated } from 'react-native';
import { RTLView, RTLText, RTLIcon } from '../RTL';
import { useThemedStyles, useProducts, useI18n } from '../../hooks';
import { SPACING, FONT_SIZES, FONT_WEIGHTS, BORDER_RADIUS } from '../../constants/theme';
import type { ThemeColors } from '../../contexts/ThemeContext';
import type { Vendor } from '../../contexts/DataContext';

interface LiveActivityIndicatorsProps {
  vendor: Vendor;
  style?: any;
  compact?: boolean;
}

interface ActivityIndicator {
  id: string;
  type: 'new_order' | 'new_product' | 'sale_active' | 'high_demand' | 'recently_updated';
  icon: string;
  text: string;
  color: string;
  backgroundColor: string;
  borderColor: string;
  priority: number;
  animated?: boolean;
}

export const LiveActivityIndicators: React.FC<LiveActivityIndicatorsProps> = ({
  vendor,
  style,
  compact = false,
}) => {
  const styles = useThemedStyles(createStyles);
  const { getProductsByVendor } = useProducts();
  const { t } = useI18n();
  
  const [pulseAnim] = useState(new Animated.Value(1));
  const [fadeAnim] = useState(new Animated.Value(0));

  const vendorProducts = getProductsByVendor(vendor.id);
  const activeProducts = vendorProducts.filter(p => p.isActive);

  // Generate activity indicators based on vendor data
  const activityIndicators: ActivityIndicator[] = React.useMemo(() => {
    const indicators: ActivityIndicator[] = [];

    // Simulate recent orders (based on rating and random factor)
    if (vendor.rating >= 4.0 && Math.random() > 0.6) {
      const orderCount = Math.floor(Math.random() * 5) + 1;
      indicators.push({
        id: 'new_order',
        type: 'new_order',
        icon: 'flash',
        text: t('vendor.liveRecentOrders', { count: orderCount }),
        color: '#EF4444',
        backgroundColor: 'rgba(239, 68, 68, 0.1)',
        borderColor: '#EF4444',
        priority: 1,
        animated: true,
      });
    }

    // Check for new products (simulated)
    if (Math.random() > 0.7) {
      indicators.push({
        id: 'new_product',
        type: 'new_product',
        icon: 'add-circle',
        text: t('vendor.newProductsAdded'),
        color: '#10B981',
        backgroundColor: 'rgba(16, 185, 129, 0.1)',
        borderColor: '#10B981',
        priority: 2,
        animated: true,
      });
    }

    // Check for active sales
    const productsOnSale = activeProducts.filter(p => p.salePrice && p.salePrice < p.price);
    if (productsOnSale.length > 0) {
      indicators.push({
        id: 'sale_active',
        type: 'sale_active',
        icon: 'pricetag',
        text: t('vendor.activeSales', { count: productsOnSale.length }),
        color: '#F59E0B',
        backgroundColor: 'rgba(245, 158, 11, 0.1)',
        borderColor: '#F59E0B',
        priority: 3,
      });
    }

    // High demand indicator (based on rating)
    if (vendor.rating >= 4.5) {
      indicators.push({
        id: 'high_demand',
        type: 'high_demand',
        icon: 'trending-up',
        text: t('vendor.highDemand'),
        color: '#8B5CF6',
        backgroundColor: 'rgba(139, 92, 246, 0.1)',
        borderColor: '#8B5CF6',
        priority: 4,
      });
    }

    // Recently updated (simulated)
    if (Math.random() > 0.8) {
      indicators.push({
        id: 'recently_updated',
        type: 'recently_updated',
        icon: 'refresh',
        text: t('vendor.recentlyUpdated'),
        color: '#3B82F6',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        borderColor: '#3B82F6',
        priority: 5,
      });
    }

    // Sort by priority and return top 3 for compact mode, all for full mode
    return indicators
      .sort((a, b) => a.priority - b.priority)
      .slice(0, compact ? 2 : 4);
  }, [vendor, activeProducts, t, compact]);

  // Animation effects
  useEffect(() => {
    if (activityIndicators.length > 0) {
      // Fade in animation
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 500,
        useNativeDriver: true,
      }).start();

      // Pulse animation for animated indicators
      const hasAnimatedIndicators = activityIndicators.some(indicator => indicator.animated);
      if (hasAnimatedIndicators) {
        const pulse = Animated.loop(
          Animated.sequence([
            Animated.timing(pulseAnim, {
              toValue: 1.1,
              duration: 1500,
              useNativeDriver: true,
            }),
            Animated.timing(pulseAnim, {
              toValue: 1,
              duration: 1500,
              useNativeDriver: true,
            }),
          ])
        );
        pulse.start();
        return () => pulse.stop();
      }
    }
  }, [activityIndicators, fadeAnim, pulseAnim]);

  const renderIndicator = (indicator: ActivityIndicator, index: number) => {
    const animatedStyle = indicator.animated
      ? { transform: [{ scale: pulseAnim }] }
      : {};

    return (
      <Animated.View
        key={indicator.id}
        style={[
          styles.indicator,
          compact && styles.indicatorCompact,
          {
            backgroundColor: indicator.backgroundColor,
            borderColor: indicator.borderColor,
          },
          animatedStyle,
        ]}
      >
        <RTLIcon 
          name={indicator.icon} 
          size={compact ? 12 : 14} 
          color={indicator.color} 
        />
        {!compact && (
          <RTLText 
            style={[styles.indicatorText, { color: indicator.color }]}
            numberOfLines={1}
          >
            {indicator.text}
          </RTLText>
        )}
        
        {/* Animated dot for high priority indicators */}
        {indicator.animated && (
          <RTLView style={[styles.animatedDot, { backgroundColor: indicator.color }]} />
        )}
      </Animated.View>
    );
  };

  if (activityIndicators.length === 0) {
    return null;
  }

  return (
    <Animated.View 
      style={[
        styles.container,
        compact && styles.containerCompact,
        { opacity: fadeAnim },
        style
      ]}
    >
      {activityIndicators.map((indicator, index) => renderIndicator(indicator, index))}
    </Animated.View>
  );
};

const createStyles = (colors: ThemeColors) => StyleSheet.create({
  container: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: SPACING.sm,
  },
  containerCompact: {
    marginTop: SPACING.xs,
  },
  indicator: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: SPACING.sm,
    paddingVertical: SPACING.xs,
    borderRadius: BORDER_RADIUS.md,
    borderWidth: 1,
    marginRight: SPACING.sm,
    marginBottom: SPACING.xs,
    minHeight: 28,
  },
  indicatorCompact: {
    paddingHorizontal: SPACING.xs,
    paddingVertical: SPACING.xs,
    minHeight: 24,
    borderRadius: BORDER_RADIUS.sm,
  },
  indicatorText: {
    fontSize: FONT_SIZES.xs,
    fontWeight: FONT_WEIGHTS.medium,
    marginLeft: SPACING.xs,
    flex: 1,
  },
  animatedDot: {
    position: 'absolute',
    top: -2,
    right: -2,
    width: 8,
    height: 8,
    borderRadius: 4,
    borderWidth: 2,
    borderColor: '#FFFFFF',
  },
});
