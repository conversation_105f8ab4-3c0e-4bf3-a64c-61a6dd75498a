import React from 'react';
import { StyleSheet, ViewStyle } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import { RTLView } from '../RTL';
import { useThemedStyles } from '../../hooks';
import { BORDER_RADIUS, SPACING } from '../../constants/theme';
import type { ThemeColors } from '../../contexts/ThemeContext';

interface GlassmorphismCardProps {
  children: React.ReactNode;
  style?: ViewStyle;
  intensity?: 'light' | 'medium' | 'strong';
  tint?: 'light' | 'dark' | 'default';
  borderRadius?: number;
  padding?: number;
  gradient?: readonly string[];
  borderWidth?: number;
  borderColor?: string;
  shadowIntensity?: number;
}

export const GlassmorphismCard: React.FC<GlassmorphismCardProps> = ({
  children,
  style,
  intensity = 'medium',
  tint = 'dark',
  borderRadius = BORDER_RADIUS.xl,
  padding = SPACING.md,
  gradient,
  borderWidth = 1,
  borderColor = 'rgba(255, 255, 255, 0.2)',
  shadowIntensity = 0.3,
}) => {
  const styles = useThemedStyles(createStyles);

  const getBlurIntensity = () => {
    switch (intensity) {
      case 'light': return 20;
      case 'medium': return 40;
      case 'strong': return 80;
      default: return 40;
    }
  };

  const getGradientColors = () => {
    if (gradient) return gradient;
    
    switch (intensity) {
      case 'light':
        return ['rgba(255, 255, 255, 0.1)', 'rgba(255, 255, 255, 0.05)'];
      case 'medium':
        return ['rgba(255, 255, 255, 0.15)', 'rgba(255, 255, 255, 0.08)'];
      case 'strong':
        return ['rgba(255, 255, 255, 0.25)', 'rgba(255, 255, 255, 0.12)'];
      default:
        return ['rgba(255, 255, 255, 0.15)', 'rgba(255, 255, 255, 0.08)'];
    }
  };

  const cardStyle = [
    styles.container,
    {
      borderRadius,
      borderWidth,
      borderColor,
      shadowOpacity: shadowIntensity,
    },
    style,
  ];

  return (
    <RTLView style={cardStyle}>
      {/* Blur Background */}
      <BlurView
        intensity={getBlurIntensity()}
        tint={tint}
        style={[styles.blurContainer, { borderRadius }]}
      >
        {/* Gradient Overlay */}
        <LinearGradient
          colors={getGradientColors()}
          style={[styles.gradientOverlay, { borderRadius }]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        >
          {/* Content */}
          <RTLView style={[styles.content, { padding }]}>
            {children}
          </RTLView>

          {/* Shimmer Effect */}
          <RTLView style={[styles.shimmer, { borderRadius }]} />
        </LinearGradient>
      </BlurView>

      {/* Border Highlight */}
      <RTLView 
        style={[
          styles.borderHighlight, 
          { 
            borderRadius,
            borderWidth: borderWidth * 0.5,
            borderColor: 'rgba(255, 255, 255, 0.4)',
          }
        ]} 
      />
    </RTLView>
  );
};

const createStyles = (colors: ThemeColors) => StyleSheet.create({
  container: {
    backgroundColor: 'transparent',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowRadius: 16,
    elevation: 8,
  },
  blurContainer: {
    flex: 1,
    overflow: 'hidden',
  },
  gradientOverlay: {
    flex: 1,
    overflow: 'hidden',
  },
  content: {
    flex: 1,
    zIndex: 2,
  },
  shimmer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.6)',
    opacity: 0.8,
  },
  borderHighlight: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    pointerEvents: 'none',
  },
});
