import React, { useState } from 'react';
import {
  StyleSheet,
  Dimensions,
  Alert } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useThemedStyles, useProducts, useVendors, useCart, useI18n } from '../../hooks';
import { Card, Button, StatusBadge, ImageCarousel, ChatButton, RecommendationsSection } from '../../components';
import { RTLView, RTLText, RTLScrollView, RTLIcon, RTLSafeAreaView, RTLTouchableOpacity } from '../../components/RTL';
import {
  SPACING,
  FONT_SIZES,
  FONT_WEIGHTS,
  BORDER_RADIUS,
  GRADIENTS,
  ICON_SIZES } from '../../constants/theme';
import { formatCurrency } from '../../utils';
import type { ThemeColors } from '../../contexts/ThemeContext';
import type { Product } from '../../contexts/DataContext';

interface ProductDetailsScreenProps {
  navigation: any;
  route: {
    params: {
      productId: string;
    };
  };
}

const { width: screenWidth } = Dimensions.get('window');

export const ProductDetailsScreen: React.FC<ProductDetailsScreenProps> = ({ 
  navigation, 
  route 
}) => {
  const { productId } = route.params;
  const styles = useThemedStyles(createStyles);
  const { getProductById } = useProducts();
  const { getVendorById } = useVendors();
  const { addToCart, cartItems } = useCart();
  const { t } = useI18n();
  
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [quantity, setQuantity] = useState(1);
  const [showFullDescription, setShowFullDescription] = useState(false);

  const product = getProductById(productId);
  const vendor = product ? getVendorById(product.vendorId) : null;

  // Check if product is already in cart
  const isInCart = cartItems.some(item => item.productId === productId);
  const cartItem = cartItems.find(item => item.productId === productId);

  if (!product || !vendor) {
    return (
      <RTLSafeAreaView style={styles.container}>
        <RTLView style={styles.errorContainer}>
          <RTLIcon name="alert-circle-outline" size={64} color="#FF6B6B" />
          <RTLText style={styles.errorTitle} weight="bold">{t('errors.productNotFound')}</RTLText>
          <RTLText style={styles.errorDescription}>
            {t('errors.productNotFoundDescription')}
          </RTLText>
          <Button
            title={t('errors.goBack')}
            onPress={() => navigation.goBack()}
            style={styles.errorButton}
          />
        </RTLView>
      </RTLSafeAreaView>
    );
  }

  const handleAddToCart = () => {
    try {
      addToCart(product, quantity);
      Alert.alert(
        t('products.addedToCart'),
        `${product.name} ${t('cart.addedToCartMessage')}`,
        [
          { text: t('cart.continueShopping'), style: 'cancel' },
          { text: t('cart.viewCart'), onPress: () => navigation.navigate('Cart') },
        ]
      );
    } catch (error) {
      Alert.alert(t('common.error'), t('cart.failedToAddToCart'));
    }
  };

  const handleBuyNow = () => {
    handleAddToCart();
    navigation.navigate('Cart');
  };

  const handleVendorPress = () => {
    navigation.navigate('VendorShop', { vendorId: vendor.id });
  };

  const handleQuantityChange = (change: number) => {
    const newQuantity = quantity + change;
    if (newQuantity >= 1 && newQuantity <= product.inventory) {
      setQuantity(newQuantity);
    }
  };

  const renderImageGallery = () => (
    <RTLView style={styles.imageGallery}>
      <ImageCarousel
        images={product.images}
        height={300}
        showIndicators={true}
        showFullscreenButton={true}
        style={styles.carousel}
      />

      {/* Discount Badge */}
      {product.originalPrice && product.originalPrice > product.price && (
        <RTLView style={styles.discountBadge}>
          <RTLText style={styles.discountText} weight="bold">
            {Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)}% {t('products.discount')}
          </RTLText>
        </RTLView>
      )}
    </RTLView>
  );

  return (
    <RTLSafeAreaView style={styles.container}>
      <RTLScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Image Gallery */}
        {renderImageGallery()}

        {/* Product Info */}
        <RTLView style={styles.productInfo}>
          <RTLText style={styles.productName} weight="bold">{product.name}</RTLText>

          {/* Rating and Reviews */}
          <RTLView style={styles.ratingContainer}>
            <RTLView style={styles.rating}>
              <RTLIcon name="star" size={16} color="#FFD700" />
              <RTLText style={styles.ratingText}>{product.rating.toFixed(1)}</RTLText>
            </RTLView>
            <RTLText style={styles.reviewCount}>({product.reviewCount} {t('products.reviews')})</RTLText>
          </RTLView>

          {/* Price */}
          <RTLView style={styles.priceContainer}>
            <RTLText style={styles.price}>{formatCurrency(product.price)}</RTLText>
            {product.originalPrice && product.originalPrice > product.price && (
              <RTLText style={styles.originalPrice}>
                {formatCurrency(product.originalPrice)}
              </RTLText>
            )}
          </RTLView>

          {/* Stock Status */}
          <RTLView style={styles.stockContainer}>
            {product.inventory > 0 ? (
              <StatusBadge
                status={t('products.inStock')}
                customColor="#4CAF50"
                style={styles.stockBadge}
              />
            ) : (
              <StatusBadge
                status={t('products.outOfStock')}
                customColor="#F44336"
                style={styles.stockBadge}
              />
            )}
            <RTLText style={styles.stockText}>
              {product.inventory > 0 ? `${product.inventory} ${t('products.available')}` : t('products.currentlyUnavailable')}
            </RTLText>
          </RTLView>

          {/* Description */}
          <Card style={styles.descriptionCard} variant="outlined">
            <RTLText style={styles.sectionTitle}>{t('products.description')}</RTLText>
            <RTLText
              style={styles.description}
              numberOfLines={showFullDescription ? undefined : 3}
            >
              {product.description}
            </RTLText>
            {product.description.length > 150 && (
              <RTLTouchableOpacity onPress={() => setShowFullDescription(!showFullDescription)}>
                <RTLText style={styles.readMoreText}>
                  {showFullDescription ? t('products.readLess') : t('products.readMore')}
                </RTLText>
              </RTLTouchableOpacity>
            )}
          </Card>

          {/* Specifications */}
          {product.specifications && Object.keys(product.specifications).length > 0 && (
            <Card style={styles.specificationsCard} variant="outlined">
              <RTLText style={styles.sectionTitle}>{t('products.specifications')}</RTLText>
              {Object.entries(product.specifications).map(([key, value]) => (
                <RTLView key={key} style={styles.specRow}>
                  <RTLText style={styles.specKey}>{key}:</RTLText>
                  <RTLText style={styles.specValue}>{value}</RTLText>
                </RTLView>
              ))}
            </Card>
          )}

          {/* Vendor Info */}
          <Card style={styles.vendorCard} variant="elevated" onPress={handleVendorPress}>
            <RTLView style={styles.vendorHeader}>
              <RTLView style={styles.vendorLogo}>
                <RTLIcon name="storefront-outline" size={24} color="#667eea" />
              </RTLView>
              <RTLView style={styles.vendorInfo}>
                <RTLText style={styles.vendorName}>{vendor.businessName}</RTLText>
                <RTLView style={styles.vendorRating}>
                  <RTLIcon name="star" size={14} color="#FFD700" />
                  <RTLText style={styles.vendorRatingText}>{vendor.rating.toFixed(1)}</RTLText>
                  <RTLText style={styles.vendorProducts}>• {vendor.totalProducts} {t('products.productsCount')}</RTLText>
                </RTLView>
              </RTLView>
              <ChatButton
                targetUserId={vendor.id}
                targetUserName={vendor.businessName}
                targetUserAvatar={vendor.businessLogo}
                variant="icon"
                size="medium"
              />
              <RTLIcon name="chevron-forward" size={20} color="#CCCCCC" />
            </RTLView>
          </Card>

          {/* Tags */}
          {product.tags && product.tags.length > 0 && (
            <RTLView style={styles.tagsContainer}>
              <RTLText style={styles.sectionTitle}>{t('products.tags')}</RTLText>
              <RTLView style={styles.tags}>
                {product.tags.map((tag, index) => (
                  <RTLView key={index} style={styles.tag}>
                    <RTLText style={styles.tagText}>{tag}</RTLText>
                  </RTLView>
                ))}
              </RTLView>
            </RTLView>
          )}
        </RTLView>
      </RTLScrollView>

      {/* Bottom Action Bar */}
      <RTLView style={styles.bottomBar}>
        <LinearGradient
          colors={['rgba(255,255,255,0)', 'rgba(255,255,255,0.95)']}
          style={styles.bottomGradient}
        />

        {product.inventory > 0 && (
          <RTLView style={styles.quantityContainer}>
            <RTLText style={styles.quantityLabel}>{t('cart.quantity')}:</RTLText>
            <RTLView style={styles.quantityControls}>
              <RTLTouchableOpacity
                style={[styles.quantityButton, quantity <= 1 && styles.quantityButtonDisabled]}
                onPress={() => handleQuantityChange(-1)}
                disabled={quantity <= 1}
              >
                <RTLIcon name="remove" size={20} color={quantity <= 1 ? "#CCCCCC" : "#667eea"} />
              </RTLTouchableOpacity>
              <RTLText style={styles.quantityText}>{quantity}</RTLText>
              <RTLTouchableOpacity
                style={[styles.quantityButton, quantity >= product.inventory && styles.quantityButtonDisabled]}
                onPress={() => handleQuantityChange(1)}
                disabled={quantity >= product.inventory}
              >
                <RTLIcon name="add" size={20} color={quantity >= product.inventory ? "#CCCCCC" : "#667eea"} />
              </RTLTouchableOpacity>
            </RTLView>
          </RTLView>
        )}

        {/* Similar Products */}
        <RecommendationsSection
          title={t('products.similarProducts')}
          context={{
            currentProduct: product,
            currentCategory: product.category,
            excludeProductIds: [product.id],
          }}
          limit={6}
          showReason={false}
        />

        <RTLView style={styles.actionButtons}>
          {product.inventory > 0 ? (
            <>
              <Button
                title={isInCart ? `${t('cart.updateCart')} (${cartItem?.quantity || 0})` : t('products.addToCart')}
                onPress={handleAddToCart}
                variant="outline"
                style={styles.addToCartButton}
                leftIcon={<RTLIcon name="bag-add-outline" size={20} color="#667eea" />}
              />
              <Button
                title={t('products.buyNow')}
                onPress={handleBuyNow}
                style={styles.buyNowButton}
                leftIcon={<RTLIcon name="flash-outline" size={20} color="#FFFFFF" />}
              />
            </>
          ) : (
            <Button
              title={t('products.outOfStock')}
              disabled
              onPress={() => {}}
              style={styles.outOfStockButton}
              leftIcon={<RTLIcon name="close-circle-outline" size={20} color="#CCCCCC" />}
            />
          )}
        </RTLView>
      </RTLView>
    </RTLSafeAreaView>
  );
};

const createStyles = (colors: ThemeColors) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    scrollView: {
      flex: 1,
    },
    errorContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: SPACING.xl,
    },
    errorTitle: {
      fontSize: FONT_SIZES.xl,
      fontWeight: FONT_WEIGHTS.bold,
      color: colors.textPrimary,
      marginTop: SPACING.md,
      marginBottom: SPACING.sm,
    },
    errorDescription: {
      fontSize: FONT_SIZES.md,
      color: colors.textSecondary,
      textAlign: 'center',
      marginBottom: SPACING.xl,
    },
    errorButton: {
      minWidth: 120,
    },
    imageGallery: {
      position: 'relative',
      marginBottom: SPACING.lg,
    },
    carousel: {
      borderRadius: 0,
    },
    discountBadge: {
      position: 'absolute',
      top: SPACING.md,
      right: SPACING.md,
      backgroundColor: '#FF6B6B',
      paddingHorizontal: SPACING.sm,
      paddingVertical: SPACING.xs,
      borderRadius: BORDER_RADIUS.sm,
    },
    discountText: {
      color: '#FFFFFF',
      fontSize: FONT_SIZES.sm,
      fontWeight: FONT_WEIGHTS.bold,
    },
    productInfo: {
      padding: SPACING.lg,
    },
    productName: {
      fontSize: FONT_SIZES.xl,
      fontWeight: FONT_WEIGHTS.bold,
      color: colors.textPrimary,
      marginBottom: SPACING.sm,
    },
    ratingContainer: {
      flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
      alignItems: 'center',
      marginBottom: SPACING.md,
      justifyContent: 'flex-start', // This will be flipped in RTL
    },
    rating: {
      flexDirection: 'row', // This will be flipped to 'row-reverse' in RTL by RTLView
      alignItems: 'center',
      marginHorizontal: SPACING.sm,
      justifyContent: 'flex-start', // This will be flipped in RTL
    },
    ratingText: {
      fontSize: FONT_SIZES.md,
      fontWeight: FONT_WEIGHTS.semibold,
      color: colors.textPrimary,
      marginHorizontal: SPACING.xs,
    },
    reviewCount: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
    },
    priceContainer: {
      flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
      alignItems: 'center',
      marginBottom: SPACING.md,
      justifyContent: 'flex-start', // This will be flipped in RTL
    },
    price: {
      fontSize: FONT_SIZES.xxl,
      fontWeight: FONT_WEIGHTS.bold,
      color: '#667eea',
      marginHorizontal: SPACING.sm,
      textAlign: 'left', // RTL: Will be automatically flipped by RTLText component
    },
    originalPrice: {
      fontSize: FONT_SIZES.lg,
      color: colors.textSecondary,
      textDecorationLine: 'line-through',
      textAlign: 'left', // RTL: Will be automatically flipped by RTLText component
    },
    stockContainer: {
      flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
      alignItems: 'center',
      marginBottom: SPACING.lg,
    },
    stockBadge: {
      marginHorizontal: SPACING.sm,
    },
    stockText: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
    },
    descriptionCard: {
      marginBottom: SPACING.md,
    },
    sectionTitle: {
      fontSize: FONT_SIZES.lg,
      fontWeight: FONT_WEIGHTS.semibold,
      color: colors.textPrimary,
      marginBottom: SPACING.sm,
    },
    description: {
      fontSize: FONT_SIZES.md,
      color: colors.textSecondary,
      lineHeight: 22,
    },
    readMoreText: {
      fontSize: FONT_SIZES.sm,
      color: '#667eea',
      fontWeight: FONT_WEIGHTS.semibold,
      marginTop: SPACING.sm,
    },
    specificationsCard: {
      marginBottom: SPACING.md,
    },
    specRow: {
      flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingVertical: SPACING.xs,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    specKey: {
      fontSize: FONT_SIZES.md,
      fontWeight: FONT_WEIGHTS.medium,
      color: colors.textPrimary,
      flex: 1,
    },
    specValue: {
      fontSize: FONT_SIZES.md,
      color: colors.textSecondary,
      flex: 1,
      textAlign: 'auto',
    },
    vendorCard: {
      marginBottom: SPACING.md,
    },
    vendorHeader: {
      flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
      alignItems: 'center',
    },
    vendorLogo: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: colors.surface,
      justifyContent: 'center',
      alignItems: 'center',
      marginHorizontal: SPACING.sm,
    },
    vendorInfo: {
      flex: 1,
    },
    vendorName: {
      fontSize: FONT_SIZES.md,
      fontWeight: FONT_WEIGHTS.semibold,
      color: colors.textPrimary,
      marginBottom: SPACING.xs,
    },
    vendorRating: {
      flexDirection: 'row', // This will be flipped to 'row-reverse' in RTL by RTLView
      alignItems: 'center',
      justifyContent: 'flex-start', // This will be flipped in RTL
    },
    vendorRatingText: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
      marginHorizontal: SPACING.xs,
    },
    vendorProducts: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
      marginHorizontal: SPACING.xs,
    },
    tagsContainer: {
      marginBottom: SPACING.xl,
    },
    tags: {
      flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
      flexWrap: 'wrap',
    },
    tag: {
      backgroundColor: colors.surface,
      paddingHorizontal: SPACING.sm,
      paddingVertical: SPACING.xs,
      borderRadius: BORDER_RADIUS.sm,
      marginHorizontal: SPACING.xs,
      marginBottom: SPACING.xs,
    },
    tagText: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
    },
    bottomBar: {
      backgroundColor: colors.background,
      borderTopWidth: 1,
      borderTopColor: colors.border,
      paddingHorizontal: SPACING.lg,
      paddingVertical: SPACING.md,
      position: 'relative',
    },
    bottomGradient: {
      position: 'absolute',
      top: -20,
      left: 0,
      right: 0,
      height: 20,
    },
    quantityContainer: {
      flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
      alignItems: 'center',
      justifyContent: 'space-between',
      marginBottom: SPACING.md,
    },
    quantityLabel: {
      fontSize: FONT_SIZES.md,
      fontWeight: FONT_WEIGHTS.medium,
      color: colors.textPrimary,
    },
    quantityControls: {
      flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
      alignItems: 'center',
      backgroundColor: colors.surface,
      borderRadius: BORDER_RADIUS.sm,
      padding: SPACING.xs,
    },
    quantityButton: {
      width: 32,
      height: 32,
      borderRadius: 16,
      backgroundColor: colors.background,
      justifyContent: 'center',
      alignItems: 'center',
    },
    quantityButtonDisabled: {
      backgroundColor: colors.surface,
    },
    quantityText: {
      fontSize: FONT_SIZES.md,
      fontWeight: FONT_WEIGHTS.semibold,
      color: colors.textPrimary,
      marginHorizontal: SPACING.md,
      minWidth: 30,
      textAlign: 'center',
    },
    actionButtons: {
      flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
      gap: SPACING.sm,
    },
    addToCartButton: {
      flex: 1,
    },
    buyNowButton: {
      flex: 1,
    },
    outOfStockButton: {
      flex: 1,
      backgroundColor: colors.surface,
    },
  });
