#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Configuration
const config = {
  srcDir: path.join(__dirname, '../src'),
  outputFile: path.join(__dirname, '../DUPLICATE_IMPORTS_FIXES_REPORT.md'),
  excludePatterns: [
    /node_modules/,
    /\.git/,
    /\.expo/,
    /dist/,
    /build/,
    /coverage/,
    /\.DS_Store/,
    /\.env/,
    /package-lock\.json/,
    /yarn\.lock/,
    /\.log$/,
    /\.md$/,
    /\.json$/,
    /\.js\.map$/,
    /\.d\.ts$/,
    /scripts/,
    /assets/,
  ],
  fileExtensions: ['.tsx', '.ts', '.jsx', '.js'],
};

class DuplicateImportsFixer {
  constructor() {
    this.results = {
      filesProcessed: 0,
      filesFixed: 0,
      totalFixes: 0,
      fixedFiles: []
    };
  }

  scanDirectory(dirPath) {
    const items = fs.readdirSync(dirPath);
    
    for (const item of items) {
      const fullPath = path.join(dirPath, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        // Skip excluded directories
        if (config.excludePatterns.some(pattern => pattern.test(fullPath))) {
          continue;
        }
        this.scanDirectory(fullPath);
      } else if (stat.isFile()) {
        // Check if file should be processed
        if (this.shouldProcessFile(fullPath)) {
          this.processFile(fullPath);
        }
      }
    }
  }

  shouldProcessFile(filePath) {
    // Check file extension
    const ext = path.extname(filePath);
    if (!config.fileExtensions.includes(ext)) {
      return false;
    }
    
    // Check exclude patterns
    if (config.excludePatterns.some(pattern => pattern.test(filePath))) {
      return false;
    }
    
    return true;
  }

  processFile(filePath) {
    this.results.filesProcessed++;
    
    try {
      let content = fs.readFileSync(filePath, 'utf8');
      let originalContent = content;
      let fileFixed = false;
      let fileFixes = [];

      // Find and fix duplicate imports
      const fixes = this.findAndFixDuplicateImports(content);
      
      if (fixes.length > 0) {
        content = fixes.reduce((acc, fix) => fix.newContent, content);
        fileFixed = true;
        fileFixes = fixes;
        this.results.totalFixes += fixes.length;
      }

      // Write file if changes were made
      if (fileFixed && content !== originalContent) {
        fs.writeFileSync(filePath, content, 'utf8');
        this.results.filesFixed++;
        this.results.fixedFiles.push({
          path: path.relative(process.cwd(), filePath),
          fixes: fileFixes,
          totalFixes: fileFixes.length
        });
        
        console.log(`✅ Fixed: ${path.relative(process.cwd(), filePath)} (${fileFixes.length} fixes)`);
      }
    } catch (error) {
      console.error(`❌ Error processing ${filePath}:`, error.message);
    }
  }

  findAndFixDuplicateImports(content) {
    const lines = content.split('\n');
    const fixes = [];
    const importMap = new Map();
    const importLines = [];

    // Find all import statements
    lines.forEach((line, index) => {
      const trimmedLine = line.trim();
      if (trimmedLine.startsWith('import ') && trimmedLine.includes('from ')) {
        const match = trimmedLine.match(/import\s+(.+?)\s+from\s+['"](.+?)['"]/);
        if (match) {
          const [, imports, source] = match;
          importLines.push({
            lineIndex: index,
            line: line,
            imports: imports.trim(),
            source: source.trim(),
            originalLine: trimmedLine
          });
        }
      }
    });

    // Group imports by source
    const sourceGroups = new Map();
    importLines.forEach(importLine => {
      if (!sourceGroups.has(importLine.source)) {
        sourceGroups.set(importLine.source, []);
      }
      sourceGroups.get(importLine.source).push(importLine);
    });

    // Find duplicates and merge them
    sourceGroups.forEach((imports, source) => {
      if (imports.length > 1) {
        // Merge all imports from the same source
        const allImports = new Set();
        const linesToRemove = [];
        let firstImportLine = null;

        imports.forEach((importLine, index) => {
          if (index === 0) {
            firstImportLine = importLine;
          } else {
            linesToRemove.push(importLine.lineIndex);
          }

          // Parse imports
          const importsPart = importLine.imports;
          if (importsPart.startsWith('{') && importsPart.endsWith('}')) {
            // Named imports
            const namedImports = importsPart.slice(1, -1)
              .split(',')
              .map(imp => imp.trim())
              .filter(imp => imp.length > 0);
            namedImports.forEach(imp => allImports.add(imp));
          } else {
            // Default or other imports
            allImports.add(importsPart);
          }
        });

        if (linesToRemove.length > 0) {
          // Create merged import
          const mergedImports = Array.from(allImports).sort().join(', ');
          const newImportLine = `import { ${mergedImports} } from '${source}';`;
          
          // Create fix
          let newContent = content;
          
          // Remove duplicate lines (in reverse order to maintain line numbers)
          linesToRemove.sort((a, b) => b - a).forEach(lineIndex => {
            const contentLines = newContent.split('\n');
            contentLines.splice(lineIndex, 1);
            newContent = contentLines.join('\n');
          });
          
          // Replace first import line
          const contentLines = newContent.split('\n');
          const adjustedFirstLineIndex = firstImportLine.lineIndex - linesToRemove.filter(idx => idx < firstImportLine.lineIndex).length;
          contentLines[adjustedFirstLineIndex] = newImportLine;
          newContent = contentLines.join('\n');

          fixes.push({
            type: 'duplicateImports',
            source: source,
            originalLines: imports.map(imp => imp.originalLine),
            newLine: newImportLine,
            newContent: newContent,
            description: `Merged ${imports.length} duplicate imports from '${source}'`
          });
        }
      }
    });

    return fixes;
  }

  generateReport() {
    const report = [
      '# Duplicate Imports Fixes Report',
      '',
      `**Generated:** ${new Date().toLocaleString()}`,
      `**Files Processed:** ${this.results.filesProcessed}`,
      `**Files Fixed:** ${this.results.filesFixed}`,
      `**Total Fixes Applied:** ${this.results.totalFixes}`,
      '',
      '## 📊 Fixes Summary',
      '',
      '| Fix Type | Count | Description |',
      '|----------|-------|-------------|',
      `| duplicateImports | ${this.results.totalFixes} | Merged duplicate import statements |`,
      ''
    ];

    if (this.results.fixedFiles.length > 0) {
      report.push('## 📁 Fixed Files', '');
      
      this.results.fixedFiles.forEach(file => {
        report.push(`### ${file.path}`, '');
        report.push(`**Total fixes:** ${file.totalFixes}`, '');
        
        file.fixes.forEach(fix => {
          report.push(`- **${fix.type}**: ${fix.description}`);
          report.push(`  - **Source:** \`${fix.source}\``);
          report.push(`  - **Original:** ${fix.originalLines.length} separate import statements`);
          report.push(`  - **New:** \`${fix.newLine}\``);
        });
        
        report.push('');
      });
    }

    // Add best practices
    report.push(
      '## 📋 Best Practices',
      '',
      '### Import Organization',
      '',
      '```typescript',
      '// ✅ Good - Single import statement per source',
      "import { RTLView, RTLText, RTLIcon, RTLScrollView } from './RTL';",
      '',
      '// ❌ Bad - Multiple import statements from same source',
      "import { RTLView } from './RTL';",
      "import { RTLText, RTLIcon } from './RTL';",
      "import { RTLScrollView } from './RTL';",
      '```',
      '',
      '### Import Sorting',
      '',
      '1. **External libraries** (react, react-native, expo, etc.)',
      '2. **Internal utilities** (hooks, utils, services)',
      '3. **Components** (UI components, RTL components)',
      '4. **Constants and types** (theme, constants, types)',
      '',
      '## 🔧 Prevention',
      '',
      '- Use ESLint rules for import organization',
      '- Configure IDE to auto-organize imports',
      '- Regular code reviews to catch duplicates',
      '- Use this script in CI/CD pipeline'
    );

    return report.join('\n');
  }

  run() {
    console.log('🔍 Scanning for duplicate imports...');
    
    // Scan source directory
    if (fs.existsSync(config.srcDir)) {
      this.scanDirectory(config.srcDir);
    }

    // Generate and save report
    const report = this.generateReport();
    fs.writeFileSync(config.outputFile, report, 'utf8');
    
    console.log('\n📊 Summary:');
    console.log(`Files processed: ${this.results.filesProcessed}`);
    console.log(`Files fixed: ${this.results.filesFixed}`);
    console.log(`Total fixes: ${this.results.totalFixes}`);
    console.log(`Report saved: ${config.outputFile}`);
    
    if (this.results.totalFixes > 0) {
      console.log('\n✅ Duplicate imports have been fixed!');
    } else {
      console.log('\n✅ No duplicate imports found.');
    }
  }
}

// Run the fixer
const fixer = new DuplicateImportsFixer();
fixer.run();
