import React, { useState, useMemo } from 'react';
import { StyleSheet, RefreshControl } from 'react-native';
import { useThemedStyles, useProducts, useVendors, useI18n } from '../../hooks';
import { AdvancedFilterPanel, Card, EmptyState, FilterPanel, SearchBar, StatusBadge, type SearchResult, FilterOptions, AdvancedFilterOptions  } from '../../components';
import { RTLView, RTLText, RTLIcon, RTLSafeAreaView, RTLFlatList, RTLTouchableOpacity } from '../../components/RTL';
import {
  SPACING,
  FONT_SIZES,
  FONT_WEIGHTS,
  BORDER_RADIUS } from '../../constants/theme';
import { formatCurrency } from '../../utils';
import type ThemeColors  from '../../contexts/ThemeContext';
import type Product  from '../../contexts/DataContext';

interface SearchScreenProps {
  navigation: any;
  route?: {
    params?: {
      initialQuery?: string;
      category?: string;
    };
  };
}

export const SearchScreen: React.FC<SearchScreenProps> = ({ navigation, route }) => {
  const styles = useThemedStyles(createStyles);
  const { getAllProducts } = useProducts();
  const { vendors } = useVendors();
  const { t } = useI18n();
  
  const [searchQuery, setSearchQuery] = useState(route?.params?.initialQuery || '');
  const [showFilters, setShowFilters] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('list');
  
  const allProducts = getAllProducts();
  const allVendors = vendors;
  const activeProducts = allProducts.filter(product => product.isActive);
  
  // Calculate price range from all products
  const priceRange = useMemo(() => {
    const prices = activeProducts.map(p => p.price);
    return {
      min: Math.floor(Math.min(...prices, 0)),
      max: Math.ceil(Math.max(...prices, 1000)),
    };
  }, [activeProducts]);

  // Available vendors for filtering
  const availableVendors = useMemo(() => {
    const vendorMap = new Map();
    activeProducts.forEach(product => {
      const vendor = allVendors.find(v => v.id === product.vendorId);
      if (vendor && vendor.status === 'approved') {
        const existing = vendorMap.get(vendor.id);
        vendorMap.set(vendor.id, {
          id: vendor.id,
          name: vendor.businessName,
          productCount: (existing?.productCount || 0) + 1,
        });
      }
    });
    return Array.from(vendorMap.values());
  }, [activeProducts, allVendors]);

  const [filters, setFilters] = useState<AdvancedFilterOptions>({
    categories: route?.params?.category ? [route.params.category as any] : [],
    priceRange: { min: priceRange.min, max: priceRange.max },
    rating: 0,
    inStock: false,
    onSale: false,
    freeShipping: false,
    vendors: [],
    sortBy: 'relevance',
    availability: 'all',
    condition: 'all',
  });

  // Filter and sort products based on search query and filters
  const filteredProducts = useMemo(() => {
    let results = activeProducts;

    // Apply search query
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      results = results.filter(product =>
        product.name.toLowerCase().includes(query) ||
        product.description.toLowerCase().includes(query) ||
        product.tags.some(tag => tag.toLowerCase().includes(query)) ||
        product.category.toLowerCase().includes(query)
      );
    }

    // Apply filters
    if (filters.categories.length > 0) {
      results = results.filter(product => filters.categories.includes(product.category));
    }

    if (filters.priceRange.min > priceRange.min || filters.priceRange.max < priceRange.max) {
      results = results.filter(product => 
        product.price >= filters.priceRange.min && product.price <= filters.priceRange.max
      );
    }

    if (filters.rating > 0) {
      results = results.filter(product => product.rating >= filters.rating);
    }

    if (filters.inStock) {
      results = results.filter(product => product.inventory > 0);
    }

    if (filters.onSale) {
      results = results.filter(product => product.originalPrice && product.originalPrice > product.price);
    }

    if (filters.freeShipping) {
      // Assuming products have a freeShipping property or we can determine it
      results = results.filter(product => product.price > 50); // Free shipping over $50
    }

    if (filters.vendors.length > 0) {
      results = results.filter(product => filters.vendors.includes(product.vendorId));
    }

    // Apply availability filter
    if (filters.availability !== 'all') {
      switch (filters.availability) {
        case 'in_stock':
          results = results.filter(product => product.inventory > 10);
          break;
        case 'low_stock':
          results = results.filter(product => product.inventory > 0 && product.inventory <= 10);
          break;
        case 'out_of_stock':
          results = results.filter(product => product.inventory === 0);
          break;
      }
    }

    // Apply sorting
    switch (filters.sortBy) {
      case 'price_low':
        results.sort((a, b) => a.price - b.price);
        break;
      case 'price_high':
        results.sort((a, b) => b.price - a.price);
        break;
      case 'rating':
        results.sort((a, b) => b.rating - a.rating);
        break;
      case 'newest':
        results.sort((a, b) => new Date(b.createdAt || 0).getTime() - new Date(a.createdAt || 0).getTime());
        break;
      case 'popularity':
        results.sort((a, b) => (b.reviewCount || 0) - (a.reviewCount || 0));
        break;
      case 'discount':
        results.sort((a, b) => {
          const discountA = a.originalPrice ? ((a.originalPrice - a.price) / a.originalPrice) * 100 : 0;
          const discountB = b.originalPrice ? ((b.originalPrice - b.price) / b.originalPrice) * 100 : 0;
          return discountB - discountA;
        });
        break;
      default:
        // Relevance - keep original order for now
        break;
    }

    return results;
  }, [activeProducts, searchQuery, filters, priceRange]);

  const onRefresh = React.useCallback(async () => {
    setRefreshing(true);
    setTimeout(() => setRefreshing(false), 1000);
  }, []);

  const handleSearchResultPress = (result: SearchResult) => {
    if (result.type === 'product') {
      navigation.navigate('ProductDetails', { productId: result.id });
    } else if (result.type === 'vendor') {
      navigation.navigate('VendorShop', { vendorId: result.id });
    } else if (result.type === 'category') {
      setFilters(prev => ({ ...prev, categories: [result.id as any] }));
    }
  };

  const handleProductPress = (productId: string) => {
    navigation.navigate('ProductDetails', { productId });
  };

  const getActiveFiltersCount = () => {
    let count = 0;
    if (filters.categories.length > 0) count++;
    if (filters.priceRange.min > priceRange.min || filters.priceRange.max < priceRange.max) count++;
    if (filters.rating > 0) count++;
    if (filters.inStock) count++;
    if (filters.onSale) count++;
    if (filters.freeShipping) count++;
    if (filters.vendors.length > 0) count++;
    if (filters.availability !== 'all') count++;
    if (filters.condition !== 'all') count++;
    if (filters.sortBy !== 'relevance') count++;
    return count;
  };

  const handleAdvancedFiltersApply = () => {
    // Filters are already applied through the filteredProducts useMemo
    setShowFilters(false);
  };

  const handleAdvancedFiltersReset = () => {
    setFilters({
      categories: [],
      priceRange: { min: priceRange.min, max: priceRange.max },
      rating: 0,
      inStock: false,
      onSale: false,
      freeShipping: false,
      vendors: [],
      sortBy: 'relevance',
      availability: 'all',
      condition: 'all',
    });
  };

  const renderProduct = ({ item }: { item: Product }) => {
    const vendor = allVendors.find(v => v.id === item.vendorId);
    
    return (
      <Card style={styles.productCard} variant="outlined">
        <RTLTouchableOpacity
          style={styles.productContent}
          onPress={() => handleProductPress(item.id)}
        >
          <RTLView style={styles.productImage}>
            <RTLIcon name="image-outline" size={40} color="#CCCCCC" />
          </RTLView>

          <RTLView style={styles.productInfo}>
            <RTLText style={styles.productName} numberOfLines={2}>
              {item.name}
            </RTLText>
            <RTLText style={styles.productVendor} numberOfLines={1}>
              {t('search.by')} {vendor?.businessName || t('search.unknownVendor')}
            </RTLText>
            <RTLText style={styles.productDescription} numberOfLines={2}>
              {item.description}
            </RTLText>

            <RTLView style={styles.productFooter}>
              <RTLView style={styles.productPricing}>
                <RTLText style={styles.productPrice}>
                  {formatCurrency(item.price)}
                </RTLText>
                {item.originalPrice && item.originalPrice > item.price && (
                  <RTLText style={styles.productOriginalPrice}>
                    {formatCurrency(item.originalPrice)}
                  </RTLText>
                )}
              </RTLView>

              <RTLView style={styles.productMeta}>
                <RTLView style={styles.productRating}>
                  <RTLIcon name="star" size={14} color="#FFD700" />
                  <RTLText style={styles.productRatingText}>{(item.rating || 0).toFixed(1)}</RTLText>
                </RTLView>
                {item.inventory <= 10 && item.inventory > 0 && (
                  <StatusBadge status={t('products.lowStock')} customColor="#FF9800" size="small" />
                )}
              </RTLView>
            </RTLView>
          </RTLView>
        </RTLTouchableOpacity>
      </Card>
    );
  };

  const renderHeader = () => (
    <RTLView style={styles.header}>
      {/* Search Bar */}
      <SearchBar
        placeholder={t('search.searchProductsVendorsCategories')}
        onResultPress={handleSearchResultPress}
        onSearchChange={setSearchQuery}
        style={styles.searchBar}
      />

      {/* Controls */}
      <RTLView style={styles.controls}>
        <RTLView style={styles.resultsInfo}>
          <RTLText style={styles.resultsText}>
            {filteredProducts.length} {filteredProducts.length === 1 ? t('search.result') : t('search.results')}
            {searchQuery ? ` ${t('search.for')} "${searchQuery}"` : ''}
          </RTLText>
          {getActiveFiltersCount() > 0 && (
            <RTLText style={styles.filtersText}>
              {getActiveFiltersCount()} {getActiveFiltersCount() === 1 ? t('search.filterApplied') : t('search.filtersApplied')}
            </RTLText>
          )}
        </RTLView>

        <RTLView style={styles.controlButtons}>
          <RTLTouchableOpacity
            style={styles.filterButton}
            onPress={() => setShowFilters(true)}
          >
            <RTLIcon name="options-outline" size={20} color="#667eea" />
            {getActiveFiltersCount() > 0 && (
              <RTLView style={styles.filterBadge}>
                <RTLText style={styles.filterBadgeText}>{getActiveFiltersCount()}</RTLText>
              </RTLView>
            )}
          </RTLTouchableOpacity>

          <RTLTouchableOpacity
            style={styles.viewModeButton}
            onPress={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
          >
            <RTLIcon
              name={viewMode === 'grid' ? 'list-outline' : 'grid-outline'}
              size={20}
              color="#667eea"
            />
          </RTLTouchableOpacity>
        </RTLView>
      </RTLView>
    </RTLView>
  );

  return (
    <RTLSafeAreaView style={styles.container}>
      <RTLFlatList
        data={filteredProducts}
        renderItem={renderProduct}
        keyExtractor={(item) => item.id}
        ListHeaderComponent={renderHeader}
        ListEmptyComponent={
          <EmptyState
            icon="search-outline"
            title={t('search.noProductsFound')}
            description={
              searchQuery || getActiveFiltersCount() > 0
                ? t('search.adjustSearchFilters')
                : t('search.startSearchingToDiscover')
            }
          />
        }
        contentContainerStyle={styles.listContent}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      />

      {/* Advanced Filter Panel */}
      <AdvancedFilterPanel
        visible={showFilters}
        onClose={() => setShowFilters(false)}
        filters={filters}
        onFiltersChange={setFilters}
        priceRange={priceRange}
        availableVendors={availableVendors}
        onApply={handleAdvancedFiltersApply}
        onReset={handleAdvancedFiltersReset}
      />
    </RTLSafeAreaView>
  );
};

const createStyles = (colors: ThemeColors) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    listContent: {
      padding: SPACING.md,
    },
    header: {
      marginBottom: SPACING.lg,
    },
    searchBar: {
      marginBottom: SPACING.md,
    },
    controls: {
      flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    resultsInfo: {
      flex: 1,
    },
    resultsText: {
      fontSize: FONT_SIZES.md,
      fontWeight: FONT_WEIGHTS.medium,
      color: colors.textPrimary,
    },
    filtersText: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
      marginTop: SPACING.xs,
    },
    controlButtons: {
      flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
      gap: SPACING.sm,
    },
    filterButton: {
      position: 'relative',
      padding: SPACING.sm,
      borderRadius: BORDER_RADIUS.sm,
      backgroundColor: colors.surface,
      borderWidth: 1,
      borderColor: colors.border,
    },
    filterBadge: {
      position: 'absolute',
      top: -4,
      right: -4,
      backgroundColor: '#FF6B6B',
      borderRadius: 8,
      minWidth: 16,
      height: 16,
      justifyContent: 'center',
      alignItems: 'center',
    },
    filterBadgeText: {
      fontSize: FONT_SIZES.xs,
      fontWeight: FONT_WEIGHTS.bold,
      color: '#FFFFFF',
    },
    viewModeButton: {
      padding: SPACING.sm,
      borderRadius: BORDER_RADIUS.sm,
      backgroundColor: colors.surface,
      borderWidth: 1,
      borderColor: colors.border,
    },
    productCard: {
      marginBottom: SPACING.md,
    },
    productContent: {
      flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
      alignItems: 'flex-start',
    },
    productImage: {
      width: 80,
      height: 80,
      backgroundColor: colors.surface,
      borderRadius: BORDER_RADIUS.md,
      justifyContent: 'center',
      alignItems: 'center',
      marginHorizontal: SPACING.md,
      overflow: 'hidden', // Ensure content stays within bounds
      // Consistent spacing for RTL compatibility
      marginVertical: SPACING.xs,
    },
    productInfo: {
      flex: 1,
    },
    productName: {
      fontSize: FONT_SIZES.md,
      fontWeight: FONT_WEIGHTS.semibold,
      color: colors.textPrimary,
      marginBottom: SPACING.xs,
    },
    productVendor: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
      marginBottom: SPACING.xs,
    },
    productDescription: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
      lineHeight: 18,
      marginBottom: SPACING.sm,
    },
    productFooter: {
      flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
      justifyContent: 'space-between',
      alignItems: 'flex-end',
    },
    productPricing: {
      flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
      alignItems: 'center',
      justifyContent: 'flex-start', // This will be flipped in RTL
    },
    productPrice: {
      fontSize: FONT_SIZES.lg,
      fontWeight: FONT_WEIGHTS.bold,
      color: '#667eea',
      marginHorizontal: SPACING.sm,
      textAlign: 'left', // RTL: Will be automatically flipped by RTLText component
    },
    productOriginalPrice: {
      fontSize: FONT_SIZES.md,
      color: colors.textSecondary,
      textDecorationLine: 'line-through',
      textAlign: 'left', // RTL: Will be automatically flipped by RTLText component
    },
    productMeta: {
      alignItems: 'flex-end',
    },
    productRating: {
      flexDirection: 'row', // This will be flipped to 'row-reverse' in RTL by RTLView
      alignItems: 'center',
      marginBottom: SPACING.xs,
      justifyContent: 'flex-start', // This will be flipped in RTL
    },
    productRatingText: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
      marginHorizontal: SPACING.xs,
    },
  });
