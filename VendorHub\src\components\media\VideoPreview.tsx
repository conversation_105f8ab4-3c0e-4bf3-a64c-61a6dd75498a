import React, { useState, useRef } from 'react';
import { StyleSheet, Animated, ViewStyle } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';

// Fallback types for expo-av
type ResizeMode = 'contain' | 'cover' | 'stretch';
const ResizeMode = {
  CONTAIN: 'contain' as ResizeMode,
  COVER: 'cover' as ResizeMode,
  STRETCH: 'stretch' as ResizeMode,
};
import { RTLView, RTLText, RTLTouchableOpacity, RTLIcon } from '../RTL';
import { useThemedStyles } from '../../hooks';
import { SPACING, FONT_SIZES, FONT_WEIGHTS, BORDER_RADIUS } from '../../constants/theme';
import { SmartImage } from './SmartImage';
import { AnimatedPressable, FadeInView } from '../visual/MicroAnimations';
import type { ThemeColors } from '../../contexts/ThemeContext';

interface VideoPreviewProps {
  videoUri: string;
  thumbnailUri?: string;
  style?: ViewStyle;
  aspectRatio?: number;
  autoPlay?: boolean;
  loop?: boolean;
  muted?: boolean;
  showControls?: boolean;
  resizeMode?: ResizeMode;
  onPlay?: () => void;
  onPause?: () => void;
  onEnd?: () => void;
  overlayContent?: React.ReactNode;
  borderRadius?: number;
}

export const VideoPreview: React.FC<VideoPreviewProps> = ({
  videoUri,
  thumbnailUri,
  style,
  aspectRatio = 16/9,
  autoPlay = false,
  loop = false,
  muted = true,
  showControls = true,
  resizeMode = ResizeMode.COVER,
  onPlay,
  onPause,
  onEnd,
  overlayContent,
  borderRadius = BORDER_RADIUS.lg,
}) => {
  const styles = useThemedStyles(createStyles);
  const [isPlaying, setIsPlaying] = useState(autoPlay);
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [showThumbnail, setShowThumbnail] = useState(!autoPlay);
  const [duration, setDuration] = useState(0);
  const [position, setPosition] = useState(0);
  
  const videoRef = useRef<any>(null);
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(1)).current;

  const handlePlayPress = async () => {
    // Fallback implementation - just toggle state without actual video playback
    try {
      if (isPlaying) {
        setIsPlaying(false);
        onPause?.();
      } else {
        setShowThumbnail(false);
        setIsPlaying(true);
        onPlay?.();

        // Fade in video placeholder
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }).start();
      }
    } catch (error) {
      console.error('Video playback error:', error);
      setHasError(true);
    }
  };

  const handleVideoLoad = (status: any) => {
    // Fallback implementation
    setIsLoading(false);
    setDuration(30000); // Default 30 seconds
  };

  const handlePlaybackStatusUpdate = (status: any) => {
    // Fallback implementation - simulate playback
    if (isPlaying) {
      const newPosition = Math.min(position + 1000, duration);
      setPosition(newPosition);

      if (newPosition >= duration) {
        setIsPlaying(false);
        setPosition(0);
        onEnd?.();

        if (!loop) {
          setShowThumbnail(true);
          fadeAnim.setValue(0);
        }
      }
    }
  };

  const handleVideoError = () => {
    setHasError(true);
    setIsLoading(false);
  };

  const formatTime = (milliseconds: number) => {
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const progressPercentage = duration > 0 ? (position / duration) * 100 : 0;

  const renderPlayButton = () => (
    <AnimatedPressable
      style={styles.playButtonContainer}
      onPress={handlePlayPress}
      scaleValue={0.9}
    >
      <LinearGradient
        colors={['rgba(0, 0, 0, 0.6)', 'rgba(0, 0, 0, 0.8)']}
        style={styles.playButton}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <RTLIcon 
          name={isPlaying ? 'pause' : 'play'} 
          size={32} 
          color="#FFFFFF" 
        />
      </LinearGradient>
    </AnimatedPressable>
  );

  const renderControls = () => {
    if (!showControls || showThumbnail) return null;

    return (
      <FadeInView duration={300} style={styles.controlsContainer}>
        <LinearGradient
          colors={['transparent', 'rgba(0, 0, 0, 0.7)']}
          style={styles.controlsGradient}
        >
          {/* Progress Bar */}
          <RTLView style={styles.progressContainer}>
            <RTLView style={styles.progressBar}>
              <RTLView 
                style={[
                  styles.progressFill, 
                  { width: `${progressPercentage}%` }
                ]} 
              />
            </RTLView>
            <RTLText style={styles.timeText}>
              {formatTime(position)} / {formatTime(duration)}
            </RTLText>
          </RTLView>

          {/* Control Buttons */}
          <RTLView style={styles.controlButtons}>
            <RTLTouchableOpacity
              style={styles.controlButton}
              onPress={handlePlayPress}
            >
              <RTLIcon 
                name={isPlaying ? 'pause' : 'play'} 
                size={20} 
                color="#FFFFFF" 
              />
            </RTLTouchableOpacity>
            
            <RTLTouchableOpacity
              style={styles.controlButton}
              onPress={() => {
                // Fallback: just reset position state
                setPosition(0);
              }}
            >
              <RTLIcon name="refresh" size={18} color="#FFFFFF" />
            </RTLTouchableOpacity>
          </RTLView>
        </LinearGradient>
      </FadeInView>
    );
  };

  const renderError = () => (
    <RTLView style={[styles.errorContainer, { borderRadius }]}>
      <RTLIcon name="videocam-off-outline" size={48} color="rgba(239, 68, 68, 0.7)" />
      <RTLText style={styles.errorText}>Video unavailable</RTLText>
    </RTLView>
  );

  const containerStyle = [
    styles.container,
    { aspectRatio, borderRadius },
    style,
  ];

  if (hasError) {
    return <RTLView style={containerStyle}>{renderError()}</RTLView>;
  }

  return (
    <RTLView style={containerStyle}>
      {/* Thumbnail */}
      {showThumbnail && thumbnailUri && (
        <SmartImage
          source={{ uri: thumbnailUri }}
          style={styles.thumbnail}
          aspectRatio={aspectRatio}
          borderRadius={borderRadius}
          overlayGradient={['transparent', 'rgba(0, 0, 0, 0.3)']}
        >
          {renderPlayButton()}
        </SmartImage>
      )}

      {/* Video */}
      {!showThumbnail && (
        <Animated.View 
          style={[
            styles.videoContainer,
            { 
              opacity: fadeAnim,
              transform: [{ scale: scaleAnim }],
              borderRadius,
            }
          ]}
        >
          {/* Video component requires expo-av package */}
          <RTLView style={[styles.video, styles.videoFallback]}>
            <RTLIcon name="videocam-outline" size={48} color="rgba(255, 255, 255, 0.7)" />
            <RTLText style={styles.fallbackText}>Video Preview</RTLText>
            <RTLText style={styles.fallbackSubtext}>
              Install expo-av package for video playback
            </RTLText>
          </RTLView>
          
          {/* Video Controls Overlay */}
          {renderControls()}
          
          {/* Custom Overlay Content */}
          {overlayContent && (
            <RTLView style={styles.overlayContent}>
              {overlayContent}
            </RTLView>
          )}
        </Animated.View>
      )}

      {/* Loading State */}
      {isLoading && !showThumbnail && (
        <RTLView style={styles.loadingContainer}>
          <RTLIcon name="refresh" size={32} color="rgba(255, 255, 255, 0.7)" />
          <RTLText style={styles.loadingText}>Loading video...</RTLText>
        </RTLView>
      )}
    </RTLView>
  );
};

const createStyles = (colors: ThemeColors) => StyleSheet.create({
  container: {
    overflow: 'hidden',
    backgroundColor: colors.backgroundSecondary,
    position: 'relative',
  },
  thumbnail: {
    ...StyleSheet.absoluteFillObject,
  },
  videoContainer: {
    ...StyleSheet.absoluteFillObject,
  },
  video: {
    ...StyleSheet.absoluteFillObject,
  },
  videoFallback: {
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    alignItems: 'center',
    justifyContent: 'center',
    padding: SPACING.lg,
  },
  fallbackText: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.semiBold,
    color: '#FFFFFF',
    marginTop: SPACING.md,
    textAlign: 'center',
  },
  fallbackSubtext: {
    fontSize: FONT_SIZES.sm,
    color: 'rgba(255, 255, 255, 0.7)',
    marginTop: SPACING.sm,
    textAlign: 'center',
  },
  playButtonContainer: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [{ translateX: -35 }, { translateY: -35 }],
    zIndex: 2,
  },
  playButton: {
    width: 70,
    height: 70,
    borderRadius: 35,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.8)',
  },
  controlsContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    zIndex: 3,
  },
  controlsGradient: {
    padding: SPACING.md,
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  progressBar: {
    flex: 1,
    height: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 2,
    marginRight: SPACING.sm,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#3B82F6',
    borderRadius: 2,
  },
  timeText: {
    fontSize: FONT_SIZES.xs,
    color: '#FFFFFF',
    fontWeight: FONT_WEIGHTS.medium,
    minWidth: 80,
    textAlign: 'right',
  },
  controlButtons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  controlButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: SPACING.sm,
  },
  overlayContent: {
    position: 'absolute',
    top: SPACING.md,
    left: SPACING.md,
    right: SPACING.md,
    zIndex: 4,
  },
  loadingContainer: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  loadingText: {
    fontSize: FONT_SIZES.sm,
    color: 'rgba(255, 255, 255, 0.8)',
    marginTop: SPACING.sm,
  },
  errorContainer: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(239, 68, 68, 0.1)',
    borderWidth: 1,
    borderColor: 'rgba(239, 68, 68, 0.3)',
    borderStyle: 'dashed',
  },
  errorText: {
    fontSize: FONT_SIZES.sm,
    color: 'rgba(239, 68, 68, 0.8)',
    marginTop: SPACING.sm,
    fontWeight: FONT_WEIGHTS.medium,
  },
});
