#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Configuration
const config = {
  srcDir: path.join(__dirname, '../src'),
  appDir: path.join(__dirname, '../app'),
  componentsDir: path.join(__dirname, '../components'),
  excludePatterns: [
    /node_modules/,
    /\.git/,
    /\.expo/,
    /dist/,
    /build/,
    /coverage/,
    /\.DS_Store/,
    /\.env/,
    /package-lock\.json/,
    /yarn\.lock/,
    /\.log$/,
    /\.md$/,
    /\.json$/,
    /\.js\.map$/,
    /\.d\.ts$/,
    /constants/,
    /utils/,
    /services/,
    /hooks/,
    /contexts/,
    /navigation/,
    /scripts/,
    /assets/,
  ],
  fileExtensions: ['.tsx', '.ts', '.jsx', '.js'],
};

class PeriodIssueFinder {
  constructor() {
    this.results = [];
  }

  shouldExcludeFile(filePath) {
    const relativePath = path.relative(process.cwd(), filePath);
    return config.excludePatterns.some(pattern => pattern.test(relativePath));
  }

  hasValidExtension(filePath) {
    return config.fileExtensions.some(ext => filePath.endsWith(ext));
  }

  analyzeFileContent(filePath, content) {
    const relativePath = path.relative(process.cwd(), filePath);
    const lines = content.split('\n');
    
    lines.forEach((line, index) => {
      const lineNumber = index + 1;
      const trimmedLine = line.trim();
      
      // Look for specific patterns that could cause a period to be rendered as text
      const suspiciousPatterns = [
        // Period in JSX expression
        {
          regex: />\s*\{[^}]*\.\s*[^}]*\}\s*</g,
          description: 'JSX expression containing period - might render as text node'
        },
        // Conditional with period
        {
          regex: />\s*\{[^}]*\?\s*[^:]*\.\s*[^:]*:/g,
          description: 'Conditional expression with period - might render as text node'
        },
        // Template literal with period
        {
          regex: />\s*\{[^}]*`[^`]*\.[^`]*`[^}]*\}\s*</g,
          description: 'Template literal with period - might render as text node'
        },
        // String concatenation with period
        {
          regex: />\s*\{[^}]*\+\s*['"`]\.[^'"`]*['"`][^}]*\}\s*</g,
          description: 'String concatenation with period - might render as text node'
        },
        // Object property access that might return string with period
        {
          regex: />\s*\{[a-zA-Z_$][a-zA-Z0-9_$]*\.[a-zA-Z_$][a-zA-Z0-9_$]*\}\s*</g,
          description: 'Object property access - ensure it returns JSX or is wrapped in Text'
        },
        // Function call with period in result
        {
          regex: />\s*\{[a-zA-Z_$][a-zA-Z0-9_$]*\([^)]*\)\.[a-zA-Z_$][a-zA-Z0-9_$]*\}\s*</g,
          description: 'Function call with property access - might return string with period'
        },
        // Direct period character
        {
          regex: />\s*\.\s*</g,
          description: 'Direct period character in JSX - will render as text node'
        },
        // Period in string literal
        {
          regex: />\s*['"`]\.[^'"`]*['"`]\s*</g,
          description: 'String literal starting with period - needs Text wrapper'
        }
      ];

      suspiciousPatterns.forEach(pattern => {
        const matches = trimmedLine.match(pattern.regex);
        if (matches) {
          // Skip if it's already inside a Text component
          if (!trimmedLine.includes('<RTLText') && !trimmedLine.includes('<Text')) {
            this.results.push({
              file: relativePath,
              line: lineNumber,
              content: trimmedLine,
              description: pattern.description,
              matches: matches
            });
          }
        }
      });

      // Special check for common problematic patterns
      if (trimmedLine.includes('>.') || trimmedLine.includes('. <')) {
        this.results.push({
          file: relativePath,
          line: lineNumber,
          content: trimmedLine,
          description: 'Line contains period near JSX boundaries - potential text node issue',
          matches: ['period near JSX']
        });
      }
    });
  }

  scanDirectory(dirPath) {
    if (!fs.existsSync(dirPath)) {
      return;
    }

    const items = fs.readdirSync(dirPath);

    items.forEach(item => {
      const itemPath = path.join(dirPath, item);
      const stat = fs.statSync(itemPath);

      if (stat.isDirectory()) {
        this.scanDirectory(itemPath);
      } else if (stat.isFile()) {
        if (this.hasValidExtension(itemPath) && !this.shouldExcludeFile(itemPath)) {
          try {
            const content = fs.readFileSync(itemPath, 'utf8');
            this.analyzeFileContent(itemPath, content);
          } catch (error) {
            console.warn(`Warning: Could not read file ${itemPath}: ${error.message}`);
          }
        }
      }
    });
  }

  run() {
    console.log('🔍 Scanning for period-related text node issues...');
    
    // Scan directories
    [config.srcDir, config.appDir, config.componentsDir].forEach(dir => {
      if (fs.existsSync(dir)) {
        console.log(`Scanning: ${dir}`);
        this.scanDirectory(dir);
      }
    });

    console.log(`\n📊 Found ${this.results.length} potential period-related issues:`);

    if (this.results.length === 0) {
      console.log('🎉 No period-related text node issues found!');
      return 0;
    }

    // Group by file
    const groupedResults = {};
    this.results.forEach(result => {
      if (!groupedResults[result.file]) {
        groupedResults[result.file] = [];
      }
      groupedResults[result.file].push(result);
    });

    Object.entries(groupedResults).forEach(([file, issues]) => {
      console.log(`\n📄 ${file}:`);
      issues.forEach(issue => {
        console.log(`  ⚠️  Line ${issue.line}: ${issue.description}`);
        console.log(`      ${issue.content}`);
        if (issue.matches && issue.matches.length > 0) {
          console.log(`      Matches: ${issue.matches.join(', ')}`);
        }
      });
    });

    console.log('\n💡 Recommendations:');
    console.log('1. Wrap any string content in <RTLText> components');
    console.log('2. Ensure conditional expressions return JSX elements, not strings');
    console.log('3. Check object property access to ensure it returns JSX or is wrapped');
    console.log('4. Remove any direct period characters in JSX');

    return this.results.length > 0 ? 1 : 0;
  }
}

// Run the finder
const finder = new PeriodIssueFinder();
const exitCode = finder.run();
process.exit(exitCode);
