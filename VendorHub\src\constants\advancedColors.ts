/**
 * Advanced Color System - Exclusive Blue-Navy-Black Theme
 * Premium color palette for VendorHub application
 */

// Extended Blue Palette
export const BLUE_PALETTE = {
  // Ice Blues (Very Light)
  ice50: '#F0F9FF',
  ice100: '#E0F2FE',
  ice200: '#BAE6FD',
  ice300: '#7DD3FC',
  
  // Sky Blues (Light)
  sky400: '#38BDF8',
  sky500: '#0EA5E9',
  sky600: '#0284C7',
  sky700: '#0369A1',
  
  // Ocean Blues (Medium)
  ocean400: '#60A5FA',
  ocean500: '#3B82F6',
  ocean600: '#2563EB',
  ocean700: '#1D4ED8',
  
  // Royal Blues (Deep)
  royal600: '#1E40AF',
  royal700: '#1E3A8A',
  royal800: '#1E3A8A',
  royal900: '#172554',
  
  // Midnight Blues (Very Deep)
  midnight800: '#1E293B',
  midnight900: '#0F172A',
  midnight950: '#020617',
};

// Extended Navy Palette
export const NAVY_PALETTE = {
  // Light Navy
  light50: '#F8FAFC',
  light100: '#F1F5F9',
  light200: '#E2E8F0',
  light300: '#CBD5E1',
  
  // Medium Navy
  medium400: '#94A3B8',
  medium500: '#64748B',
  medium600: '#475569',
  medium700: '#334155',
  
  // Dark Navy
  dark800: '#1E293B',
  dark900: '#0F172A',
  dark950: '#020617',
};

// Premium Gradients Collection
export const PREMIUM_GRADIENTS = {
  // Hero Gradients
  heroMain: ['#1E3A8A', '#0F172A', '#000000'] as const,
  heroLight: ['#3B82F6', '#1E3A8A', '#0F172A'] as const,
  heroReverse: ['#000000', '#0F172A', '#1E3A8A'] as const,
  
  // Card Gradients
  cardElevated: ['#FFFFFF', '#F8FAFC', '#F1F5F9'] as const,
  cardDark: ['#334155', '#1E293B', '#0F172A'] as const,
  cardGlass: ['rgba(248, 250, 252, 0.8)', 'rgba(241, 245, 249, 0.6)'] as const,
  
  // Button Gradients
  buttonPrimary: ['#1E3A8A', '#1D4ED8'] as const,
  buttonSecondary: ['#3B82F6', '#2563EB'] as const,
  buttonSuccess: ['#059669', '#047857'] as const,
  buttonDanger: ['#DC2626', '#B91C1C'] as const,
  
  // Background Gradients
  backgroundLight: ['#FFFFFF', '#F8FAFC'] as const,
  backgroundDark: ['#0F172A', '#1E293B'] as const,
  backgroundMidnight: ['#000000', '#0F172A'] as const,

  // Dark Attractive Gradients (Navy + Black)
  darkElegant: ['#000000', '#0F172A', '#1E293B'] as const,
  darkMystic: ['#020617', '#0F172A', '#334155'] as const,
  darkRoyal: ['#000000', '#1E3A8A', '#0F172A'] as const,
  darkOcean: ['#0F172A', '#1E40AF', '#000000'] as const,
  darkStorm: ['#111827', '#1E293B', '#0F172A'] as const,
  darkVoid: ['#000000', '#111827', '#1F2937'] as const,
  darkAbyss: ['#020617', '#0F172A', '#1E293B', '#334155'] as const,
  darkNebula: ['#000000', '#0F172A', '#1E3A8A', '#1E293B'] as const,
  
  // Special Effects
  aurora: ['#1E3A8A', '#3B82F6', '#0EA5E9', '#38BDF8'] as const,
  ocean: ['#0F172A', '#1E3A8A', '#1D4ED8', '#3B82F6'] as const,
  depth: ['#000000', '#0F172A', '#1E293B', '#334155'] as const,

  // Missing gradients used throughout the app
  royalSpotlight: ['#1D4ED8', '#3B82F6', '#0EA5E9'] as const,
  elegantDepth: ['#0F172A', '#1E293B', '#334155'] as const,
  subtleElegance: ['#1E293B', '#334155', '#475569'] as const,
  mysticAurora: ['#1E3A8A', '#1D4ED8', '#3B82F6'] as const,
  oceanDepth: ['#0F172A', '#1E40AF', '#1D4ED8'] as const,
  royalElegance: ['#1E3A8A', '#1D4ED8', '#0F172A'] as const,
  vibrantEnergy: ['#3B82F6', '#1D4ED8', '#1E40AF'] as const,
  
  // Glass Morphism
  glassLight: ['rgba(255, 255, 255, 0.25)', 'rgba(248, 250, 252, 0.1)'] as const,
  glassDark: ['rgba(15, 23, 42, 0.8)', 'rgba(30, 41, 59, 0.6)'] as const,
  glassBlue: ['rgba(30, 58, 138, 0.2)', 'rgba(59, 130, 246, 0.1)'] as const,

  // Advanced Dark Glass Effects
  glassDarkElegant: ['rgba(0, 0, 0, 0.9)', 'rgba(15, 23, 42, 0.7)', 'rgba(30, 41, 59, 0.5)'] as const,
  glassDarkMystic: ['rgba(2, 6, 23, 0.95)', 'rgba(15, 23, 42, 0.8)', 'rgba(51, 65, 85, 0.6)'] as const,
  glassDarkRoyal: ['rgba(0, 0, 0, 0.9)', 'rgba(30, 58, 138, 0.3)', 'rgba(15, 23, 42, 0.8)'] as const,
};

// Shadow Configurations
export const PREMIUM_SHADOWS = {
  // Light Shadows
  lightSoft: {
    shadowColor: '#1E3A8A',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  lightMedium: {
    shadowColor: '#1E3A8A',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 6,
  },
  lightStrong: {
    shadowColor: '#1E3A8A',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.2,
    shadowRadius: 16,
    elevation: 12,
  },
  
  // Dark Shadows
  darkSoft: {
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 3,
  },
  darkMedium: {
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.4,
    shadowRadius: 8,
    elevation: 6,
  },
  darkStrong: {
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.5,
    shadowRadius: 16,
    elevation: 12,
  },
  
  // Colored Shadows
  blueShadow: {
    shadowColor: '#3B82F6',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  navyShadow: {
    shadowColor: '#1E293B',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.4,
    shadowRadius: 8,
    elevation: 6,
  },
};

// Status Colors with Blue Theme
export const STATUS_COLORS = {
  // Success States
  successLight: '#ECFDF5',
  success: '#059669',
  successDark: '#047857',
  
  // Warning States
  warningLight: '#FFFBEB',
  warning: '#D97706',
  warningDark: '#B45309',
  
  // Error States
  errorLight: '#FEF2F2',
  error: '#DC2626',
  errorDark: '#B91C1C',
  
  // Info States (Blue themed)
  infoLight: '#F0F9FF',
  info: '#0EA5E9',
  infoDark: '#0284C7',
  
  // Neutral States
  neutralLight: '#F8FAFC',
  neutral: '#64748B',
  neutralDark: '#334155',
};

// Interactive States
export const INTERACTIVE_COLORS = {
  // Hover States
  hoverLight: 'rgba(30, 58, 138, 0.05)',
  hover: 'rgba(30, 58, 138, 0.1)',
  hoverDark: 'rgba(30, 58, 138, 0.15)',
  
  // Active States
  activeLight: 'rgba(30, 58, 138, 0.1)',
  active: 'rgba(30, 58, 138, 0.2)',
  activeDark: 'rgba(30, 58, 138, 0.3)',
  
  // Focus States
  focusLight: 'rgba(59, 130, 246, 0.2)',
  focus: 'rgba(59, 130, 246, 0.3)',
  focusDark: 'rgba(59, 130, 246, 0.4)',
  
  // Disabled States
  disabledLight: 'rgba(148, 163, 184, 0.3)',
  disabled: 'rgba(148, 163, 184, 0.5)',
  disabledDark: 'rgba(148, 163, 184, 0.7)',
};

// Brand Colors
export const BRAND_COLORS = {
  primary: '#1E3A8A',      // Deep Blue
  secondary: '#0F172A',    // Black Navy
  accent: '#3B82F6',       // Bright Blue
  highlight: '#0EA5E9',    // Sky Blue
  
  // Brand Variations
  primaryLight: '#3B82F6',
  primaryDark: '#172554',
  secondaryLight: '#1E293B',
  secondaryDark: '#020617',
};

export default {
  BLUE_PALETTE,
  NAVY_PALETTE,
  PREMIUM_GRADIENTS,
  PREMIUM_SHADOWS,
  STATUS_COLORS,
  INTERACTIVE_COLORS,
  BRAND_COLORS,
};
