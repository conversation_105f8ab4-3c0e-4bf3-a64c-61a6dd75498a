import React, { useState } from 'react';
import {
  StyleSheet,
  Alert,
  KeyboardAvoidingView,
  Platform } from 'react-native';
import { useThemedStyles, useAuth, useProducts, useI18n } from '../../hooks';
import { Card, Button, Input, ImagePicker } from '../../components';
import { RTLView, RTLText, RTLIcon, RTLSafeAreaView, RTLScrollView, RTLTouchableOpacity } from '../../components/RTL';
import {
  SPACING,
  FONT_SIZES,
  FONT_WEIGHTS,
  BORDER_RADIUS } from '../../constants/theme';
import { PRODUCT_CATEGORIES, type ProductCategory } from '../../constants';
import type { ThemeColors } from '../../contexts/ThemeContext';

interface AddProductScreenProps {
  navigation: any;
}

interface ProductFormData {
  name: string;
  description: string;
  price: string;
  originalPrice: string;
  category: ProductCategory | '';
  inventory: string;
  tags: string;
  specifications: { [key: string]: string };
  images: string[];
}

export const AddProductScreen: React.FC<AddProductScreenProps> = ({ navigation }) => {
  const styles = useThemedStyles(createStyles);
  const { user } = useAuth();
  const { addProduct } = useProducts();
  const { t } = useI18n();
  
  const [formData, setFormData] = useState<ProductFormData>({
    name: '',
    description: '',
    price: '',
    originalPrice: '',
    category: '',
    inventory: '',
    tags: '',
    specifications: {},
    images: [],
  });

  const [errors, setErrors] = useState<Partial<ProductFormData>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [showCategoryPicker, setShowCategoryPicker] = useState(false);

  const validateForm = (): boolean => {
    const newErrors: Partial<ProductFormData> = {};

    if (!formData.name.trim()) {
      newErrors.name = t('products.productNameRequired');
    }

    if (!formData.description.trim()) {
      newErrors.description = t('products.productDescriptionRequired');
    }

    if (!formData.price.trim()) {
      newErrors.price = t('products.priceRequired');
    } else if (isNaN(Number(formData.price)) || Number(formData.price) <= 0) {
      newErrors.price = t('products.validPriceRequired');
    }

    if (formData.originalPrice && (isNaN(Number(formData.originalPrice)) || Number(formData.originalPrice) <= 0)) {
      newErrors.originalPrice = t('products.validOriginalPriceRequired');
    }

    if (!formData.category) {
      newErrors.category = t('products.categoryRequired') as any;
    }

    if (!formData.inventory.trim()) {
      newErrors.inventory = t('products.inventoryRequired');
    } else if (isNaN(Number(formData.inventory)) || Number(formData.inventory) < 0) {
      newErrors.inventory = t('products.validInventoryRequired');
    }

    if (formData.images.length === 0) {
      newErrors.images = [t('products.imageRequired')] as any;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm() || !user) return;

    setIsLoading(true);
    try {
      const productData = {
        vendorId: user.id,
        name: formData.name.trim(),
        description: formData.description.trim(),
        price: Number(formData.price),
        originalPrice: formData.originalPrice ? Number(formData.originalPrice) : undefined,
        category: formData.category as ProductCategory,
        inventory: Number(formData.inventory),
        tags: formData.tags.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0),
        specifications: formData.specifications,
        images: formData.images.length > 0 ? formData.images : ['https://via.placeholder.com/400x400/667eea/ffffff?text=Product'],
        isActive: true,
        rating: 0,
        reviewCount: 0,
      };

      await addProduct(productData);
      
      Alert.alert(
        t('common.success'),
        t('products.productAddedSuccessfully'),
        [
          {
            text: t('common.done'),
            onPress: () => navigation.goBack(),
          },
        ]
      );
    } catch (error) {
      Alert.alert(t('common.error'), t('products.failedToAddProduct'));
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (field: keyof ProductFormData, value: string | string[]) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const handleCategorySelect = (category: ProductCategory) => {
    setFormData(prev => ({ ...prev, category }));
    setShowCategoryPicker(false);
    if (errors.category) {
      setErrors(prev => ({ ...prev, category: undefined }));
    }
  };

  const renderCategoryPicker = () => (
    <Card style={styles.categoryPicker} variant="elevated">
      <RTLText style={styles.categoryPickerTitle}>{t('products.selectCategory')}</RTLText>
      {Object.values(PRODUCT_CATEGORIES).map((category) => (
        <RTLTouchableOpacity
          key={category}
          style={styles.categoryOption}
          onPress={() => handleCategorySelect(category)}
        >
          <RTLText style={styles.categoryOptionText}>{category}</RTLText>
          <RTLIcon name="chevron-forward" size={20} color="#CCCCCC" />
        </RTLTouchableOpacity>
      ))}
      <Button
        title={t('common.cancel')}
        onPress={() => setShowCategoryPicker(false)}
        variant="outline"
        style={styles.cancelButton}
      />
    </Card>
  );

  return (
    <RTLSafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        style={styles.keyboardAvoid}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <RTLScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
          <RTLView style={styles.content}>
            {/* Header */}
            <RTLView style={styles.header}>
              <RTLText style={styles.title}>{t('products.addProduct')}</RTLText>
              <RTLText style={styles.subtitle}>
                Fill in the details below to add a new product to your shop
              </RTLText>
            </RTLView>

            {/* Basic Information */}
            <Card style={styles.section} variant="outlined">
              <RTLText style={styles.sectionTitle}>{t('products.basicInformation')}</RTLText>

              <Input
                label={`${t('products.productName')} *`}
                placeholder={t('products.productNamePlaceholder')}
                value={formData.name}
                onChangeText={(value) => handleInputChange('name', value)}
                error={errors.name}
                style={styles.input}
              />

              <Input
                label={`${t('products.productDescription')} *`}
                placeholder={t('products.productDescriptionPlaceholder')}
                value={formData.description}
                onChangeText={(value) => handleInputChange('description', value)}
                error={errors.description}
                multiline
                numberOfLines={4}
                style={styles.input}
              />

              <RTLTouchableOpacity
                style={styles.categorySelector}
                onPress={() => setShowCategoryPicker(true)}
              >
                <RTLText style={styles.categoryLabel}>{`${t('products.productCategory')} *`}</RTLText>
                <RTLView style={styles.categoryValue}>
                  <RTLText style={[styles.categoryText, !formData.category && styles.placeholderText].filter(Boolean) as any}>
                    {formData.category || t('products.selectCategory')}
                  </RTLText>
                  <RTLIcon name="chevron-down" size={20} color="#CCCCCC" />
                </RTLView>
                {errors.category && (
                  <RTLText style={styles.errorText}>{errors.category}</RTLText>
                )}
              </RTLTouchableOpacity>
            </Card>

            {/* Pricing */}
            <Card style={styles.section} variant="outlined">
              <RTLText style={styles.sectionTitle}>{t('products.pricingInventory')}</RTLText>

              <Input
                label={`${t('products.productPrice')} *`}
                placeholder={t('products.productPricePlaceholder')}
                value={formData.price}
                onChangeText={(value) => handleInputChange('price', value)}
                error={errors.price}
                keyboardType="numeric"

                style={styles.input}
              />

              <Input
                label={t('products.productOriginalPrice')}
                placeholder={t('products.productOriginalPricePlaceholder')}
                value={formData.originalPrice}
                onChangeText={(value) => handleInputChange('originalPrice', value)}
                error={errors.originalPrice}
                keyboardType="numeric"

                style={styles.input}
              />

              <RTLText style={styles.helperText}>
                Set an original price higher than the current price to show a discount
              </RTLText>
            </Card>

            {/* Inventory */}
            <Card style={styles.section} variant="outlined">
              <RTLText style={styles.sectionTitle}>{t('products.productInventory')}</RTLText>

              <Input
                label={`${t('products.productInventory')} *`}
                placeholder={t('products.productInventoryPlaceholder')}
                value={formData.inventory}
                onChangeText={(value) => handleInputChange('inventory', value)}
                error={errors.inventory}
                keyboardType="numeric"
                style={styles.input}
              />
            </Card>

            {/* Product Images */}
            <Card style={styles.section} variant="outlined">
              <RTLText style={styles.sectionTitle}>{t('products.productImages')}</RTLText>

              <ImagePicker
                images={formData.images}
                onImagesChange={(images) => handleInputChange('images', images)}
                maxImages={5}
                title={t('products.productImages')}
                subtitle={t('products.addUpToImages')}
              />

              {errors.images && (
                <RTLText style={styles.errorText}>{errors.images}</RTLText>
              )}

              <RTLText style={styles.helperText}>
                The first image will be used as the main product image. High-quality images help increase sales.
              </RTLText>
            </Card>

            {/* Additional Details */}
            <Card style={styles.section} variant="outlined">
              <RTLText style={styles.sectionTitle}>{t('products.additionalDetails')}</RTLText>

              <Input
                label={t('products.productTags')}
                placeholder={t('products.productTagsPlaceholder')}
                value={formData.tags}
                onChangeText={(value) => handleInputChange('tags', value)}
                style={styles.input}
              />

              <RTLText style={styles.helperText}>
                Separate tags with commas to help customers find your product
              </RTLText>
            </Card>

            {/* Action Buttons */}
            <RTLView style={styles.actionButtons}>
              <Button
                title={t('common.cancel')}
                onPress={() => navigation.goBack()}
                variant="outline"
                style={styles.cancelActionButton}
              />
              <Button
                title={t('products.addProduct')}
                onPress={handleSubmit}
                loading={isLoading}
                style={styles.submitButton}
                leftIcon={<RTLIcon name="add-outline" size={20} color="#FFFFFF" />}
              />
            </RTLView>
          </RTLView>
        </RTLScrollView>
      </KeyboardAvoidingView>

      {/* Category Picker Modal */}
      {showCategoryPicker && (
        <RTLView style={styles.modalOverlay}>
          {renderCategoryPicker()}
        </RTLView>
      )}
    </RTLSafeAreaView>
  );
};

const createStyles = (colors: ThemeColors) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    keyboardAvoid: {
      flex: 1,
    },
    scrollView: {
      flex: 1,
    },
    content: {
      padding: SPACING.lg,
    },
    header: {
      marginBottom: SPACING.xl,
    },
    title: {
      fontSize: FONT_SIZES.xxl,
      fontWeight: FONT_WEIGHTS.bold,
      color: colors.textPrimary,
      marginBottom: SPACING.sm,
    },
    subtitle: {
      fontSize: FONT_SIZES.md,
      color: colors.textSecondary,
      lineHeight: 22,
    },
    section: {
      marginBottom: SPACING.lg,
    },
    sectionTitle: {
      fontSize: FONT_SIZES.lg,
      fontWeight: FONT_WEIGHTS.semibold,
      color: colors.textPrimary,
      marginBottom: SPACING.md,
    },
    input: {
      marginBottom: SPACING.md,
    },
    categorySelector: {
      marginBottom: SPACING.md,
    },
    categoryLabel: {
      fontSize: FONT_SIZES.md,
      fontWeight: FONT_WEIGHTS.medium,
      color: colors.textPrimary,
      marginBottom: SPACING.sm,
    },
    categoryValue: {
      flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
      justifyContent: 'space-between',
      alignItems: 'center',
      padding: SPACING.md,
      borderWidth: 1,
      borderColor: colors.border,
      borderRadius: BORDER_RADIUS.md,
      backgroundColor: colors.surface,
    },
    categoryText: {
      fontSize: FONT_SIZES.md,
      color: colors.textPrimary,
    },
    placeholderText: {
      color: colors.textSecondary,
    },
    errorText: {
      fontSize: FONT_SIZES.sm,
      color: '#FF6B6B',
      marginTop: SPACING.xs,
    },
    currencySymbol: {
      fontSize: FONT_SIZES.md,
      color: colors.textSecondary,
      fontWeight: FONT_WEIGHTS.medium,
    },
    helperText: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
      fontStyle: 'italic',
      marginTop: -SPACING.sm,
    },
    actionButtons: {
      flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
      gap: SPACING.md,
      marginTop: SPACING.xl,
      marginBottom: SPACING.xl,
    },
    cancelActionButton: {
      flex: 1,
    },
    submitButton: {
      flex: 2,
    },
    modalOverlay: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'center',
      alignItems: 'center',
      padding: SPACING.xl,
    },
    categoryPicker: {
      width: '100%',
      maxHeight: '80%',
    },
    categoryPickerTitle: {
      fontSize: FONT_SIZES.lg,
      fontWeight: FONT_WEIGHTS.semibold,
      color: colors.textPrimary,
      marginBottom: SPACING.md,
      textAlign: 'center',
    },
    categoryOption: {
      flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingVertical: SPACING.md,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    categoryOptionText: {
      fontSize: FONT_SIZES.md,
      color: colors.textPrimary,
    },
    cancelButton: {
      marginTop: SPACING.md,
    },
  });
