import { Platform } from 'react-native';

/**
 * Animation Performance Monitoring and Optimization Utilities
 * Provides tools to monitor, optimize, and recycle animations for better performance
 */

export interface AnimationMetrics {
  animationType: string;
  duration: number;
  startTime: number;
  endTime?: number;
  frameDrops?: number;
  memoryUsage?: number;
  isNativeDriver: boolean;
}

export interface PerformanceReport {
  totalAnimations: number;
  averageDuration: number;
  frameDropRate: number;
  memoryPeak: number;
  nativeDriverUsage: number;
  recommendations: string[];
}

class AnimationPerformanceMonitor {
  private metrics: AnimationMetrics[] = [];
  private frameDropCounter = 0;
  private memoryPeak = 0;
  private isMonitoring = false;

  /**
   * Start monitoring animation performance
   */
  startMonitoring(): void {
    this.isMonitoring = true;
    this.metrics = [];
    this.frameDropCounter = 0;
    this.memoryPeak = 0;
    
    if (__DEV__) {
      console.log('🎬 Animation performance monitoring started');
    }
  }

  /**
   * Stop monitoring and generate report
   */
  stopMonitoring(): PerformanceReport {
    this.isMonitoring = false;
    
    const report = this.generateReport();
    
    if (__DEV__) {
      console.log('🎬 Animation performance monitoring stopped');
      console.table(report);
    }
    
    return report;
  }

  /**
   * Record animation start
   */
  recordAnimationStart(animationType: string, isNativeDriver: boolean = true): string {
    if (!this.isMonitoring) return '';
    
    const animationId = `${animationType}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const metric: AnimationMetrics = {
      animationType,
      duration: 0,
      startTime: performance.now(),
      isNativeDriver,
    };
    
    this.metrics.push(metric);
    
    return animationId;
  }

  /**
   * Record animation end
   */
  recordAnimationEnd(animationId: string): void {
    if (!this.isMonitoring) return;
    
    const endTime = performance.now();
    const metric = this.metrics.find(m => 
      `${m.animationType}_${m.startTime}_${animationId.split('_')[2]}` === animationId
    );
    
    if (metric) {
      metric.endTime = endTime;
      metric.duration = endTime - metric.startTime;
    }
  }

  /**
   * Record frame drop
   */
  recordFrameDrop(): void {
    if (!this.isMonitoring) return;
    this.frameDropCounter++;
  }

  /**
   * Update memory usage
   */
  updateMemoryUsage(usage: number): void {
    if (!this.isMonitoring) return;
    if (usage > this.memoryPeak) {
      this.memoryPeak = usage;
    }
  }

  /**
   * Generate performance report
   */
  private generateReport(): PerformanceReport {
    const completedAnimations = this.metrics.filter(m => m.endTime);
    const totalAnimations = completedAnimations.length;
    
    const averageDuration = totalAnimations > 0 
      ? completedAnimations.reduce((sum, m) => sum + m.duration, 0) / totalAnimations
      : 0;
    
    const nativeDriverCount = completedAnimations.filter(m => m.isNativeDriver).length;
    const nativeDriverUsage = totalAnimations > 0 ? (nativeDriverCount / totalAnimations) * 100 : 0;
    
    const frameDropRate = totalAnimations > 0 ? (this.frameDropCounter / totalAnimations) * 100 : 0;
    
    const recommendations = this.generateRecommendations(
      frameDropRate,
      nativeDriverUsage,
      averageDuration
    );
    
    return {
      totalAnimations,
      averageDuration,
      frameDropRate,
      memoryPeak: this.memoryPeak,
      nativeDriverUsage,
      recommendations,
    };
  }

  /**
   * Generate performance recommendations
   */
  private generateRecommendations(
    frameDropRate: number,
    nativeDriverUsage: number,
    averageDuration: number
  ): string[] {
    const recommendations: string[] = [];
    
    if (frameDropRate > 5) {
      recommendations.push('High frame drop rate detected. Consider reducing animation complexity.');
    }
    
    if (nativeDriverUsage < 80) {
      recommendations.push('Low native driver usage. Enable useNativeDriver for better performance.');
    }
    
    if (averageDuration > 1000) {
      recommendations.push('Long animation durations detected. Consider shorter, snappier animations.');
    }
    
    if (this.memoryPeak > 50) {
      recommendations.push('High memory usage detected. Implement animation recycling.');
    }
    
    if (recommendations.length === 0) {
      recommendations.push('Animation performance is optimal! 🎉');
    }
    
    return recommendations;
  }

  /**
   * Get current metrics
   */
  getMetrics(): AnimationMetrics[] {
    return [...this.metrics];
  }

  /**
   * Clear all metrics
   */
  clearMetrics(): void {
    this.metrics = [];
    this.frameDropCounter = 0;
    this.memoryPeak = 0;
  }
}

// Singleton instance
export const animationMonitor = new AnimationPerformanceMonitor();

/**
 * Animation recycling pool for frequently used animations
 */
class AnimationPool {
  private pool: Map<string, any[]> = new Map();
  private maxPoolSize = 10;

  /**
   * Get animation from pool or create new one
   */
  getAnimation(type: string, factory: () => any): any {
    const poolKey = type;
    const pool = this.pool.get(poolKey) || [];
    
    if (pool.length > 0) {
      return pool.pop();
    }
    
    return factory();
  }

  /**
   * Return animation to pool
   */
  returnAnimation(type: string, animation: any): void {
    const poolKey = type;
    const pool = this.pool.get(poolKey) || [];
    
    if (pool.length < this.maxPoolSize) {
      // Reset animation state before returning to pool
      if (animation && typeof animation.reset === 'function') {
        animation.reset();
      }
      pool.push(animation);
      this.pool.set(poolKey, pool);
    }
  }

  /**
   * Clear animation pool
   */
  clearPool(): void {
    this.pool.clear();
  }

  /**
   * Get pool statistics
   */
  getPoolStats(): Record<string, number> {
    const stats: Record<string, number> = {};
    this.pool.forEach((pool, type) => {
      stats[type] = pool.length;
    });
    return stats;
  }
}

// Singleton instance
export const animationPool = new AnimationPool();

/**
 * Optimized animation configuration based on platform and performance
 */
export const getOptimizedAnimationConfig = (
  baseConfig: any,
  animationType: string
): any => {
  const optimizedConfig = { ...baseConfig };
  
  // Platform-specific optimizations
  if (Platform.OS === 'android') {
    // Android optimizations
    optimizedConfig.useNativeDriver = optimizedConfig.useNativeDriver !== false;
    
    // Reduce duration on lower-end devices
    if (optimizedConfig.duration && optimizedConfig.duration > 300) {
      optimizedConfig.duration = Math.min(optimizedConfig.duration, 300);
    }
  } else if (Platform.OS === 'ios') {
    // iOS optimizations
    optimizedConfig.useNativeDriver = optimizedConfig.useNativeDriver !== false;
  } else if (Platform.OS === 'web') {
    // Web optimizations
    optimizedConfig.useNativeDriver = false; // Web doesn't support native driver
    
    // Use CSS animations when possible
    if (animationType === 'fade' || animationType === 'scale') {
      optimizedConfig.useCSS = true;
    }
  }
  
  return optimizedConfig;
};

/**
 * Performance-aware animation hook
 */
export const useAnimationPerformance = (animationType: string) => {
  const startAnimation = (isNativeDriver: boolean = true) => {
    return animationMonitor.recordAnimationStart(animationType, isNativeDriver);
  };
  
  const endAnimation = (animationId: string) => {
    animationMonitor.recordAnimationEnd(animationId);
  };
  
  const recordFrameDrop = () => {
    animationMonitor.recordFrameDrop();
  };
  
  return {
    startAnimation,
    endAnimation,
    recordFrameDrop,
  };
};

/**
 * Optimized easing functions for better performance
 */
export const OptimizedEasing = {
  // Fast easing for quick interactions
  fast: {
    duration: 150,
    easing: 'ease-out',
  },
  
  // Standard easing for most animations
  standard: {
    duration: 250,
    easing: 'ease-in-out',
  },
  
  // Slow easing for important transitions
  slow: {
    duration: 400,
    easing: 'ease-in-out',
  },
  
  // Bounce easing for playful interactions
  bounce: {
    duration: 300,
    easing: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
  },
  
  // Elastic easing for attention-grabbing animations
  elastic: {
    duration: 500,
    easing: 'cubic-bezier(0.175, 0.885, 0.32, 1.275)',
  },
};

/**
 * Check if device supports high-performance animations
 */
export const supportsHighPerformanceAnimations = (): boolean => {
  // Simple heuristic based on platform
  if (Platform.OS === 'web') {
    return true; // Assume modern browsers support good performance
  }
  
  // For native platforms, assume good performance
  // In a real app, you might check device specs
  return true;
};

/**
 * Get recommended animation settings based on device performance
 */
export const getRecommendedAnimationSettings = () => {
  const highPerformance = supportsHighPerformanceAnimations();
  
  return {
    enableComplexAnimations: highPerformance,
    maxConcurrentAnimations: highPerformance ? 10 : 5,
    defaultDuration: highPerformance ? 250 : 200,
    enableParticleEffects: highPerformance,
    enableShadowAnimations: highPerformance,
  };
};
