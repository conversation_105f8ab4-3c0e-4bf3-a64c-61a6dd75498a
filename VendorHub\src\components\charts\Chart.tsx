import React from 'react';
import { StyleSheet, Dimensions } from 'react-native';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  ContributionGraph } from 'react-native-chart-kit';
import { useTheme } from '../../hooks';
import { RTLView, RTLText } from '../RTL';
import { SPACING, FONT_SIZES, FONT_WEIGHTS, BORDER_RADIUS } from '../../constants/theme';
import type ThemeColors  from '../../contexts/ThemeContext';

const screenWidth = Dimensions.get('window').width;

export interface ChartData {
  labels?: string[];
  datasets?: Array<{
    data: number[];
    color?: (opacity: number) => string;
    strokeWidth?: number;
  }>;
  data?: number[];
  legend?: string[];
}

export interface ChartProps {
  type: 'line' | 'bar' | 'pie' | 'progress' | 'contribution';
  data: ChartData;
  title?: string;
  subtitle?: string;
  height?: number;
  width?: number;
  showLegend?: boolean;
  style?: any;
}

export const Chart: React.FC<ChartProps> = ({
  type,
  data,
  title,
  subtitle,
  height = 220,
  width = screenWidth - 32,
  showLegend = true,
  style,
}) => {
  const { colors } = useTheme();
  const styles = createStyles(colors);

  const chartConfig = {
    backgroundColor: colors.surface,
    backgroundGradientFrom: colors.surface,
    backgroundGradientTo: colors.surface,
    decimalPlaces: 0,
    color: (opacity = 1) => `rgba(102, 126, 234, ${opacity})`,
    labelColor: (opacity = 1) => colors.textSecondary,
    style: {
      borderRadius: BORDER_RADIUS.md,
    },
    propsForDots: {
      r: '4',
      strokeWidth: '2',
      stroke: '#667eea',
    },
    propsForBackgroundLines: {
      strokeDasharray: '',
      stroke: colors.border,
      strokeWidth: 1,
    },
    propsForLabels: {
      fontSize: 12,
      fontWeight: '500',
    },
  };

  const pieChartConfig = {
    color: (opacity = 1) => `rgba(102, 126, 234, ${opacity})`,
    labelColor: (opacity = 1) => colors.textPrimary,
    style: {
      borderRadius: BORDER_RADIUS.md,
    },
  };

  const renderChart = () => {
    switch (type) {
      case 'line':
        return (
          <LineChart
            data={data as any}
            width={width}
            height={height}
            chartConfig={chartConfig}
            bezier
            style={styles.chart}
            withDots={true}
            withShadow={false}
            withScrollableDot={false}
            withInnerLines={true}
            withOuterLines={false}
            withVerticalLines={false}
            withHorizontalLines={true}
          />
        );

      case 'bar':
        return (
          <BarChart
            data={data as any}
            width={width}
            height={height}
            yAxisLabel=""
            yAxisSuffix=""
            chartConfig={chartConfig}
            style={styles.chart}
            showValuesOnTopOfBars={true}
            withInnerLines={false}
            showBarTops={false}
            fromZero={true}
          />
        );

      case 'pie':
        const pieData = data.data?.map((value, index) => ({
          name: data.labels?.[index] || `Item ${index + 1}`,
          population: value,
          color: `hsl(${(index * 60) % 360}, 70%, 60%)`,
          legendFontColor: colors.textSecondary,
          legendFontSize: 12,
        })) || [];

        return (
          <PieChart
            data={pieData}
            width={width}
            height={height}
            chartConfig={pieChartConfig}
            accessor="population"
            backgroundColor="transparent"
            paddingLeft="15"
            center={[10, 10]}
            style={styles.chart}
          />
        );

      case 'progress':
        const progressData = {
          labels: data.labels || [],
          data: data.data || [],
        };

        return (
          <ProgressChart
            data={progressData}
            width={width}
            height={height}
            strokeWidth={16}
            radius={32}
            chartConfig={chartConfig}
            hideLegend={!showLegend}
            style={styles.chart}
          />
        );

      case 'contribution':
        const contributionData = data.datasets?.[0]?.data.map((value, index) => ({
          date: new Date(2024, 0, index + 1),
          count: value,
        })) || [];

        return (
          <ContributionGraph
            values={contributionData}
            endDate={new Date()}
            numDays={105}
            width={width}
            height={height}
            chartConfig={chartConfig}
            style={styles.chart}
            tooltipDataAttrs={(value) => ({})}
          />
        );

      default:
        return null;
    }
  };

  return (
    <RTLView style={[styles.container, style]}>
      {(title || subtitle) && (
        <RTLView style={styles.header}>
          {title && <RTLText style={styles.title}>{title}</RTLText>}
          {subtitle && <RTLText style={styles.subtitle}>{subtitle}</RTLText>}
        </RTLView>
      )}

      <RTLView style={styles.chartContainer}>
        {renderChart()}
      </RTLView>

      {showLegend && data.legend && (
        <RTLView style={styles.legend}>
          {data.legend.map((item, index) => (
            <RTLView key={index} style={styles.legendItem}>
              <RTLView
                style={[
                  styles.legendColor,
                  { backgroundColor: `hsl(${(index * 60) % 360}, 70%, 60%)` },
                ]}
              />
              <RTLText style={styles.legendText}>{item}</RTLText>
            </RTLView>
          ))}
        </RTLView>
      )}
    </RTLView>
  );
};

const createStyles = (colors: ThemeColors) =>
  StyleSheet.create({
    container: {
      backgroundColor: colors.surface,
      borderRadius: BORDER_RADIUS.md,
      padding: SPACING.md,
      marginBottom: SPACING.md,
      elevation: 2,
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: 1,
      },
      shadowOpacity: 0.22,
      shadowRadius: 2.22,
    },
    header: {
      marginBottom: SPACING.md,
    },
    title: {
      fontSize: FONT_SIZES.lg,
      fontWeight: FONT_WEIGHTS.semibold,
      color: colors.textPrimary,
      marginBottom: SPACING.xs,
    },
    subtitle: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
    },
    chartContainer: {
      alignItems: 'center',
      justifyContent: 'center',
    },
    chart: {
      borderRadius: BORDER_RADIUS.md,
    },
    legend: {
      flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
      flexWrap: 'wrap',
      justifyContent: 'center',
      marginTop: SPACING.md,
      gap: SPACING.sm,
    },
    legendItem: {
      flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
      alignItems: 'center',
      marginHorizontal: SPACING.xs,
    },
    legendColor: {
      width: 12,
      height: 12,
      borderRadius: 6,
      marginRight: SPACING.xs,
    },
    legendText: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
    },
  });

export default Chart;
