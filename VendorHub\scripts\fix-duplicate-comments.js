#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Configuration
const config = {
  srcDir: path.join(__dirname, '../src'),
  outputFile: path.join(__dirname, '../DUPLICATE_COMMENTS_FIXES_REPORT.md'),
  excludePatterns: [
    /node_modules/,
    /\.git/,
    /\.expo/,
    /dist/,
    /build/,
    /coverage/,
    /\.DS_Store/,
    /\.env/,
    /package-lock\.json/,
    /yarn\.lock/,
    /\.log$/,
    /\.md$/,
    /\.json$/,
    /\.js\.map$/,
    /\.d\.ts$/,
    /scripts/,
    /assets/,
  ],
  fileExtensions: ['.tsx', '.ts', '.jsx', '.js'],
  
  // Patterns to fix
  duplicatePatterns: [
    {
      name: 'duplicateRTLComment',
      pattern: /\/\/ RTL: Will be automatically flipped by RTLText component \/\/ RTL: Will be automatically flipped by RTLText component[^,\n]*/g,
      replacement: ', // RTL: Will be automatically flipped by RTLText component',
      description: 'Fixed duplicate RTL comment'
    },
    {
      name: 'duplicateRTLCommentWithExtra',
      pattern: /\/\/ RTL: Will be automatically flipped by RTLText component[^,\n]*\/\/ Will be flipped to ['"]right['"] in RTL by RTLText[^,\n]*/g,
      replacement: ', // RTL: Will be automatically flipped by RTLText component',
      description: 'Fixed duplicate RTL comment with extra text'
    },
    {
      name: 'duplicateRTLCommentAsConst',
      pattern: /\/\/ RTL: Will be automatically flipped by RTLText component \/\/ RTL: Will be automatically flipped by RTLText component as const,/g,
      replacement: ' as const, // RTL: Will be automatically flipped by RTLText component',
      description: 'Fixed duplicate RTL comment with as const'
    },
    {
      name: 'trailingCommaIssue',
      pattern: /textAlign:\s*['"][^'"]*['"][^,\n]*\/\/ RTL: Will be automatically flipped by RTLText component\s*([^,\n])/g,
      replacement: (match, nextChar) => {
        const base = match.replace(/\/\/ RTL: Will be automatically flipped by RTLText component\s*[^,\n]*/, ', // RTL: Will be automatically flipped by RTLText component');
        return base + (nextChar && nextChar !== ',' ? nextChar : '');
      },
      description: 'Fixed trailing comma and RTL comment formatting'
    }
  ]
};

class DuplicateCommentsFixer {
  constructor() {
    this.results = {
      filesProcessed: 0,
      filesFixed: 0,
      totalFixes: 0,
      fixesByType: {},
      fixedFiles: []
    };
  }

  scanDirectory(dirPath) {
    const items = fs.readdirSync(dirPath);
    
    for (const item of items) {
      const fullPath = path.join(dirPath, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        // Skip excluded directories
        if (config.excludePatterns.some(pattern => pattern.test(fullPath))) {
          continue;
        }
        this.scanDirectory(fullPath);
      } else if (stat.isFile()) {
        // Check if file should be processed
        if (this.shouldProcessFile(fullPath)) {
          this.processFile(fullPath);
        }
      }
    }
  }

  shouldProcessFile(filePath) {
    // Check file extension
    const ext = path.extname(filePath);
    if (!config.fileExtensions.includes(ext)) {
      return false;
    }
    
    // Check exclude patterns
    if (config.excludePatterns.some(pattern => pattern.test(filePath))) {
      return false;
    }
    
    return true;
  }

  processFile(filePath) {
    this.results.filesProcessed++;
    
    try {
      let content = fs.readFileSync(filePath, 'utf8');
      let originalContent = content;
      let fileFixed = false;
      let fileFixes = [];

      // Apply each fix pattern
      config.duplicatePatterns.forEach(pattern => {
        let matches = 0;
        
        if (typeof pattern.replacement === 'function') {
          content = content.replace(pattern.pattern, (...args) => {
            matches++;
            return pattern.replacement(...args);
          });
        } else {
          const newContent = content.replace(pattern.pattern, pattern.replacement);
          if (newContent !== content) {
            matches = (content.match(pattern.pattern) || []).length;
            content = newContent;
          }
        }

        if (matches > 0) {
          fileFixed = true;
          fileFixes.push({
            type: pattern.name,
            count: matches,
            description: pattern.description
          });
          
          if (!this.results.fixesByType[pattern.name]) {
            this.results.fixesByType[pattern.name] = 0;
          }
          this.results.fixesByType[pattern.name] += matches;
          this.results.totalFixes += matches;
        }
      });

      // Write file if changes were made
      if (fileFixed && content !== originalContent) {
        fs.writeFileSync(filePath, content, 'utf8');
        this.results.filesFixed++;
        this.results.fixedFiles.push({
          path: path.relative(process.cwd(), filePath),
          fixes: fileFixes,
          totalFixes: fileFixes.reduce((sum, fix) => sum + fix.count, 0)
        });
        
        console.log(`✅ Fixed: ${path.relative(process.cwd(), filePath)} (${fileFixes.reduce((sum, fix) => sum + fix.count, 0)} fixes)`);
      }
    } catch (error) {
      console.error(`❌ Error processing ${filePath}:`, error.message);
    }
  }

  generateReport() {
    const report = [
      '# Duplicate Comments Fixes Report',
      '',
      `**Generated:** ${new Date().toLocaleString()}`,
      `**Files Processed:** ${this.results.filesProcessed}`,
      `**Files Fixed:** ${this.results.filesFixed}`,
      `**Total Fixes Applied:** ${this.results.totalFixes}`,
      '',
      '## 📊 Fixes Summary',
      '',
      '| Fix Type | Count | Description |',
      '|----------|-------|-------------|'
    ];

    // Add fixes summary
    Object.entries(this.results.fixesByType).forEach(([type, count]) => {
      const pattern = config.duplicatePatterns.find(p => p.name === type);
      const description = pattern ? pattern.description : type;
      report.push(`| ${type} | ${count} | ${description} |`);
    });

    if (this.results.fixedFiles.length > 0) {
      report.push('', '## 📁 Fixed Files', '');
      
      this.results.fixedFiles.forEach(file => {
        report.push(`### ${file.path}`, '');
        report.push(`**Total fixes:** ${file.totalFixes}`, '');
        
        file.fixes.forEach(fix => {
          report.push(`- **${fix.type}** (${fix.count} occurrences): ${fix.description}`);
        });
        
        report.push('');
      });
    }

    // Add recommendations
    report.push(
      '## 🔧 Recommendations',
      '',
      '1. **Code Review:** Review the fixed files to ensure changes are correct',
      '2. **Testing:** Run tests to ensure functionality is not affected',
      '3. **Prevention:** Use linting rules to prevent duplicate comments',
      '4. **Documentation:** Update coding standards to avoid such issues',
      '',
      '## ✅ Next Steps',
      '',
      '- Commit the fixed files',
      '- Update ESLint rules if needed',
      '- Run comprehensive tests',
      '- Review RTL implementation for consistency'
    );

    return report.join('\n');
  }

  run() {
    console.log('🔍 Scanning for duplicate comments...');
    
    // Scan source directory
    if (fs.existsSync(config.srcDir)) {
      this.scanDirectory(config.srcDir);
    }

    // Generate and save report
    const report = this.generateReport();
    fs.writeFileSync(config.outputFile, report, 'utf8');
    
    console.log('\n📊 Summary:');
    console.log(`Files processed: ${this.results.filesProcessed}`);
    console.log(`Files fixed: ${this.results.filesFixed}`);
    console.log(`Total fixes: ${this.results.totalFixes}`);
    console.log(`Report saved: ${config.outputFile}`);
    
    if (this.results.totalFixes > 0) {
      console.log('\n✅ Duplicate comments have been fixed!');
    } else {
      console.log('\n✅ No duplicate comments found.');
    }
  }
}

// Run the fixer
const fixer = new DuplicateCommentsFixer();
fixer.run();
