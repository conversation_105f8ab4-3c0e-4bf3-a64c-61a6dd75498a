import React, { createContext, useContext, useRef, useCallback, useEffect } from 'react';
import { useIntelligentCache } from '../performance/IntelligentCache';
import type { Vendor, Product } from '../../contexts/DataContext';

interface CategoryRule {
  id: string;
  name: string;
  keywords: string[];
  patterns: RegExp[];
  weight: number;
  confidence: number;
  parentCategory?: string;
  subcategories?: string[];
}

interface CategoryPrediction {
  category: string;
  confidence: number;
  reasons: string[];
  subcategory?: string;
  tags: string[];
}

interface SmartCategorizationConfig {
  enableAutoTagging: boolean;
  enableHierarchicalCategories: boolean;
  enableMultiLanguageSupport: boolean;
  enableLearningFromUserBehavior: boolean;
  confidenceThreshold: number;
  maxSuggestions: number;
  enableCulturalCategories: boolean;
}

interface SmartCategorizationContextType {
  categorizeVendor: (vendor: Vendor) => Promise<CategoryPrediction[]>;
  categorizeProduct: (product: Product) => Promise<CategoryPrediction[]>;
  suggestCategories: (text: string, type: 'vendor' | 'product') => Promise<CategoryPrediction[]>;
  getAvailableCategories: (type: 'vendor' | 'product') => Promise<string[]>;
  addCustomCategory: (category: CategoryRule) => Promise<void>;
  trainFromUserFeedback: (itemId: string, correctCategory: string, type: 'vendor' | 'product') => Promise<void>;
  getCategorizationInsights: () => Promise<any>;
  exportCategorizationModel: () => Promise<string>;
}

const DEFAULT_CONFIG: SmartCategorizationConfig = {
  enableAutoTagging: true,
  enableHierarchicalCategories: true,
  enableMultiLanguageSupport: true,
  enableLearningFromUserBehavior: true,
  confidenceThreshold: 0.6,
  maxSuggestions: 5,
  enableCulturalCategories: true,
};

// Predefined category rules
const VENDOR_CATEGORIES: CategoryRule[] = [
  {
    id: 'electronics',
    name: 'Electronics',
    keywords: ['phone', 'computer', 'laptop', 'tablet', 'electronics', 'mobile', 'tech', 'gadget'],
    patterns: [/\b(phone|mobile|computer|laptop|electronics?)\b/i],
    weight: 1.0,
    confidence: 0.8,
    subcategories: ['Mobile Phones', 'Computers', 'Accessories'],
  },
  {
    id: 'fashion',
    name: 'Fashion & Clothing',
    keywords: ['clothes', 'fashion', 'dress', 'shirt', 'pants', 'shoes', 'accessories', 'jewelry'],
    patterns: [/\b(fashion|cloth(es|ing)|dress|shirt|shoes?)\b/i],
    weight: 1.0,
    confidence: 0.8,
    subcategories: ['Men\'s Clothing', 'Women\'s Clothing', 'Shoes', 'Accessories'],
  },
  {
    id: 'food',
    name: 'Food & Beverages',
    keywords: ['restaurant', 'food', 'cafe', 'coffee', 'bakery', 'grocery', 'market', 'dining'],
    patterns: [/\b(restaurant|food|cafe|coffee|bakery|grocery)\b/i],
    weight: 1.0,
    confidence: 0.9,
    subcategories: ['Restaurants', 'Cafes', 'Grocery Stores', 'Bakeries'],
  },
  {
    id: 'home',
    name: 'Home & Garden',
    keywords: ['furniture', 'home', 'garden', 'decor', 'kitchen', 'bedroom', 'living room'],
    patterns: [/\b(furniture|home|garden|decor|kitchen)\b/i],
    weight: 1.0,
    confidence: 0.8,
    subcategories: ['Furniture', 'Home Decor', 'Garden Supplies', 'Kitchen'],
  },
  {
    id: 'health',
    name: 'Health & Beauty',
    keywords: ['pharmacy', 'health', 'beauty', 'cosmetics', 'skincare', 'medical', 'wellness'],
    patterns: [/\b(pharmacy|health|beauty|cosmetics|medical)\b/i],
    weight: 1.0,
    confidence: 0.8,
    subcategories: ['Pharmacy', 'Beauty Products', 'Wellness', 'Medical'],
  },
  {
    id: 'automotive',
    name: 'Automotive',
    keywords: ['car', 'auto', 'vehicle', 'motorcycle', 'parts', 'repair', 'garage'],
    patterns: [/\b(car|auto|vehicle|motorcycle|garage)\b/i],
    weight: 1.0,
    confidence: 0.9,
    subcategories: ['Car Sales', 'Auto Repair', 'Parts & Accessories'],
  },
  {
    id: 'services',
    name: 'Services',
    keywords: ['service', 'repair', 'cleaning', 'maintenance', 'consultation', 'professional'],
    patterns: [/\b(service|repair|cleaning|maintenance)\b/i],
    weight: 0.8,
    confidence: 0.7,
    subcategories: ['Professional Services', 'Home Services', 'Business Services'],
  },
];

const PRODUCT_CATEGORIES: CategoryRule[] = [
  {
    id: 'electronics',
    name: 'Electronics',
    keywords: ['smartphone', 'laptop', 'tablet', 'headphones', 'camera', 'tv', 'gaming'],
    patterns: [/\b(smartphone|laptop|tablet|headphones|camera)\b/i],
    weight: 1.0,
    confidence: 0.8,
  },
  {
    id: 'clothing',
    name: 'Clothing',
    keywords: ['shirt', 'dress', 'pants', 'jeans', 'jacket', 'sweater', 'shoes'],
    patterns: [/\b(shirt|dress|pants|jeans|jacket|shoes?)\b/i],
    weight: 1.0,
    confidence: 0.9,
  },
  {
    id: 'books',
    name: 'Books & Media',
    keywords: ['book', 'novel', 'textbook', 'magazine', 'dvd', 'cd', 'music'],
    patterns: [/\b(book|novel|magazine|dvd|music)\b/i],
    weight: 1.0,
    confidence: 0.9,
  },
  {
    id: 'sports',
    name: 'Sports & Outdoors',
    keywords: ['sports', 'fitness', 'gym', 'outdoor', 'camping', 'hiking', 'exercise'],
    patterns: [/\b(sports?|fitness|gym|outdoor|camping)\b/i],
    weight: 1.0,
    confidence: 0.8,
  },
  {
    id: 'toys',
    name: 'Toys & Games',
    keywords: ['toy', 'game', 'puzzle', 'doll', 'action figure', 'board game'],
    patterns: [/\b(toy|game|puzzle|doll)\b/i],
    weight: 1.0,
    confidence: 0.9,
  },
];

// Arabic category mappings
const ARABIC_CATEGORY_KEYWORDS = {
  electronics: ['إلكترونيات', 'هاتف', 'كمبيوتر', 'لابتوب', 'تقنية'],
  fashion: ['أزياء', 'ملابس', 'فستان', 'قميص', 'حذاء', 'إكسسوارات'],
  food: ['مطعم', 'طعام', 'مقهى', 'قهوة', 'مخبز', 'بقالة'],
  home: ['أثاث', 'منزل', 'حديقة', 'ديكور', 'مطبخ'],
  health: ['صيدلية', 'صحة', 'جمال', 'مستحضرات', 'طبي'],
  automotive: ['سيارة', 'سيارات', 'مركبة', 'دراجة نارية', 'قطع غيار'],
  services: ['خدمة', 'خدمات', 'إصلاح', 'تنظيف', 'صيانة'],
};

const SmartCategorizationContext = createContext<SmartCategorizationContextType | undefined>(undefined);

export const useSmartCategorization = (): SmartCategorizationContextType => {
  const context = useContext(SmartCategorizationContext);
  if (!context) {
    throw new Error('useSmartCategorization must be used within a SmartCategorizationProvider');
  }
  return context;
};

interface SmartCategorizationProviderProps {
  children: React.ReactNode;
  config?: Partial<SmartCategorizationConfig>;
}

export const SmartCategorizationProvider: React.FC<SmartCategorizationProviderProps> = ({
  children,
  config = {},
}) => {
  const finalConfig = useRef({ ...DEFAULT_CONFIG, ...config });
  const cache = useIntelligentCache();
  
  // Category rules and learning data
  const vendorRules = useRef<CategoryRule[]>([...VENDOR_CATEGORIES]);
  const productRules = useRef<CategoryRule[]>([...PRODUCT_CATEGORIES]);
  const userFeedback = useRef<Map<string, { category: string; confidence: number }>>(new Map());
  const categoryStats = useRef<Map<string, { correct: number; total: number }>>(new Map());

  useEffect(() => {
    loadCustomRules();
    loadUserFeedback();
  }, []);

  const loadCustomRules = async () => {
    try {
      const customVendorRules = await cache.get<CategoryRule[]>('custom-vendor-rules');
      const customProductRules = await cache.get<CategoryRule[]>('custom-product-rules');
      
      if (customVendorRules) {
        vendorRules.current = [...VENDOR_CATEGORIES, ...customVendorRules];
      }
      
      if (customProductRules) {
        productRules.current = [...PRODUCT_CATEGORIES, ...customProductRules];
      }
    } catch (error) {
      console.warn('Failed to load custom rules:', error);
    }
  };

  const loadUserFeedback = async () => {
    try {
      const feedback = await cache.get<Record<string, any>>('categorization-feedback');
      if (feedback) {
        userFeedback.current = new Map(Object.entries(feedback));
      }
      
      const stats = await cache.get<Record<string, any>>('categorization-stats');
      if (stats) {
        categoryStats.current = new Map(Object.entries(stats));
      }
    } catch (error) {
      console.warn('Failed to load user feedback:', error);
    }
  };

  const saveUserFeedback = async () => {
    try {
      await cache.set('categorization-feedback', Object.fromEntries(userFeedback.current), {
        ttl: 30 * 24 * 60 * 60 * 1000, // 30 days
        tags: ['categorization'],
      });
      
      await cache.set('categorization-stats', Object.fromEntries(categoryStats.current), {
        ttl: 30 * 24 * 60 * 60 * 1000, // 30 days
        tags: ['categorization'],
      });
    } catch (error) {
      console.warn('Failed to save user feedback:', error);
    }
  };

  const extractFeatures = (text: string): string[] => {
    if (!text) return [];
    
    const features: string[] = [];
    const normalizedText = text.toLowerCase();
    
    // Extract words
    const words = normalizedText.match(/\b\w+\b/g) || [];
    features.push(...words);
    
    // Extract bigrams
    for (let i = 0; i < words.length - 1; i++) {
      features.push(`${words[i]} ${words[i + 1]}`);
    }
    
    // Extract special patterns
    const patterns = [
      /\b\d+\s*(gb|mb|inch|cm|kg|g)\b/g, // Technical specs
      /\b(new|used|refurbished|vintage)\b/g, // Condition
      /\b(premium|luxury|budget|affordable)\b/g, // Quality indicators
    ];
    
    patterns.forEach(pattern => {
      const matches = normalizedText.match(pattern);
      if (matches) {
        features.push(...matches);
      }
    });
    
    return features;
  };

  const calculateCategoryScore = (
    features: string[],
    rule: CategoryRule,
    enableArabic = true
  ): { score: number; matchedKeywords: string[] } => {
    let score = 0;
    const matchedKeywords: string[] = [];
    const featureText = features.join(' ').toLowerCase();
    
    // Keyword matching
    rule.keywords.forEach(keyword => {
      if (featureText.includes(keyword.toLowerCase())) {
        score += rule.weight;
        matchedKeywords.push(keyword);
      }
    });
    
    // Arabic keyword matching
    if (enableArabic && finalConfig.current.enableMultiLanguageSupport) {
      const arabicKeywords = ARABIC_CATEGORY_KEYWORDS[rule.id as keyof typeof ARABIC_CATEGORY_KEYWORDS] || [];
      arabicKeywords.forEach(keyword => {
        if (featureText.includes(keyword)) {
          score += rule.weight;
          matchedKeywords.push(keyword);
        }
      });
    }
    
    // Pattern matching
    rule.patterns.forEach(pattern => {
      if (pattern.test(featureText)) {
        score += rule.weight * 1.5; // Patterns get higher weight
        matchedKeywords.push('pattern match');
      }
    });
    
    // Normalize score
    const normalizedScore = Math.min(score / (rule.keywords.length + rule.patterns.length), 1);
    
    return { score: normalizedScore, matchedKeywords };
  };

  const categorizeText = (
    text: string,
    rules: CategoryRule[],
    type: 'vendor' | 'product'
  ): CategoryPrediction[] => {
    const features = extractFeatures(text);
    const predictions: CategoryPrediction[] = [];
    
    rules.forEach(rule => {
      const { score, matchedKeywords } = calculateCategoryScore(features, rule);
      
      if (score >= finalConfig.current.confidenceThreshold) {
        // Check for user feedback adjustment
        const feedbackKey = `${type}-${rule.id}`;
        const feedback = userFeedback.current.get(feedbackKey);
        const adjustedConfidence = feedback ? 
          (rule.confidence + feedback.confidence) / 2 : 
          rule.confidence;
        
        predictions.push({
          category: rule.name,
          confidence: Math.min(score * adjustedConfidence, 1),
          reasons: matchedKeywords.map(kw => `Matched keyword: ${kw}`),
          subcategory: rule.subcategories?.[0], // Default to first subcategory
          tags: generateTags(features, rule),
        });
      }
    });
    
    // Sort by confidence and limit results
    return predictions
      .sort((a, b) => b.confidence - a.confidence)
      .slice(0, finalConfig.current.maxSuggestions);
  };

  const generateTags = (features: string[], rule: CategoryRule): string[] => {
    if (!finalConfig.current.enableAutoTagging) return [];
    
    const tags: string[] = [];
    const featureText = features.join(' ').toLowerCase();
    
    // Quality tags
    if (featureText.includes('premium') || featureText.includes('luxury')) {
      tags.push('premium');
    }
    if (featureText.includes('budget') || featureText.includes('affordable')) {
      tags.push('budget');
    }
    
    // Condition tags
    if (featureText.includes('new')) tags.push('new');
    if (featureText.includes('used')) tags.push('used');
    
    // Cultural tags
    if (finalConfig.current.enableCulturalCategories) {
      if (featureText.includes('traditional') || featureText.includes('تقليدي')) {
        tags.push('traditional');
      }
      if (featureText.includes('modern') || featureText.includes('حديث')) {
        tags.push('modern');
      }
      if (featureText.includes('local') || featureText.includes('محلي')) {
        tags.push('local');
      }
    }
    
    return tags;
  };

  const categorizeVendor = useCallback(async (vendor: Vendor): Promise<CategoryPrediction[]> => {
    const text = `${vendor.businessName} ${vendor.businessDescription || ''} ${vendor.businessType || ''}`;
    return categorizeText(text, vendorRules.current, 'vendor');
  }, []);

  const categorizeProduct = useCallback(async (product: Product): Promise<CategoryPrediction[]> => {
    const text = `${product.name} ${product.description || ''} ${product.category || ''}`;
    return categorizeText(text, productRules.current, 'product');
  }, []);

  const suggestCategories = useCallback(async (
    text: string,
    type: 'vendor' | 'product'
  ): Promise<CategoryPrediction[]> => {
    const rules = type === 'vendor' ? vendorRules.current : productRules.current;
    return categorizeText(text, rules, type);
  }, []);

  const getAvailableCategories = useCallback(async (type: 'vendor' | 'product'): Promise<string[]> => {
    const rules = type === 'vendor' ? vendorRules.current : productRules.current;
    return rules.map(rule => rule.name);
  }, []);

  const addCustomCategory = useCallback(async (category: CategoryRule): Promise<void> => {
    try {
      // Add to appropriate rules array
      if (category.name.toLowerCase().includes('vendor') || category.name.toLowerCase().includes('shop')) {
        vendorRules.current.push(category);
        await cache.set('custom-vendor-rules', vendorRules.current.slice(VENDOR_CATEGORIES.length), {
          ttl: 365 * 24 * 60 * 60 * 1000, // 1 year
          tags: ['categorization', 'custom-rules'],
        });
      } else {
        productRules.current.push(category);
        await cache.set('custom-product-rules', productRules.current.slice(PRODUCT_CATEGORIES.length), {
          ttl: 365 * 24 * 60 * 60 * 1000, // 1 year
          tags: ['categorization', 'custom-rules'],
        });
      }
    } catch (error) {
      console.warn('Failed to add custom category:', error);
    }
  }, [cache]);

  const trainFromUserFeedback = useCallback(async (
    itemId: string,
    correctCategory: string,
    type: 'vendor' | 'product'
  ): Promise<void> => {
    if (!finalConfig.current.enableLearningFromUserBehavior) return;
    
    try {
      const feedbackKey = `${type}-${correctCategory}`;
      const existing = userFeedback.current.get(feedbackKey);
      
      if (existing) {
        // Increase confidence for correct predictions
        existing.confidence = Math.min(existing.confidence + 0.1, 1);
      } else {
        userFeedback.current.set(feedbackKey, {
          category: correctCategory,
          confidence: 0.8,
        });
      }
      
      // Update statistics
      const statsKey = correctCategory;
      const stats = categoryStats.current.get(statsKey) || { correct: 0, total: 0 };
      stats.correct += 1;
      stats.total += 1;
      categoryStats.current.set(statsKey, stats);
      
      await saveUserFeedback();
    } catch (error) {
      console.warn('Failed to save user feedback:', error);
    }
  }, []);

  const getCategorizationInsights = useCallback(async (): Promise<any> => {
    const vendorCategoryDistribution = vendorRules.current.reduce((acc, rule) => {
      acc[rule.name] = rule.confidence;
      return acc;
    }, {} as Record<string, number>);
    
    const productCategoryDistribution = productRules.current.reduce((acc, rule) => {
      acc[rule.name] = rule.confidence;
      return acc;
    }, {} as Record<string, number>);
    
    const accuracyStats = Array.from(categoryStats.current.entries()).map(([category, stats]) => ({
      category,
      accuracy: stats.correct / stats.total,
      totalPredictions: stats.total,
    }));
    
    return {
      vendorCategories: vendorCategoryDistribution,
      productCategories: productCategoryDistribution,
      accuracyStats,
      totalFeedback: userFeedback.current.size,
      customRules: {
        vendors: vendorRules.current.length - VENDOR_CATEGORIES.length,
        products: productRules.current.length - PRODUCT_CATEGORIES.length,
      },
    };
  }, []);

  const exportCategorizationModel = useCallback(async (): Promise<string> => {
    const model = {
      vendorRules: vendorRules.current,
      productRules: productRules.current,
      userFeedback: Object.fromEntries(userFeedback.current),
      categoryStats: Object.fromEntries(categoryStats.current),
      config: finalConfig.current,
      exportDate: new Date().toISOString(),
    };
    
    return JSON.stringify(model, null, 2);
  }, []);

  const contextValue: SmartCategorizationContextType = {
    categorizeVendor,
    categorizeProduct,
    suggestCategories,
    getAvailableCategories,
    addCustomCategory,
    trainFromUserFeedback,
    getCategorizationInsights,
    exportCategorizationModel,
  };

  return (
    <SmartCategorizationContext.Provider value={contextValue}>
      {children}
    </SmartCategorizationContext.Provider>
  );
};
