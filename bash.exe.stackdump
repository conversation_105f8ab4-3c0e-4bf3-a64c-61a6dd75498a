Stack trace:
Frame         Function      Args
0007FFFF9DD0  00021005FEBA (000210285F48, 00021026AB6E, 0007FFFF9DD0, 0007FFFF8CD0) msys-2.0.dll+0x1FEBA
0007FFFF9DD0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFA0A8) msys-2.0.dll+0x67F9
0007FFFF9DD0  000210046832 (000210285FF9, 0007FFFF9C88, 0007FFFF9DD0, 000000000000) msys-2.0.dll+0x6832
0007FFFF9DD0  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFF9DD0  0002100690B4 (0007FFFF9DE0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFFA0B0  00021006A49D (0007FFFF9DE0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFA33940000 ntdll.dll
7FFA32BC0000 KERNEL32.DLL
7FFA30DF0000 KERNELBASE.dll
7FFA32170000 USER32.dll
7FFA30BD0000 win32u.dll
7FFA32C90000 GDI32.dll
7FFA311E0000 gdi32full.dll
7FFA30B20000 msvcp_win.dll
7FFA30C00000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFA32CC0000 advapi32.dll
7FFA335F0000 msvcrt.dll
7FFA32DA0000 sechost.dll
7FFA32460000 RPCRT4.dll
7FFA2FFA0000 CRYPTBASE.DLL
7FFA30D50000 bcryptPrimitives.dll
7FFA33340000 IMM32.DLL
