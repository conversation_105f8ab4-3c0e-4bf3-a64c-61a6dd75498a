import React, { useState } from 'react';
import { StyleSheet, Alert, Image, Dimensions } from 'react-native';
import { RTLIcon, RTLText, RTLTouchableOpacity, RTLView } from '../RTL';
import * as ImagePickerExpo from 'expo-image-picker';
import { useThemedStyles } from '../../hooks';
import { Button } from '../Button';
import {
  SPACING,
  FONT_SIZES,
  FONT_WEIGHTS,
  BORDER_RADIUS } from '../../constants/theme';
import type ThemeColors  from '../../contexts/ThemeContext';

const { width: screenWidth } = Dimensions.get('window');

export interface SingleImagePickerProps {
  image: string | null;
  onImageChange: (image: string | null) => void;
  title?: string;
  subtitle?: string;
  placeholder?: string;
  aspectRatio?: [number, number];
  size?: 'small' | 'medium' | 'large';
  shape?: 'square' | 'circle' | 'rectangle';
  style?: any;
}

export const SingleImagePicker: React.FC<SingleImagePickerProps> = ({
  image,
  onImageChange,
  title = "Image",
  subtitle = "Upload an image",
  placeholder = "Tap to add image",
  aspectRatio = [1, 1],
  size = 'medium',
  shape = 'square',
  style,
}) => {
  const styles = useThemedStyles(createStyles);
  const [isLoading, setIsLoading] = useState(false);

  const getImageSize = () => {
    switch (size) {
      case 'small':
        return 80;
      case 'large':
        return 160;
      default:
        return 120;
    }
  };

  const requestPermissions = async () => {
    const { status: cameraStatus } = await ImagePickerExpo.requestCameraPermissionsAsync();
    const { status: mediaStatus } = await ImagePickerExpo.requestMediaLibraryPermissionsAsync();
    
    if (cameraStatus !== 'granted' || mediaStatus !== 'granted') {
      Alert.alert(
        'Permissions Required',
        'Please grant camera and photo library permissions to upload images.',
        [{ text: 'OK' }]
      );
      return false;
    }
    return true;
  };

  const showImagePickerOptions = () => {
    Alert.alert(
      'Select Image',
      'Choose how you want to add an image',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Camera', onPress: openCamera },
        { text: 'Photo Library', onPress: openImageLibrary },
        ...(image ? [{ text: 'Remove Image', style: 'destructive' as const, onPress: removeImage }] : []),
      ]
    );
  };

  const openCamera = async () => {
    const hasPermission = await requestPermissions();
    if (!hasPermission) return;

    setIsLoading(true);
    try {
      const result = await ImagePickerExpo.launchCameraAsync({
        mediaTypes: ImagePickerExpo.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: aspectRatio,
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        onImageChange(result.assets[0].uri);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to take photo. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const openImageLibrary = async () => {
    const hasPermission = await requestPermissions();
    if (!hasPermission) return;

    setIsLoading(true);
    try {
      const result = await ImagePickerExpo.launchImageLibraryAsync({
        mediaTypes: ImagePickerExpo.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: aspectRatio,
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        onImageChange(result.assets[0].uri);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to select image. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const removeImage = () => {
    Alert.alert(
      'Remove Image',
      'Are you sure you want to remove this image?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Remove',
          style: 'destructive',
          onPress: () => onImageChange(null),
        },
      ]
    );
  };

  const imageSize = getImageSize();
  const imageStyle = [
    styles.image,
    {
      width: imageSize,
      height: shape === 'rectangle' ? imageSize * 0.6 : imageSize,
      borderRadius: shape === 'circle' ? imageSize / 2 : BORDER_RADIUS.md,
    },
  ];

  const containerStyle = [
    styles.imageContainer,
    {
      width: imageSize,
      height: shape === 'rectangle' ? imageSize * 0.6 : imageSize,
      borderRadius: shape === 'circle' ? imageSize / 2 : BORDER_RADIUS.md,
    },
  ];

  return (
    <RTLView style={[styles.container, style]}>
      {/* Header */}
      <RTLView style={styles.header}>
        <RTLText style={styles.title}>{title}</RTLText>
        <RTLText style={styles.subtitle}>{subtitle}</RTLText>
      </RTLView>

      {/* Image Display */}
      <RTLView style={styles.imageSection}>
        <RTLTouchableOpacity
          style={containerStyle}
          onPress={showImagePickerOptions}
          disabled={isLoading}
        >
          {image ? (
            <>
              <Image source={{ uri: image }} style={imageStyle} />
              {/* Edit Overlay */}
              <RTLView style={styles.editOverlay}>
                <RTLIcon name="camera" size={20} color="#FFFFFF" />
              </RTLView>
            </>
          ) : (
            <RTLView style={[styles.placeholder, containerStyle]}>
              <RTLIcon 
                name="camera-outline" 
                size={imageSize * 0.3} 
                color="#CCCCCC" 
              />
              <RTLText style={styles.placeholderText}>{placeholder}</RTLText>
            </RTLView>
          )}
        </RTLTouchableOpacity>

        {/* Action Buttons */}
        {image && (
          <RTLView style={styles.actionButtons}>
            <Button
              title="Change"
              onPress={showImagePickerOptions}
              variant="outline"
              size="small"
              style={styles.actionButton}
            />
            <Button
              title="Remove"
              onPress={removeImage}
              variant="outline"
              size="small"
              style={[styles.actionButton, styles.removeButton].filter(Boolean) as any}
            />
          </RTLView>
        )}
      </RTLView>

      {/* Loading Indicator */}
      {isLoading && (
        <RTLView style={styles.loadingOverlay}>
          <RTLText style={styles.loadingText}>Processing...</RTLText>
        </RTLView>
      )}
    </RTLView>
  );
};

const createStyles = (colors: ThemeColors) => StyleSheet.create({
  container: {
    alignItems: 'center',
  },
  header: {
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  title: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.semibold,
    color: colors.text,
    marginBottom: SPACING.xs,
  },
  subtitle: {
    fontSize: FONT_SIZES.sm,
    color: colors.textSecondary,
    textAlign: 'center',
  },
  imageSection: {
    alignItems: 'center',
  },
  imageContainer: {
    position: 'relative',
    overflow: 'hidden',
    borderWidth: 2,
    borderColor: colors.border,
    borderStyle: 'dashed',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  placeholder: {
    backgroundColor: colors.surface,
    justifyContent: 'center',
    alignItems: 'center',
    borderStyle: 'dashed',
  },
  placeholderText: {
    fontSize: FONT_SIZES.xs,
    color: colors.textSecondary,
    marginTop: SPACING.xs,
    textAlign: 'center',
  },
  editOverlay: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    borderRadius: 15,
    width: 30,
    height: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  actionButtons: {
    flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
    marginTop: SPACING.md,
    gap: SPACING.sm,
  },
  actionButton: {
    minWidth: 80,
  },
  removeButton: {
    borderColor: '#FF6B6B',
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: BORDER_RADIUS.md,
  },
  loadingText: {
    fontSize: FONT_SIZES.sm,
    color: colors.text,
    marginTop: SPACING.xs,
  },
});
