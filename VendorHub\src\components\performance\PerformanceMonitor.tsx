import React, { createContext, useContext, useRef, useCallback, useEffect } from 'react';
import { AppState, Dimensions, InteractionManager } from 'react-native';

interface PerformanceMetrics {
  // Rendering performance
  averageFPS: number;
  frameDrops: number;
  totalFrames: number;
  renderTime: number;
  
  // Memory usage
  memoryUsage: number;
  memoryPeak: number;
  memoryWarnings: number;
  
  // App lifecycle
  appStartTime: number;
  timeToInteractive: number;
  backgroundTime: number;
  foregroundTime: number;
  
  // User interactions
  totalInteractions: number;
  averageResponseTime: number;
  slowInteractions: number;
  
  // Bundle and asset loading
  bundleLoadTime: number;
  assetLoadTime: number;
  imageLoadFailures: number;
  
  // Navigation performance
  screenTransitions: number;
  averageTransitionTime: number;
  slowTransitions: number;
  
  // Error tracking
  jsErrors: number;
  nativeErrors: number;
  networkErrors: number;
}

interface PerformanceAlert {
  id: string;
  type: 'warning' | 'error' | 'critical';
  message: string;
  timestamp: number;
  metrics: Partial<PerformanceMetrics>;
}

interface PerformanceConfig {
  enableMonitoring: boolean;
  enableAlerts: boolean;
  fpsThreshold: number;
  memoryThreshold: number; // in MB
  responseTimeThreshold: number; // in ms
  transitionTimeThreshold: number; // in ms
  alertCooldown: number; // in ms
  maxAlerts: number;
  enableDetailedLogging: boolean;
}

interface PerformanceMonitorContextType {
  getMetrics: () => PerformanceMetrics;
  getAlerts: () => PerformanceAlert[];
  clearAlerts: () => void;
  startInteractionMeasurement: (name: string) => () => void;
  measureScreenTransition: (screenName: string) => () => void;
  reportError: (error: Error, type: 'js' | 'native' | 'network') => void;
  setConfig: (config: Partial<PerformanceConfig>) => void;
  generateReport: () => string;
}

const DEFAULT_CONFIG: PerformanceConfig = {
  enableMonitoring: true,
  enableAlerts: true,
  fpsThreshold: 55,
  memoryThreshold: 150, // 150MB
  responseTimeThreshold: 100, // 100ms
  transitionTimeThreshold: 300, // 300ms
  alertCooldown: 30000, // 30 seconds
  maxAlerts: 50,
  enableDetailedLogging: __DEV__,
};

const PerformanceMonitorContext = createContext<PerformanceMonitorContextType | undefined>(undefined);

export const usePerformanceMonitor = (): PerformanceMonitorContextType => {
  const context = useContext(PerformanceMonitorContext);
  if (!context) {
    throw new Error('usePerformanceMonitor must be used within a PerformanceMonitorProvider');
  }
  return context;
};

interface PerformanceMonitorProviderProps {
  children: React.ReactNode;
  config?: Partial<PerformanceConfig>;
}

export const PerformanceMonitorProvider: React.FC<PerformanceMonitorProviderProps> = ({
  children,
  config = {},
}) => {
  const finalConfig = useRef({ ...DEFAULT_CONFIG, ...config });
  
  // Performance metrics
  const metrics = useRef<PerformanceMetrics>({
    averageFPS: 60,
    frameDrops: 0,
    totalFrames: 0,
    renderTime: 0,
    memoryUsage: 0,
    memoryPeak: 0,
    memoryWarnings: 0,
    appStartTime: Date.now(),
    timeToInteractive: 0,
    backgroundTime: 0,
    foregroundTime: 0,
    totalInteractions: 0,
    averageResponseTime: 0,
    slowInteractions: 0,
    bundleLoadTime: 0,
    assetLoadTime: 0,
    imageLoadFailures: 0,
    screenTransitions: 0,
    averageTransitionTime: 0,
    slowTransitions: 0,
    jsErrors: 0,
    nativeErrors: 0,
    networkErrors: 0,
  });

  // Alerts
  const alerts = useRef<PerformanceAlert[]>([]);
  const lastAlertTime = useRef<Record<string, number>>({});

  // Monitoring state
  const frameStartTime = useRef(Date.now());
  const lastFrameTime = useRef(Date.now());
  const activeInteractions = useRef<Map<string, number>>(new Map());
  const activeTransitions = useRef<Map<string, number>>(new Map());
  const appStateChangeTime = useRef(Date.now());

  // Timers and intervals
  const fpsMonitorInterval = useRef<NodeJS.Timeout>();
  const memoryMonitorInterval = useRef<NodeJS.Timeout>();

  useEffect(() => {
    if (!finalConfig.current.enableMonitoring) return;

    initializeMonitoring();
    setupAppStateListener();
    setupErrorHandlers();
    startPerformanceMonitoring();

    return () => {
      cleanup();
    };
  }, []);

  const initializeMonitoring = () => {
    // Mark time to interactive
    InteractionManager.runAfterInteractions(() => {
      metrics.current.timeToInteractive = Date.now() - metrics.current.appStartTime;
      
      if (finalConfig.current.enableDetailedLogging) {
        console.log(`Time to Interactive: ${metrics.current.timeToInteractive}ms`);
      }
    });
  };

  const setupAppStateListener = () => {
    const handleAppStateChange = (nextAppState: string) => {
      const now = Date.now();
      const timeDelta = now - appStateChangeTime.current;

      if (nextAppState === 'background') {
        metrics.current.foregroundTime += timeDelta;
      } else if (nextAppState === 'active') {
        metrics.current.backgroundTime += timeDelta;
      }

      appStateChangeTime.current = now;
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);
    return () => subscription?.remove();
  };

  const setupErrorHandlers = () => {
    // Global error handler for JS errors
    const originalHandler = global.ErrorUtils?.getGlobalHandler();
    
    global.ErrorUtils?.setGlobalHandler((error: Error, isFatal?: boolean) => {
      reportError(error, 'js');
      originalHandler?.(error, isFatal);
    });

    // Unhandled promise rejection handler
    const handleUnhandledRejection = (event: any) => {
      reportError(new Error(event.reason), 'js');
    };

    // Note: React Native doesn't have addEventListener for unhandledrejection
    // This would need to be implemented differently in a real app
  };

  const startPerformanceMonitoring = () => {
    // FPS monitoring
    fpsMonitorInterval.current = setInterval(() => {
      monitorFPS();
    }, 1000);

    // Memory monitoring
    memoryMonitorInterval.current = setInterval(() => {
      monitorMemory();
    }, 5000);
  };

  const monitorFPS = () => {
    const now = Date.now();
    const timeDelta = now - lastFrameTime.current;
    
    if (timeDelta > 0) {
      const currentFPS = 1000 / timeDelta;
      metrics.current.totalFrames++;
      
      // Update average FPS
      metrics.current.averageFPS = 
        (metrics.current.averageFPS * 0.9) + (currentFPS * 0.1);
      
      // Detect frame drops
      if (currentFPS < finalConfig.current.fpsThreshold) {
        metrics.current.frameDrops++;
        
        if (finalConfig.current.enableAlerts) {
          createAlert('warning', `Low FPS detected: ${currentFPS.toFixed(1)}`, {
            averageFPS: currentFPS,
          });
        }
      }
    }
    
    lastFrameTime.current = now;
  };

  const monitorMemory = () => {
    // Note: React Native doesn't provide direct memory access
    // This would need to be implemented with native modules
    // For now, we'll simulate memory monitoring
    
    const estimatedMemory = Math.random() * 100 + 50; // Simulated memory usage
    metrics.current.memoryUsage = estimatedMemory;
    
    if (estimatedMemory > metrics.current.memoryPeak) {
      metrics.current.memoryPeak = estimatedMemory;
    }
    
    if (estimatedMemory > finalConfig.current.memoryThreshold) {
      metrics.current.memoryWarnings++;
      
      if (finalConfig.current.enableAlerts) {
        createAlert('warning', `High memory usage: ${estimatedMemory.toFixed(1)}MB`, {
          memoryUsage: estimatedMemory,
        });
      }
    }
  };

  const createAlert = (
    type: 'warning' | 'error' | 'critical',
    message: string,
    alertMetrics: Partial<PerformanceMetrics> = {}
  ) => {
    if (!finalConfig.current.enableAlerts) return;

    const now = Date.now();
    const alertKey = `${type}-${message}`;
    
    // Check cooldown
    if (lastAlertTime.current[alertKey] && 
        now - lastAlertTime.current[alertKey] < finalConfig.current.alertCooldown) {
      return;
    }

    const alert: PerformanceAlert = {
      id: `${now}-${Math.random().toString(36).substr(2, 9)}`,
      type,
      message,
      timestamp: now,
      metrics: alertMetrics,
    };

    alerts.current.push(alert);
    lastAlertTime.current[alertKey] = now;

    // Limit number of alerts
    if (alerts.current.length > finalConfig.current.maxAlerts) {
      alerts.current = alerts.current.slice(-finalConfig.current.maxAlerts);
    }

    if (finalConfig.current.enableDetailedLogging) {
      console.warn(`Performance Alert [${type}]: ${message}`, alertMetrics);
    }
  };

  const cleanup = () => {
    if (fpsMonitorInterval.current) {
      clearInterval(fpsMonitorInterval.current);
    }
    if (memoryMonitorInterval.current) {
      clearInterval(memoryMonitorInterval.current);
    }
  };

  const getMetrics = useCallback((): PerformanceMetrics => {
    return { ...metrics.current };
  }, []);

  const getAlerts = useCallback((): PerformanceAlert[] => {
    return [...alerts.current];
  }, []);

  const clearAlerts = useCallback(() => {
    alerts.current = [];
    lastAlertTime.current = {};
  }, []);

  const startInteractionMeasurement = useCallback((name: string) => {
    const startTime = Date.now();
    activeInteractions.current.set(name, startTime);
    
    return () => {
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      activeInteractions.current.delete(name);
      metrics.current.totalInteractions++;
      
      // Update average response time
      metrics.current.averageResponseTime = 
        (metrics.current.averageResponseTime * (metrics.current.totalInteractions - 1) + duration) / 
        metrics.current.totalInteractions;
      
      // Check for slow interactions
      if (duration > finalConfig.current.responseTimeThreshold) {
        metrics.current.slowInteractions++;
        
        if (finalConfig.current.enableAlerts) {
          createAlert('warning', `Slow interaction: ${name} (${duration}ms)`, {
            averageResponseTime: duration,
          });
        }
      }
      
      if (finalConfig.current.enableDetailedLogging) {
        console.log(`Interaction "${name}" completed in ${duration}ms`);
      }
    };
  }, []);

  const measureScreenTransition = useCallback((screenName: string) => {
    const startTime = Date.now();
    activeTransitions.current.set(screenName, startTime);
    
    return () => {
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      activeTransitions.current.delete(screenName);
      metrics.current.screenTransitions++;
      
      // Update average transition time
      metrics.current.averageTransitionTime = 
        (metrics.current.averageTransitionTime * (metrics.current.screenTransitions - 1) + duration) / 
        metrics.current.screenTransitions;
      
      // Check for slow transitions
      if (duration > finalConfig.current.transitionTimeThreshold) {
        metrics.current.slowTransitions++;
        
        if (finalConfig.current.enableAlerts) {
          createAlert('warning', `Slow screen transition: ${screenName} (${duration}ms)`, {
            averageTransitionTime: duration,
          });
        }
      }
      
      if (finalConfig.current.enableDetailedLogging) {
        console.log(`Screen transition to "${screenName}" completed in ${duration}ms`);
      }
    };
  }, []);

  const reportError = useCallback((error: Error, type: 'js' | 'native' | 'network') => {
    switch (type) {
      case 'js':
        metrics.current.jsErrors++;
        break;
      case 'native':
        metrics.current.nativeErrors++;
        break;
      case 'network':
        metrics.current.networkErrors++;
        break;
    }
    
    if (finalConfig.current.enableAlerts) {
      createAlert('error', `${type.toUpperCase()} Error: ${error.message}`, {
        jsErrors: type === 'js' ? metrics.current.jsErrors : undefined,
        nativeErrors: type === 'native' ? metrics.current.nativeErrors : undefined,
        networkErrors: type === 'network' ? metrics.current.networkErrors : undefined,
      });
    }
    
    if (finalConfig.current.enableDetailedLogging) {
      console.error(`Performance Monitor - ${type} error:`, error);
    }
  }, []);

  const setConfig = useCallback((config: Partial<PerformanceConfig>) => {
    finalConfig.current = { ...finalConfig.current, ...config };
  }, []);

  const generateReport = useCallback((): string => {
    const currentMetrics = getMetrics();
    const currentAlerts = getAlerts();
    
    const report = {
      timestamp: new Date().toISOString(),
      metrics: currentMetrics,
      alerts: currentAlerts.slice(-10), // Last 10 alerts
      summary: {
        overallHealth: currentMetrics.averageFPS > 55 && 
                      currentMetrics.memoryUsage < 100 && 
                      currentMetrics.averageResponseTime < 100 ? 'Good' : 'Needs Attention',
        criticalIssues: currentAlerts.filter(a => a.type === 'critical').length,
        warnings: currentAlerts.filter(a => a.type === 'warning').length,
      },
    };
    
    return JSON.stringify(report, null, 2);
  }, [getMetrics, getAlerts]);

  const contextValue: PerformanceMonitorContextType = {
    getMetrics,
    getAlerts,
    clearAlerts,
    startInteractionMeasurement,
    measureScreenTransition,
    reportError,
    setConfig,
    generateReport,
  };

  return (
    <PerformanceMonitorContext.Provider value={contextValue}>
      {children}
    </PerformanceMonitorContext.Provider>
  );
};
