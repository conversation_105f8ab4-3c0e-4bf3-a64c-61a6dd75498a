// App constants
export const APP_NAME = 'محلات';
export const APP_VERSION = '1.0.0';
export const APP_DESCRIPTION = 'منصة التجارة الإلكترونية متعددة التجار';

// User roles
export const USER_ROLES = {
  ADMIN: 'admin',
  VENDOR: 'vendor',
  CUSTOMER: 'customer',
} as const;

export type UserRole = typeof USER_ROLES[keyof typeof USER_ROLES];

// Vendor status
export const VENDOR_STATUS = {
  PENDING: 'pending',
  APPROVED: 'approved',
  REJECTED: 'rejected',
  SUSPENDED: 'suspended',
} as const;

export type VendorStatus = typeof VENDOR_STATUS[keyof typeof VENDOR_STATUS];

// Order status
export const ORDER_STATUS = {
  PENDING: 'pending',
  CONFIRMED: 'confirmed',
  PROCESSING: 'processing',
  SHIPPED: 'shipped',
  DELIVERED: 'delivered',
  CANCELLED: 'cancelled',
  REFUNDED: 'refunded',
} as const;

export type OrderStatus = typeof ORDER_STATUS[keyof typeof ORDER_STATUS];

// Product categories
export const PRODUCT_CATEGORIES = [
  'Electronics',
  'Fashion',
  'Home & Garden',
  'Sports & Outdoors',
  'Books',
  'Health & Beauty',
  'Toys & Games',
  'Automotive',
  'Food & Beverages',
  'Art & Crafts',
] as const;

export type ProductCategory = typeof PRODUCT_CATEGORIES[number];

// Default admin credentials
export const DEFAULT_ADMIN = {
  email: '<EMAIL>',
  password: 'admin123',
};

// API endpoints (mock)
export const API_ENDPOINTS = {
  AUTH: {
    LOGIN: '/auth/login',
    REGISTER: '/auth/register',
    LOGOUT: '/auth/logout',
    REFRESH: '/auth/refresh',
  },
  VENDORS: {
    LIST: '/vendors',
    CREATE: '/vendors',
    UPDATE: '/vendors/:id',
    DELETE: '/vendors/:id',
    APPROVE: '/vendors/:id/approve',
    REJECT: '/vendors/:id/reject',
  },
  PRODUCTS: {
    LIST: '/products',
    CREATE: '/products',
    UPDATE: '/products/:id',
    DELETE: '/products/:id',
    BY_VENDOR: '/vendors/:vendorId/products',
  },
  ORDERS: {
    LIST: '/orders',
    CREATE: '/orders',
    UPDATE: '/orders/:id',
    BY_VENDOR: '/vendors/:vendorId/orders',
    BY_CUSTOMER: '/customers/:customerId/orders',
  },
  ANALYTICS: {
    DASHBOARD: '/analytics/dashboard',
    VENDOR_STATS: '/analytics/vendor/:id',
    PLATFORM_STATS: '/analytics/platform',
  },
} as const;

// Storage keys
export const STORAGE_KEYS = {
  USER_TOKEN: '@vendorhub_user_token',
  USER_DATA: '@vendorhub_user_data',
  CART_ITEMS: '@vendorhub_cart_items',
  THEME_MODE: '@vendorhub_theme_mode',
  ONBOARDING_COMPLETED: '@vendorhub_onboarding_completed',
} as const;

// Validation rules
export const VALIDATION = {
  EMAIL_REGEX: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  PHONE_REGEX: /^\+?[\d\s\-\(\)]+$/,
  PASSWORD_MIN_LENGTH: 6,
  BUSINESS_NAME_MIN_LENGTH: 2,
  BUSINESS_NAME_MAX_LENGTH: 50,
  PRODUCT_NAME_MIN_LENGTH: 3,
  PRODUCT_NAME_MAX_LENGTH: 100,
  DESCRIPTION_MAX_LENGTH: 500,
} as const;

// Pagination
export const PAGINATION = {
  DEFAULT_PAGE_SIZE: 10,
  MAX_PAGE_SIZE: 50,
} as const;

// Currency Configuration
export const CURRENCY = {
  CODE: 'BHD',
  SYMBOL: 'BD',
  NAME: 'Bahraini Dinar',
  COUNTRY: 'BH',
  COUNTRY_NAME: 'Bahrain',
  LOCALE: 'ar-BH',
  DECIMAL_PLACES: 3, // BHD uses 3 decimal places (fils)
  SUBUNIT: 'fils',
  SUBUNIT_RATIO: 1000, // 1 BHD = 1000 fils
} as const;

// Image constraints
export const IMAGE_CONSTRAINTS = {
  MAX_SIZE_MB: 5,
  MAX_WIDTH: 1920,
  MAX_HEIGHT: 1920,
  QUALITY: 0.8,
  ALLOWED_TYPES: ['image/jpeg', 'image/png', 'image/webp'],
} as const;

// Animation durations
export const ANIMATION_DURATION = {
  FAST: 200,
  MEDIUM: 300,
  SLOW: 500,
  VERY_SLOW: 1000,
} as const;

// Notification types
export const NOTIFICATION_TYPES = {
  SUCCESS: 'success',
  ERROR: 'error',
  WARNING: 'warning',
  INFO: 'info',
} as const;

export type NotificationType = typeof NOTIFICATION_TYPES[keyof typeof NOTIFICATION_TYPES];

// Error messages
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Network error. Please check your connection.',
  UNAUTHORIZED: 'You are not authorized to perform this action.',
  VALIDATION_ERROR: 'Please check your input and try again.',
  SERVER_ERROR: 'Server error. Please try again later.',
  NOT_FOUND: 'The requested resource was not found.',
  TIMEOUT: 'Request timeout. Please try again.',
} as const;

// Success messages
export const SUCCESS_MESSAGES = {
  LOGIN_SUCCESS: 'Login successful!',
  REGISTRATION_SUCCESS: 'Registration successful!',
  VENDOR_APPROVED: 'Vendor approved successfully!',
  VENDOR_REJECTED: 'Vendor rejected successfully!',
  PRODUCT_CREATED: 'Product created successfully!',
  PRODUCT_UPDATED: 'Product updated successfully!',
  ORDER_PLACED: 'Order placed successfully!',
  ORDER_UPDATED: 'Order status updated successfully!',
} as const;

// Feature flags
export const FEATURE_FLAGS = {
  BIOMETRIC_AUTH: true,
  PUSH_NOTIFICATIONS: true,
  OFFLINE_MODE: true,
  ANALYTICS: true,
  DARK_MODE: true,
} as const;

// Export theme constants
export * from './theme';
export * from './advancedColors';
