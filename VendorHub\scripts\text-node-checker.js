#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Configuration
const config = {
  srcDir: path.join(__dirname, '../src'),
  appDir: path.join(__dirname, '../app'),
  componentsDir: path.join(__dirname, '../components'),
  outputFile: path.join(__dirname, '../TEXT_NODE_ISSUES_REPORT.md'),
  excludePatterns: [
    /node_modules/,
    /\.git/,
    /\.expo/,
    /dist/,
    /build/,
    /coverage/,
    /\.DS_Store/,
    /\.env/,
    /package-lock\.json/,
    /yarn\.lock/,
    /\.log$/,
    /\.md$/,
    /\.json$/,
    /\.js\.map$/,
    /\.d\.ts$/,
    /constants/,
    /utils/,
    /services/,
    /hooks/,
    /contexts/,
    /navigation/,
    /scripts/,
    /assets/,
  ],
  fileExtensions: ['.tsx', '.ts', '.jsx', '.js'],
};

class TextNodeChecker {
  constructor() {
    this.results = {
      potentialIssues: [],
      totalFilesScanned: 0,
    };
  }

  shouldExcludeFile(filePath) {
    const relativePath = path.relative(process.cwd(), filePath);
    return config.excludePatterns.some(pattern => pattern.test(relativePath));
  }

  hasValidExtension(filePath) {
    return config.fileExtensions.some(ext => filePath.endsWith(ext));
  }

  analyzeFileContent(filePath, content) {
    const relativePath = path.relative(process.cwd(), filePath);
    const lines = content.split('\n');
    
    const fileResult = {
      path: relativePath,
      issues: []
    };

    lines.forEach((line, index) => {
      const lineNumber = index + 1;
      const trimmedLine = line.trim();
      
      // Check for potential text node issues
      const patterns = [
        // Direct string in JSX that might not be wrapped in Text
        {
          regex: />\s*['"`][^<>]*['"`]\s*</g,
          description: 'Direct string in JSX - might need Text wrapper'
        },
        // Template literals in JSX
        {
          regex: />\s*\{[^}]*`[^`]*`[^}]*\}\s*</g,
          description: 'Template literal in JSX - ensure proper Text wrapper'
        },
        // Conditional expressions that might return strings
        {
          regex: />\s*\{[^}]*\?\s*['"`][^'"`]*['"`]\s*:/g,
          description: 'Conditional expression with string - ensure Text wrapper'
        },
        // String interpolation that might not be wrapped
        {
          regex: />\s*\{[^}]*\$\{[^}]*\}[^}]*\}\s*</g,
          description: 'String interpolation - ensure Text wrapper'
        },
        // Variables that might contain strings
        {
          regex: />\s*\{[a-zA-Z_$][a-zA-Z0-9_$]*\}\s*</g,
          description: 'Variable in JSX - ensure it returns JSX or is wrapped in Text'
        },
        // Function calls that might return strings
        {
          regex: />\s*\{[a-zA-Z_$][a-zA-Z0-9_$]*\([^)]*\)\}\s*</g,
          description: 'Function call in JSX - ensure it returns JSX or is wrapped in Text'
        }
      ];

      patterns.forEach(pattern => {
        if (pattern.regex.test(trimmedLine)) {
          // Skip if it's already inside a Text component
          if (!trimmedLine.includes('<RTLText') && !trimmedLine.includes('<Text')) {
            fileResult.issues.push({
              line: lineNumber,
              content: trimmedLine,
              description: pattern.description,
              severity: 'warning'
            });
          }
        }
      });

      // Check for empty JSX expressions that might cause issues
      if (/>\s*\{\s*\}\s*</g.test(trimmedLine)) {
        fileResult.issues.push({
          line: lineNumber,
          content: trimmedLine,
          description: 'Empty JSX expression - might cause rendering issues',
          severity: 'info'
        });
      }

      // Check for potential whitespace issues
      if (/>\s+</g.test(trimmedLine) && !trimmedLine.includes('<RTLText') && !trimmedLine.includes('<Text')) {
        fileResult.issues.push({
          line: lineNumber,
          content: trimmedLine,
          description: 'Whitespace between JSX elements - might cause text node issues',
          severity: 'info'
        });
      }
    });

    if (fileResult.issues.length > 0) {
      this.results.potentialIssues.push(fileResult);
    }
  }

  scanDirectory(dirPath) {
    if (!fs.existsSync(dirPath)) {
      return;
    }

    const items = fs.readdirSync(dirPath);

    items.forEach(item => {
      const itemPath = path.join(dirPath, item);
      const stat = fs.statSync(itemPath);

      if (stat.isDirectory()) {
        this.scanDirectory(itemPath);
      } else if (stat.isFile()) {
        if (this.hasValidExtension(itemPath) && !this.shouldExcludeFile(itemPath)) {
          try {
            const content = fs.readFileSync(itemPath, 'utf8');
            this.analyzeFileContent(itemPath, content);
            this.results.totalFilesScanned++;
          } catch (error) {
            console.warn(`Warning: Could not read file ${itemPath}: ${error.message}`);
          }
        }
      }
    });
  }

  generateReport() {
    const report = [];
    
    report.push('# Text Node Issues Report');
    report.push('');
    report.push(`Generated on: ${new Date().toISOString()}`);
    report.push(`Files scanned: ${this.results.totalFilesScanned}`);
    report.push(`Files with potential issues: ${this.results.potentialIssues.length}`);
    report.push('');

    if (this.results.potentialIssues.length === 0) {
      report.push('🎉 No potential text node issues found!');
      report.push('');
      return report.join('\n');
    }

    report.push('## Files with Potential Text Node Issues');
    report.push('');

    this.results.potentialIssues.forEach(file => {
      report.push(`### ${file.path}`);
      report.push('');
      
      file.issues.forEach(issue => {
        const severity = issue.severity === 'warning' ? '⚠️' : 'ℹ️';
        report.push(`${severity} **Line ${issue.line}**: ${issue.description}`);
        report.push('```tsx');
        report.push(issue.content);
        report.push('```');
        report.push('');
      });
    });

    report.push('## Recommendations');
    report.push('');
    report.push('1. **Wrap strings in Text components**: All text content should be wrapped in `<RTLText>` or `<Text>` components');
    report.push('2. **Check conditional expressions**: Ensure conditional expressions that might return strings are properly wrapped');
    report.push('3. **Validate function returns**: Ensure functions called in JSX return proper JSX elements or are wrapped in Text');
    report.push('4. **Remove empty expressions**: Clean up empty JSX expressions `{}`');
    report.push('5. **Handle whitespace**: Be careful with whitespace between JSX elements');

    return report.join('\n');
  }

  run() {
    console.log('🔍 Scanning for potential text node issues...');
    
    // Scan directories
    [config.srcDir, config.appDir, config.componentsDir].forEach(dir => {
      if (fs.existsSync(dir)) {
        console.log(`Scanning: ${dir}`);
        this.scanDirectory(dir);
      }
    });

    // Generate and save report
    const report = this.generateReport();
    fs.writeFileSync(config.outputFile, report);
    
    console.log(`📄 Report saved to: ${config.outputFile}`);
    console.log(`📊 Files scanned: ${this.results.totalFilesScanned}`);
    console.log(`⚠️  Files with potential issues: ${this.results.potentialIssues.length}`);

    if (this.results.potentialIssues.length > 0) {
      console.log('');
      console.log('🔧 Files that might have text node issues:');
      this.results.potentialIssues.forEach(file => {
        console.log(`   - ${file.path} (${file.issues.length} issues)`);
      });
    } else {
      console.log('');
      console.log('🎉 No potential text node issues found!');
    }

    return this.results.potentialIssues.length === 0 ? 0 : 1;
  }
}

// Run the checker
const checker = new TextNodeChecker();
const exitCode = checker.run();
process.exit(exitCode);
