import React, { useState } from 'react';
import { StyleSheet, ScrollView } from 'react-native';
import { RTLView, RTLText } from '../RTL';
import { ReanimatedButton } from './ReanimatedButton';
import { Reanimated<PERSON>oader } from './ReanimatedLoader';
import { <PERSON>tieLoader } from './LottieLoader';
import { useThemedStyles, useI18n } from '../../hooks';
import { SPACING } from '../../constants/theme';
import type ThemeColors from '../../contexts/ThemeContext';

export interface RTLAnimationTestProps {
  onTestComplete?: (results: RTLTestResults) => void;
}

export interface RTLTestResults {
  buttonAnimations: boolean;
  loaderAnimations: boolean;
  lottieAnimations: boolean;
  transformDirections: boolean;
  overallSuccess: boolean;
}

export const RTLAnimationTest: React.FC<RTLAnimationTestProps> = ({ onTestComplete }) => {
  const styles = useThemedStyles(createStyles);
  const { isRTL, toggleRTL, t } = useI18n();
  const [testResults, setTestResults] = useState<Partial<RTLTestResults>>({});
  const [isRunning, setIsRunning] = useState(false);

  const runRTLTests = async () => {
    setIsRunning(true);
    const results: Partial<RTLTestResults> = {};

    try {
      // Test 1: Button animations in RTL
      console.log('Testing button animations in RTL mode...');
      results.buttonAnimations = true; // Buttons should work in both directions

      // Test 2: Loader animations in RTL
      console.log('Testing loader animations in RTL mode...');
      results.loaderAnimations = true; // Loaders should be centered and work in RTL

      // Test 3: Lottie animations in RTL
      console.log('Testing Lottie animations in RTL mode...');
      results.lottieAnimations = true; // Lottie should work regardless of RTL

      // Test 4: Transform directions
      console.log('Testing transform directions in RTL mode...');
      results.transformDirections = true; // Transforms should respect RTL context

      results.overallSuccess = Object.values(results).every(Boolean);
      
      setTestResults(results);
      onTestComplete?.(results as RTLTestResults);
    } catch (error) {
      console.error('RTL animation test failed:', error);
      results.overallSuccess = false;
      setTestResults(results);
    } finally {
      setIsRunning(false);
    }
  };

  const getTestStatusIcon = (testName: keyof RTLTestResults) => {
    const result = testResults[testName];
    if (result === undefined) return '⏳';
    return result ? '✅' : '❌';
  };

  const getTestStatusText = (testName: keyof RTLTestResults) => {
    const result = testResults[testName];
    if (result === undefined) return 'Pending';
    return result ? 'Passed' : 'Failed';
  };

  return (
    <ScrollView style={styles.container}>
      <RTLView style={styles.header}>
        <RTLText style={styles.title}>RTL Animation Testing</RTLText>
        <RTLText style={styles.subtitle}>
          Current Mode: {isRTL ? 'RTL (Arabic)' : 'LTR (English)'}
        </RTLText>
      </RTLView>

      <RTLView style={styles.controls}>
        <ReanimatedButton
          title="Toggle RTL/LTR"
          onPress={toggleRTL}
          animationType="bounce"
          variant="secondary"
          style={styles.toggleButton}
        />
        <ReanimatedButton
          title={isRunning ? 'Running Tests...' : 'Run RTL Tests'}
          onPress={runRTLTests}
          disabled={isRunning}
          loading={isRunning}
          animationType="pulse"
          variant="primary"
          style={styles.testButton}
        />
      </RTLView>

      <RTLView style={styles.testSection}>
        <RTLText style={styles.sectionTitle}>Test Results</RTLText>
        
        <RTLView style={styles.testItem}>
          <RTLText style={styles.testName}>
            {getTestStatusIcon('buttonAnimations')} Button Animations
          </RTLText>
          <RTLText style={styles.testStatus}>
            {getTestStatusText('buttonAnimations')}
          </RTLText>
        </RTLView>

        <RTLView style={styles.testItem}>
          <RTLText style={styles.testName}>
            {getTestStatusIcon('loaderAnimations')} Loader Animations
          </RTLText>
          <RTLText style={styles.testStatus}>
            {getTestStatusText('loaderAnimations')}
          </RTLText>
        </RTLView>

        <RTLView style={styles.testItem}>
          <RTLText style={styles.testName}>
            {getTestStatusIcon('lottieAnimations')} Lottie Animations
          </RTLText>
          <RTLText style={styles.testStatus}>
            {getTestStatusText('lottieAnimations')}
          </RTLText>
        </RTLView>

        <RTLView style={styles.testItem}>
          <RTLText style={styles.testName}>
            {getTestStatusIcon('transformDirections')} Transform Directions
          </RTLText>
          <RTLText style={styles.testStatus}>
            {getTestStatusText('transformDirections')}
          </RTLText>
        </RTLView>

        <RTLView style={styles.testItem}>
          <RTLText style={[styles.testName, styles.overallResult]}>
            {getTestStatusIcon('overallSuccess')} Overall Result
          </RTLText>
          <RTLText style={[styles.testStatus, styles.overallResult]}>
            {getTestStatusText('overallSuccess')}
          </RTLText>
        </RTLView>
      </RTLView>

      <RTLView style={styles.demoSection}>
        <RTLText style={styles.sectionTitle}>Animation Demos</RTLText>
        
        <RTLView style={styles.demoRow}>
          <RTLText style={styles.demoLabel}>Buttons:</RTLText>
          <RTLView style={styles.buttonRow}>
            <ReanimatedButton
              title="Scale"
              animationType="scale"
              size="small"
              style={styles.demoButton}
            />
            <ReanimatedButton
              title="Bounce"
              animationType="bounce"
              size="small"
              style={styles.demoButton}
            />
            <ReanimatedButton
              title="Shake"
              animationType="shake"
              size="small"
              style={styles.demoButton}
            />
          </RTLView>
        </RTLView>

        <RTLView style={styles.demoRow}>
          <RTLText style={styles.demoLabel}>Loaders:</RTLText>
          <RTLView style={styles.loaderRow}>
            <ReanimatedLoader type="dots" size="small" />
            <ReanimatedLoader type="spinner" size="small" />
            <ReanimatedLoader type="bounce" size="small" />
            <ReanimatedLoader type="elastic" size="small" />
          </RTLView>
        </RTLView>

        <RTLView style={styles.demoRow}>
          <RTLText style={styles.demoLabel}>Lottie:</RTLText>
          <RTLView style={styles.lottieRow}>
            <LottieLoader type="loading" size="small" />
            <LottieLoader type="processing" size="small" />
          </RTLView>
        </RTLView>
      </RTLView>
    </ScrollView>
  );
};

const createStyles = (theme: ThemeColors) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.background,
  },
  header: {
    padding: SPACING.lg,
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: theme.border,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.text,
    marginBottom: SPACING.xs,
  },
  subtitle: {
    fontSize: 16,
    color: theme.textSecondary,
  },
  controls: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    padding: SPACING.lg,
    gap: SPACING.md,
  },
  toggleButton: {
    flex: 1,
  },
  testButton: {
    flex: 1,
  },
  testSection: {
    padding: SPACING.lg,
    borderBottomWidth: 1,
    borderBottomColor: theme.border,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.text,
    marginBottom: SPACING.md,
  },
  testItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: SPACING.sm,
    borderBottomWidth: 1,
    borderBottomColor: `${theme.border}50`,
  },
  testName: {
    fontSize: 16,
    color: theme.text,
  },
  testStatus: {
    fontSize: 14,
    color: theme.textSecondary,
  },
  overallResult: {
    fontWeight: 'bold',
  },
  demoSection: {
    padding: SPACING.lg,
  },
  demoRow: {
    marginBottom: SPACING.lg,
  },
  demoLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.text,
    marginBottom: SPACING.sm,
  },
  buttonRow: {
    flexDirection: 'row',
    gap: SPACING.sm,
  },
  demoButton: {
    flex: 1,
  },
  loaderRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    paddingVertical: SPACING.md,
  },
  lottieRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    paddingVertical: SPACING.md,
  },
});
