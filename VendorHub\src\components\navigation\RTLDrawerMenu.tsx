import React, { useEffect, useRef } from 'react';
import { 
  View, 
  StyleSheet, 
  Dimensions, 
  Animated, 
  TouchableWithoutFeedback,
  StatusBar,
  Platform
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { useTheme, useI18n, useAuth } from '../../hooks';
import { RTLView, RTLText, RTLIcon, RTLTouchableOpacity, RTLScrollView } from '../RTL';
import { SPACING, FONT_SIZES, FONT_WEIGHTS, GRADIENTS, SHADOWS } from '../../constants/theme';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');
const DRAWER_WIDTH = screenWidth * 0.8;

export interface RTLDrawerMenuProps {
  isVisible: boolean;
  onClose: () => void;
  navigation?: any;
}

interface MenuItemProps {
  icon: string;
  title: string;
  onPress: () => void;
  badge?: number;
  isActive?: boolean;
}

export const RTLDrawerMenu: React.FC<RTLDrawerMenuProps> = ({
  isVisible,
  onClose,
  navigation,
}) => {
  const { colors, isDark } = useTheme();
  const { isRTL, t } = useI18n();
  const { user, logout } = useAuth();
  const insets = useSafeAreaInsets();
  
  const overlayOpacity = useRef(new Animated.Value(0)).current;
  const drawerTranslateX = useRef(new Animated.Value(isRTL ? DRAWER_WIDTH : -DRAWER_WIDTH)).current;

  useEffect(() => {
    if (isVisible) {
      Animated.parallel([
        Animated.timing(overlayOpacity, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(drawerTranslateX, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      Animated.parallel([
        Animated.timing(overlayOpacity, {
          toValue: 0,
          duration: 250,
          useNativeDriver: true,
        }),
        Animated.timing(drawerTranslateX, {
          toValue: isRTL ? DRAWER_WIDTH : -DRAWER_WIDTH,
          duration: 250,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [isVisible, isRTL]);

  const MenuItem: React.FC<MenuItemProps> = ({ icon, title, onPress, badge, isActive }) => (
    <RTLTouchableOpacity
      style={[
        styles.menuItem,
        isActive && { backgroundColor: 'rgba(255,255,255,0.1)' }
      ]}
      onPress={onPress}
      activeOpacity={0.7}
    >
      <RTLView style={styles.menuItemContent}>
        <RTLView style={styles.menuItemLeft}>
          <RTLIcon 
            name={icon} 
            size={24} 
            color={colors.textOnPrimary} 
          />
          <RTLText style={[styles.menuItemText, { color: colors.textOnPrimary }]}>
            {title}
          </RTLText>
        </RTLView>
        {badge && badge > 0 && (
          <RTLView style={[styles.menuBadge, { backgroundColor: colors.error }]}>
            <RTLText style={styles.menuBadgeText}>
              {badge > 99 ? '99+' : badge}
            </RTLText>
          </RTLView>
        )}
      </RTLView>
    </RTLTouchableOpacity>
  );

  const getMenuItems = () => {
    if (!user) return [];

    const commonItems = [
      { icon: 'home-outline', title: t('nav.home'), route: 'Home' },
      { icon: 'person-outline', title: t('nav.profile'), route: 'Profile' },
      { icon: 'settings-outline', title: t('nav.settings'), route: 'Settings' },
    ];

    if (user.role === 'admin') {
      return [
        { icon: 'analytics-outline', title: t('nav.dashboard'), route: 'Dashboard' },
        { icon: 'storefront-outline', title: t('nav.vendors'), route: 'Vendors' },
        { icon: 'cube-outline', title: t('nav.products'), route: 'Products' },
        { icon: 'receipt-outline', title: t('nav.orders'), route: 'Orders' },
        ...commonItems,
      ];
    } else if (user.role === 'vendor') {
      return [
        { icon: 'analytics-outline', title: t('nav.overview'), route: 'Overview' },
        { icon: 'cube-outline', title: t('nav.products'), route: 'Products' },
        { icon: 'receipt-outline', title: t('nav.orders'), route: 'Orders' },
        { icon: 'storefront-outline', title: t('nav.shop'), route: 'Shop' },
        ...commonItems,
      ];
    } else {
      return [
        { icon: 'home-outline', title: t('nav.home'), route: 'Home' },
        { icon: 'storefront-outline', title: t('nav.shops'), route: 'Shops' },
        { icon: 'bag-outline', title: t('nav.cart'), route: 'Cart' },
        { icon: 'chatbubbles-outline', title: t('nav.messages'), route: 'Messages' },
        { icon: 'person-outline', title: t('nav.profile'), route: 'Profile' },
      ];
    }
  };

  const handleMenuItemPress = (route: string) => {
    onClose();
    if (navigation && navigation.navigate) {
      navigation.navigate(route);
    }
  };

  const handleLogout = () => {
    onClose();
    logout();
  };

  if (!isVisible) return null;

  return (
    <View style={styles.container}>
      {/* Overlay */}
      <TouchableWithoutFeedback onPress={onClose}>
        <Animated.View 
          style={[
            styles.overlay,
            { opacity: overlayOpacity }
          ]} 
        />
      </TouchableWithoutFeedback>

      {/* Drawer */}
      <Animated.View
        style={[
          styles.drawer,
          {
            width: DRAWER_WIDTH,
            [isRTL ? 'right' : 'left']: 0,
            transform: [{ translateX: drawerTranslateX }],
          }
        ]}
      >
        <LinearGradient
          colors={isDark ? GRADIENTS.midnight : GRADIENTS.primary}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={styles.drawerContent}
        >
          {/* Header */}
          <RTLView style={[styles.drawerHeader, { paddingTop: insets.top + SPACING.md }]}>
            <RTLView style={styles.userInfo}>
              <RTLView style={styles.avatar}>
                <RTLIcon name="person" size={32} color={colors.textOnPrimary} />
              </RTLView>
              <RTLView style={styles.userDetails}>
                <RTLText style={[styles.userName, { color: colors.textOnPrimary }]}>
                  {user?.name || t('common.guest')}
                </RTLText>
                <RTLText style={[styles.userRole, { color: colors.textOnPrimary }]}>
                  {user?.role ? t(`roles.${user.role}`) : ''}
                </RTLText>
              </RTLView>
            </RTLView>
            <RTLTouchableOpacity
              style={styles.closeButton}
              onPress={onClose}
              accessibilityLabel={t('nav.close')}
            >
              <RTLIcon name="close" size={24} color={colors.textOnPrimary} />
            </RTLTouchableOpacity>
          </RTLView>

          {/* Menu Items */}
          <RTLScrollView style={styles.menuContainer} showsVerticalScrollIndicator={false}>
            {getMenuItems().map((item, index) => (
              <MenuItem
                key={index}
                icon={item.icon}
                title={item.title}
                onPress={() => handleMenuItemPress(item.route)}
              />
            ))}
            
            {/* Divider */}
            <RTLView style={[styles.divider, { backgroundColor: 'rgba(255,255,255,0.2)' }]} />
            
            {/* Logout */}
            <MenuItem
              icon="log-out-outline"
              title={t('auth.logout')}
              onPress={handleLogout}
            />
          </RTLScrollView>
        </LinearGradient>
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1000,
  },
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  drawer: {
    position: 'absolute',
    top: 0,
    bottom: 0,
    ...SHADOWS.large,
  },
  drawerContent: {
    flex: 1,
  },
  drawerHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: SPACING.lg,
    paddingBottom: SPACING.lg,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255,255,255,0.2)',
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(255,255,255,0.2)',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: SPACING.md,
  },
  userDetails: {
    flex: 1,
  },
  userName: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.semibold,
  },
  userRole: {
    fontSize: FONT_SIZES.sm,
    opacity: 0.8,
    marginTop: 2,
  },
  closeButton: {
    padding: SPACING.xs,
  },
  menuContainer: {
    flex: 1,
    paddingTop: SPACING.md,
  },
  menuItem: {
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
  },
  menuItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  menuItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  menuItemText: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.medium,
    marginLeft: SPACING.md,
  },
  menuBadge: {
    minWidth: 20,
    height: 20,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 6,
  },
  menuBadgeText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: FONT_WEIGHTS.bold,
  },
  divider: {
    height: 1,
    marginVertical: SPACING.md,
    marginHorizontal: SPACING.lg,
  },
});
