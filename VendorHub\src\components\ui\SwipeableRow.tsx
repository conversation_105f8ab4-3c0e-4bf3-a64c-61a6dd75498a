import React, { useRef } from 'react';
import { StyleSheet, Animated, Dimensions, Platform } from 'react-native';
import { RTLIcon, RTLText, RTLTouchableOpacity, RTLView } from '../RTL';
import { Swipeable, GestureHandlerRootView } from 'react-native-gesture-handler';
import * as Haptics from 'expo-haptics';
import { useThemedStyles } from '../../hooks';
import { webCompatibleHaptics, isWeb } from '../../utils/webCompatibility';
import {
  SPACING,
  FONT_SIZES,
  FONT_WEIGHTS,
  BORDER_RADIUS } from '../../constants/theme';
import type ThemeColors  from '../../contexts/ThemeContext';

const { width: screenWidth } = Dimensions.get('window');

export interface SwipeAction {
  id: string;
  title: string;
  icon: string;
  color: string;
  backgroundColor: string;
  onPress: () => void;
}

export interface SwipeableRowProps {
  children: React.ReactNode;
  leftActions?: SwipeAction[];
  rightActions?: SwipeAction[];
  onSwipeStart?: () => void;
  onSwipeEnd?: () => void;
  disabled?: boolean;
  style?: any;
}

export const SwipeableRow: React.FC<SwipeableRowProps> = ({
  children,
  leftActions = [],
  rightActions = [],
  onSwipeStart,
  onSwipeEnd,
  disabled = false,
  style,
}) => {
  const styles = useThemedStyles(createStyles);
  const swipeableRef = useRef<Swipeable>(null);

  const handleSwipeStart = () => {
    webCompatibleHaptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    onSwipeStart?.();
  };

  const handleSwipeEnd = () => {
    onSwipeEnd?.();
  };

  const handleActionPress = (action: SwipeAction) => {
    // Provide haptic feedback
    webCompatibleHaptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

    // Close the swipeable
    swipeableRef.current?.close();

    // Execute the action
    action.onPress();
  };

  const renderLeftActions = (
    progress: Animated.AnimatedAddition<number>,
    dragX: Animated.AnimatedAddition<number>
  ) => {
    if (leftActions.length === 0) return null;

    const actionWidth = 80;
    const totalWidth = leftActions.length * actionWidth;

    return (
      <RTLView style={[styles.actionsContainer, { width: totalWidth }]}>
        {leftActions.map((action, index) => {
          const trans = progress.interpolate({
            inputRange: [0, 1],
            outputRange: [-actionWidth, 0],
            extrapolate: 'clamp',
          });

          const scale = progress.interpolate({
            inputRange: [0, 1],
            outputRange: [0, 1],
            extrapolate: 'clamp',
          });

          return (
            <Animated.View
              key={action.id}
              style={[
                styles.actionButton,
                {
                  backgroundColor: action.backgroundColor,
                  width: actionWidth,
                  transform: [{ translateX: trans }, { scale }],
                },
              ]}
            >
              <RTLTouchableOpacity
                style={styles.actionTouchable}
                onPress={() => handleActionPress(action)}
                activeOpacity={0.7}
              >
                <RTLIcon
                  name={action.icon as any}
                  size={24}
                  color={action.color}
                />
                <RTLText style={[styles.actionText, { color: action.color }]}>
                  {action.title}
                </RTLText>
              </RTLTouchableOpacity>
            </Animated.View>
          );
        })}
      </RTLView>
    );
  };

  const renderRightActions = (
    progress: Animated.AnimatedAddition<number>,
    dragX: Animated.AnimatedAddition<number>
  ) => {
    if (rightActions.length === 0) return null;

    const actionWidth = 80;
    const totalWidth = rightActions.length * actionWidth;

    return (
      <RTLView style={[styles.actionsContainer, { width: totalWidth }]}>
        {rightActions.map((action, index) => {
          const trans = progress.interpolate({
            inputRange: [0, 1],
            outputRange: [actionWidth, 0],
            extrapolate: 'clamp',
          });

          const scale = progress.interpolate({
            inputRange: [0, 1],
            outputRange: [0, 1],
            extrapolate: 'clamp',
          });

          return (
            <Animated.View
              key={action.id}
              style={[
                styles.actionButton,
                {
                  backgroundColor: action.backgroundColor,
                  width: actionWidth,
                  transform: [{ translateX: trans }, { scale }],
                },
              ]}
            >
              <RTLTouchableOpacity
                style={styles.actionTouchable}
                onPress={() => handleActionPress(action)}
                activeOpacity={0.7}
              >
                <RTLIcon
                  name={action.icon as any}
                  size={24}
                  color={action.color}
                />
                <RTLText style={[styles.actionText, { color: action.color }]}>
                  {action.title}
                </RTLText>
              </RTLTouchableOpacity>
            </Animated.View>
          );
        })}
      </RTLView>
    );
  };

  if (disabled || (leftActions.length === 0 && rightActions.length === 0)) {
    return <RTLView style={style}>{children}</RTLView>;
  }

  // For web, render a simplified version without swipe gestures
  if (isWeb) {
    return (
      <RTLView style={style}>
        <RTLView style={styles.webContainer}>
          {leftActions.length > 0 && (
            <RTLView style={styles.webActionsLeft}>
              {leftActions.map((action, index) => (
                <RTLTouchableOpacity
                  key={index}
                  style={[styles.webActionButton, { backgroundColor: action.backgroundColor }]}
                  onPress={() => handleActionPress(action)}
                >
                  <RTLIcon name={action.icon as any} size={20} color={action.color || '#FFFFFF'} />
                </RTLTouchableOpacity>
              ))}
            </RTLView>
          )}
          <RTLView style={styles.webContent}>
            {children}
          </RTLView>
          {rightActions.length > 0 && (
            <RTLView style={styles.webActionsRight}>
              {rightActions.map((action, index) => (
                <RTLTouchableOpacity
                  key={index}
                  style={[styles.webActionButton, { backgroundColor: action.backgroundColor }]}
                  onPress={() => handleActionPress(action)}
                >
                  <RTLIcon name={action.icon as any} size={20} color={action.color || '#FFFFFF'} />
                </RTLTouchableOpacity>
              ))}
            </RTLView>
          )}
        </RTLView>
      </RTLView>
    );
  }

  return (
    <GestureHandlerRootView style={style}>
      <Swipeable
        ref={swipeableRef}
        renderLeftActions={leftActions.length > 0 ? renderLeftActions : undefined}
        renderRightActions={rightActions.length > 0 ? renderRightActions : undefined}
        onSwipeableOpen={handleSwipeStart}
        onSwipeableClose={handleSwipeEnd}
        leftThreshold={40}
        rightThreshold={40}
        friction={2}
        overshootLeft={false}
        overshootRight={false}
      >
        {children}
      </Swipeable>
    </GestureHandlerRootView>
  );
};

const createStyles = (colors: ThemeColors) => StyleSheet.create({
  actionsContainer: {
    flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
    alignItems: 'center',
  },
  actionButton: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    height: '100%',
  },
  actionTouchable: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
    paddingHorizontal: SPACING.xs,
  },
  actionText: {
    fontSize: FONT_SIZES.xs,
    fontWeight: FONT_WEIGHTS.medium,
    marginTop: SPACING.xs,
    textAlign: 'center',
  },
  // Web-specific styles
  webContainer: {
    flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
    alignItems: 'center',
  },
  webContent: {
    flex: 1,
  },
  webActionsLeft: {
    flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
    marginRight: SPACING.sm,
  },
  webActionsRight: {
    flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
    marginLeft: SPACING.sm,
  },
  webActionButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: SPACING.xs,
  },
});

// Predefined action configurations for common use cases
export const SwipeActions = {
  approve: (onPress: () => void): SwipeAction => ({
    id: 'approve',
    title: 'Approve',
    icon: 'checkmark-circle',
    color: '#FFFFFF',
    backgroundColor: '#4CAF50',
    onPress,
  }),
  
  reject: (onPress: () => void): SwipeAction => ({
    id: 'reject',
    title: 'Reject',
    icon: 'close-circle',
    color: '#FFFFFF',
    backgroundColor: '#F44336',
    onPress,
  }),
  
  delete: (onPress: () => void): SwipeAction => ({
    id: 'delete',
    title: 'Delete',
    icon: 'trash',
    color: '#FFFFFF',
    backgroundColor: '#FF6B6B',
    onPress,
  }),
  
  edit: (onPress: () => void): SwipeAction => ({
    id: 'edit',
    title: 'Edit',
    icon: 'pencil',
    color: '#FFFFFF',
    backgroundColor: '#2196F3',
    onPress,
  }),
  
  archive: (onPress: () => void): SwipeAction => ({
    id: 'archive',
    title: 'Archive',
    icon: 'archive',
    color: '#FFFFFF',
    backgroundColor: '#FF9800',
    onPress,
  }),
  
  favorite: (onPress: () => void): SwipeAction => ({
    id: 'favorite',
    title: 'Favorite',
    icon: 'heart',
    color: '#FFFFFF',
    backgroundColor: '#E91E63',
    onPress,
  }),
  
  share: (onPress: () => void): SwipeAction => ({
    id: 'share',
    title: 'Share',
    icon: 'share',
    color: '#FFFFFF',
    backgroundColor: '#9C27B0',
    onPress,
  }),
  
  view: (onPress: () => void): SwipeAction => ({
    id: 'view',
    title: 'View',
    icon: 'eye',
    color: '#FFFFFF',
    backgroundColor: '#607D8B',
    onPress,
  }),
};
