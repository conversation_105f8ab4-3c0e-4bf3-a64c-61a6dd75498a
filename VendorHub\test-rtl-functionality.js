#!/usr/bin/env node

/**
 * RTL Functionality Test Script
 * Tests the RTL layout switching functionality
 */

const I18nService = require('./src/services/I18nService.ts');

console.log('🧪 Testing RTL Functionality...\n');

// Test 1: Check initial state
console.log('📋 Test 1: Initial State');
console.log(`Current Language: ${I18nService.getCurrentLanguage()}`);
console.log(`Is RTL: ${I18nService.isRTL()}`);
console.log(`Text Align: ${I18nService.getTextAlign()}`);
console.log(`Flex Direction: ${I18nService.getFlexDirection()}`);
console.log(`Writing Direction: ${I18nService.getWritingDirection()}\n`);

// Test 2: Switch to Arabic
console.log('📋 Test 2: Switch to Arabic');
try {
  I18nService.setLanguage('ar');
  console.log(`✅ Language switched to: ${I18nService.getCurrentLanguage()}`);
  console.log(`✅ Is RTL: ${I18nService.isRTL()}`);
  console.log(`✅ Text Align: ${I18nService.getTextAlign()}`);
  console.log(`✅ Flex Direction: ${I18nService.getFlexDirection()}`);
  console.log(`✅ Writing Direction: ${I18nService.getWritingDirection()}\n`);
} catch (error) {
  console.log(`❌ Error switching to Arabic: ${error.message}\n`);
}

// Test 3: Switch back to English
console.log('📋 Test 3: Switch to English');
try {
  I18nService.setLanguage('en');
  console.log(`✅ Language switched to: ${I18nService.getCurrentLanguage()}`);
  console.log(`✅ Is RTL: ${I18nService.isRTL()}`);
  console.log(`✅ Text Align: ${I18nService.getTextAlign()}`);
  console.log(`✅ Flex Direction: ${I18nService.getFlexDirection()}`);
  console.log(`✅ Writing Direction: ${I18nService.getWritingDirection()}\n`);
} catch (error) {
  console.log(`❌ Error switching to English: ${error.message}\n`);
}

// Test 4: Test translation
console.log('📋 Test 4: Translation Test');
try {
  const englishWelcome = I18nService.t('home.welcomeTo');
  console.log(`✅ English Welcome: "${englishWelcome}"`);
  
  I18nService.setLanguage('ar');
  const arabicWelcome = I18nService.t('home.welcomeTo');
  console.log(`✅ Arabic Welcome: "${arabicWelcome}"`);
  
  I18nService.setLanguage('en');
} catch (error) {
  console.log(`❌ Error testing translations: ${error.message}`);
}

console.log('\n🎉 RTL Functionality Test Complete!');
