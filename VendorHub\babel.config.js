module.exports = function (api) {
  api.cache(true);
  return {
    presets: ['babel-preset-expo'],
    plugins: [
      // Add react-native-web plugin for better web support
      ['react-native-web', { commonjs: true }],
      // Add gesture handler web support
      'react-native-reanimated/plugin',
    ],
    env: {
      production: {
        plugins: ['react-native-paper/babel'],
      },
      web: {
        plugins: [
          // Web-specific optimizations
          ['react-native-web', { commonjs: true }],
          // Add Reanimated plugin for web support
          'react-native-reanimated/plugin',
        ],
      },
    },
  };
};
