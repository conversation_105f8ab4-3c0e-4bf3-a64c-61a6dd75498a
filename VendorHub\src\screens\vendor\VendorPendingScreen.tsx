import React from 'react';
import { StyleSheet } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useThemedStyles, useAuth, useI18n } from '../../hooks';
import { <PERSON>, Button } from '../../components';
import { RTLView, RTLText, RTLScrollView, RTLSafeAreaView, RTLIcon, RTLTouchableOpacity } from '../../components/RTL';
import {
  SPACING,
  FONT_SIZES,
  FONT_WEIGHTS,
  GRADIENTS,
  ICON_SIZES } from '../../constants/theme';
import { APP_NAME } from '../../constants';
import type ThemeColors  from '../../contexts/ThemeContext';

interface VendorPendingScreenProps {
  navigation: any;
}

export const VendorPendingScreen: React.FC<VendorPendingScreenProps> = ({ navigation }) => {
  const styles = useThemedStyles(createStyles);
  const { user, logout } = useAuth();
  const { t } = useI18n();

  const handleLogout = async () => {
    await logout();
  };

  const handleContactSupport = () => {
    // In a real app, this would open email or support chat
    console.log('Contact support');
  };

  return (
    <RTLSafeAreaView style={styles.container}>
      <RTLScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <LinearGradient colors={GRADIENTS.primary} style={styles.header}>
          <RTLView style={styles.headerContent}>
            <RTLText style={styles.logoText}>{APP_NAME}</RTLText>
            <RTLTouchableOpacity onPress={handleLogout} style={styles.logoutButton}>
              <RTLIcon name="log-out-outline" size={24} color="#FFFFFF" />
            </RTLTouchableOpacity>
          </RTLView>
        </LinearGradient>

        {/* Main Content */}
        <RTLView style={styles.content}>
          {/* Status Icon */}
          <RTLView style={styles.iconContainer}>
            <RTLView style={styles.iconBackground}>
              <RTLIcon name="time-outline" size={ICON_SIZES.xxl} color="#FFFFFF" />
            </RTLView>
          </RTLView>

          {/* Status Message */}
          <RTLText style={styles.title}>Application Under Review</RTLText>
          <RTLText style={styles.subtitle}>
            Thank you for applying to become a vendor on {APP_NAME}!
          </RTLText>

          {/* Information Card */}
          <Card variant="elevated" style={styles.infoCard}>
            <RTLView style={styles.infoHeader}>
              <RTLIcon name="information-circle-outline" size={24} color="#667eea" />
              <RTLText style={styles.infoTitle}>What happens next?</RTLText>
            </RTLView>

            <RTLView style={styles.infoContent}>
              <RTLView style={styles.infoItem}>
                <RTLView style={styles.stepNumber}>
                  <RTLText style={styles.stepNumberText}>1</RTLText>
                </RTLView>
                <RTLText style={styles.infoText}>
                  Our team will review your application and business information
                </RTLText>
              </RTLView>

              <RTLView style={styles.infoItem}>
                <RTLView style={styles.stepNumber}>
                  <RTLText style={styles.stepNumberText}>2</RTLText>
                </RTLView>
                <RTLText style={styles.infoText}>
                  We may contact you for additional information if needed
                </RTLText>
              </RTLView>

              <RTLView style={styles.infoItem}>
                <RTLView style={styles.stepNumber}>
                  <RTLText style={styles.stepNumberText}>3</RTLText>
                </RTLView>
                <RTLText style={styles.infoText}>
                  You'll receive an email notification with our decision
                </RTLText>
              </RTLView>
            </RTLView>
          </Card>

          {/* Application Details */}
          <Card variant="outlined" style={styles.detailsCard}>
            <RTLText style={styles.detailsTitle}>Application Details</RTLText>

            <RTLView style={styles.detailRow}>
              <RTLText style={styles.detailLabel}>Business Name:</RTLText>
              <RTLText style={styles.detailValue}>{user?.businessName || 'N/A'}</RTLText>
            </RTLView>

            <RTLView style={styles.detailRow}>
              <RTLText style={styles.detailLabel}>Owner Name:</RTLText>
              <RTLText style={styles.detailValue}>{user?.name}</RTLText>
            </RTLView>

            <RTLView style={styles.detailRow}>
              <RTLText style={styles.detailLabel}>Email:</RTLText>
              <RTLText style={styles.detailValue}>{user?.email}</RTLText>
            </RTLView>

            <RTLView style={styles.detailRow}>
              <RTLText style={styles.detailLabel}>Application Date:</RTLText>
              <RTLText style={styles.detailValue}>
                {new Date(user?.createdAt || '').toLocaleDateString()}
              </RTLText>
            </RTLView>
          </Card>

          {/* Timeline */}
          <Card variant="glass" style={styles.timelineCard}>
            <RTLText style={styles.timelineTitle}>Typical Review Timeline</RTLText>
            <RTLText style={styles.timelineText}>
              Most applications are reviewed within 2-3 business days.
              Complex applications may take up to 5 business days.
            </RTLText>
          </Card>

          {/* Actions */}
          <RTLView style={styles.actions}>
            <Button
              title="Contact Support"
              onPress={handleContactSupport}
              variant="outline"
              style={styles.supportButton}
              leftIcon={<RTLIcon name="mail-outline" size={20} color="#667eea" />}
            />
            
            <Button
              title="Sign Out"
              onPress={handleLogout}
              variant="ghost"
              style={styles.logoutActionButton}
            />
          </RTLView>
        </RTLView>
      </RTLScrollView>
    </RTLSafeAreaView>
  );
};

const createStyles = (colors: ThemeColors) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    scrollView: {
      flex: 1,
    },
    scrollContent: {
      flexGrow: 1,
    },
    header: {
      paddingHorizontal: SPACING.lg,
      paddingVertical: SPACING.lg,
    },
    headerContent: {
      flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    logoText: {
      fontSize: FONT_SIZES.xl,
      fontWeight: FONT_WEIGHTS.bold,
      color: '#FFFFFF',
    },
    logoutButton: {
      padding: SPACING.sm,
    },
    content: {
      flex: 1,
      paddingHorizontal: SPACING.lg,
      paddingVertical: SPACING.xl,
      alignItems: 'center',
    },
    iconContainer: {
      marginBottom: SPACING.xl,
    },
    iconBackground: {
      width: 100,
      height: 100,
      borderRadius: 50,
      backgroundColor: '#FF9800',
      justifyContent: 'center',
      alignItems: 'center',
    },
    title: {
      fontSize: FONT_SIZES.xxl,
      fontWeight: FONT_WEIGHTS.bold,
      color: colors.textPrimary,
      textAlign: 'center',
      marginBottom: SPACING.sm,
    },
    subtitle: {
      fontSize: FONT_SIZES.md,
      color: colors.textSecondary,
      textAlign: 'center',
      marginBottom: SPACING.xl,
      lineHeight: FONT_SIZES.md * 1.4,
    },
    infoCard: {
      width: '100%',
      marginBottom: SPACING.lg,
    },
    infoHeader: {
      flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
      alignItems: 'center',
      marginBottom: SPACING.md,
    },
    infoTitle: {
      fontSize: FONT_SIZES.lg,
      fontWeight: FONT_WEIGHTS.semibold,
      color: colors.textPrimary,
      marginLeft: SPACING.sm,
    },
    infoContent: {
      gap: SPACING.md,
    },
    infoItem: {
      flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
      alignItems: 'flex-start',
    },
    stepNumber: {
      width: 24,
      height: 24,
      borderRadius: 12,
      backgroundColor: colors.primary,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: SPACING.sm,
      marginTop: 2,
    },
    stepNumberText: {
      fontSize: FONT_SIZES.sm,
      fontWeight: FONT_WEIGHTS.bold,
      color: '#FFFFFF',
    },
    infoText: {
      flex: 1,
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
      lineHeight: FONT_SIZES.sm * 1.4,
    },
    detailsCard: {
      width: '100%',
      marginBottom: SPACING.lg,
    },
    detailsTitle: {
      fontSize: FONT_SIZES.lg,
      fontWeight: FONT_WEIGHTS.semibold,
      color: colors.textPrimary,
      marginBottom: SPACING.md,
    },
    detailRow: {
      flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingVertical: SPACING.sm,
      borderBottomWidth: 1,
      borderBottomColor: colors.borderLight,
    },
    detailLabel: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
      fontWeight: FONT_WEIGHTS.medium,
    },
    detailValue: {
      fontSize: FONT_SIZES.sm,
      color: colors.textPrimary,
      fontWeight: FONT_WEIGHTS.medium,
      textAlign: 'right' , // RTL: Will be automatically flipped by RTLText component,
      flex: 1,
      marginLeft: SPACING.md,
    },
    timelineCard: {
      width: '100%',
      marginBottom: SPACING.xl,
      alignItems: 'center',
    },
    timelineTitle: {
      fontSize: FONT_SIZES.md,
      fontWeight: FONT_WEIGHTS.semibold,
      color: colors.textPrimary,
      marginBottom: SPACING.sm,
    },
    timelineText: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
      textAlign: 'center',
      lineHeight: FONT_SIZES.sm * 1.4,
    },
    actions: {
      width: '100%',
      gap: SPACING.md,
    },
    supportButton: {
      justifyContent: 'center',
    },
    logoutActionButton: {
      backgroundColor: 'transparent',
    },
  });
