import { Dimensions } from 'react-native';

const { width, height } = Dimensions.get('window');

// Exclusive Blue-Navy-Black Color Palette
export const COLORS = {
  // Primary gradient colors - Deep Blue to Navy
  primary: '#1E3A8A',        // Deep Blue
  primaryDark: '#0F172A',    // Dark Navy/Black
  primaryLight: '#3B82F6',   // Bright Blue
  accent: '#1D4ED8',         // Royal Blue

  // Secondary blues
  blue50: '#EFF6FF',         // Very Light Blue
  blue100: '#DBEAFE',        // Light Blue
  blue200: '#BFDBFE',        // Soft Blue
  blue300: '#93C5FD',        // Medium Blue
  blue400: '#60A5FA',        // Bright Blue
  blue500: '#3B82F6',        // Standard Blue
  blue600: '#2563EB',        // Deep Blue
  blue700: '#1D4ED8',        // Royal Blue
  blue800: '#1E40AF',        // Dark Blue
  blue900: '#1E3A8A',        // Very Dark Blue

  // Navy and dark tones
  navy50: '#F8FAFC',         // Very Light Navy
  navy100: '#F1F5F9',        // Light Navy
  navy200: '#E2E8F0',        // Soft Navy
  navy300: '#CBD5E1',        // Medium Navy
  navy400: '#94A3B8',        // Gray Navy
  navy500: '#64748B',        // Standard Navy
  navy600: '#475569',        // Deep Navy
  navy700: '#334155',        // Dark Navy
  navy800: '#1E293B',        // Very Dark Navy
  navy900: '#0F172A',        // Black Navy

  // Semantic colors with blue tones
  success: '#059669',        // Emerald Green
  warning: '#D97706',        // Amber Orange
  error: '#DC2626',          // Red
  info: '#0EA5E9',           // Sky Blue

  // Status colors for vendors
  approved: '#059669',
  pending: '#D97706',
  rejected: '#DC2626',

  // Neutral colors
  white: '#FFFFFF',
  black: '#000000',
  gray50: '#F9FAFB',
  gray100: '#F3F4F6',
  gray200: '#E5E7EB',
  gray300: '#D1D5DB',
  gray400: '#9CA3AF',
  gray500: '#6B7280',
  gray600: '#4B5563',
  gray700: '#374151',
  gray800: '#1F2937',
  gray900: '#111827',

  // Background colors
  background: '#FFFFFF',
  backgroundSecondary: '#F8FAFC',
  backgroundDark: '#0F172A',
  surface: '#FFFFFF',
  surfaceDark: '#1E293B',

  // Text colors
  textPrimary: '#0F172A',
  textSecondary: '#64748B',
  textDisabled: '#94A3B8',
  textOnPrimary: '#FFFFFF',
  textOnDark: '#F1F5F9',

  // Border colors
  border: '#E2E8F0',
  borderLight: '#F1F5F9',
  borderDark: '#334155',

  // Glassmorphism colors
  glassBackground: 'rgba(30, 58, 138, 0.1)',
  glassBorder: 'rgba(59, 130, 246, 0.2)',
  glassBackgroundDark: 'rgba(15, 23, 42, 0.8)',
  glassBorderDark: 'rgba(51, 65, 85, 0.3)',

  // Dark mode colors - Enhanced Navy/Black theme
  dark: {
    background: '#0F172A',        // Black Navy
    backgroundSecondary: '#1E293B', // Very Dark Navy
    surface: '#334155',           // Dark Navy
    surfaceElevated: '#475569',   // Deep Navy
    textPrimary: '#F1F5F9',       // Light Navy
    textSecondary: '#CBD5E1',     // Soft Navy
    textTertiary: '#94A3B8',      // Gray Navy
    border: '#475569',            // Deep Navy
    borderLight: '#334155',       // Dark Navy
    accent: '#3B82F6',            // Bright Blue
    accentDark: '#1D4ED8',        // Royal Blue
  }
};

// Typography
export const FONTS = {
  regular: 'System',
  medium: 'System',
  bold: 'System',
  light: 'System',
};

export const FONT_SIZES = {
  xs: 12,
  sm: 14,
  md: 16,
  lg: 18,
  xl: 20,
  xxl: 24,
  xxxl: 32,
  display: 40,
};

export const FONT_WEIGHTS = {
  light: '300' as const,
  regular: '400' as const,
  medium: '500' as const,
  semibold: '600' as const,
  semiBold: '600' as const, // Alias for consistency
  bold: '700' as const,
};

// Spacing
export const SPACING = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,
  xxxl: 64,
};

// Border radius
export const BORDER_RADIUS = {
  xs: 4,
  sm: 8,
  md: 12,
  lg: 16,
  xl: 24,
  round: 50,
  full: 9999,
};

// Shadows
export const SHADOWS = {
  small: {
    shadowColor: COLORS.black,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  medium: {
    shadowColor: COLORS.black,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.15,
    shadowRadius: 6.27,
    elevation: 10,
  },
  large: {
    shadowColor: COLORS.black,
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.2,
    shadowRadius: 10.32,
    elevation: 16,
  },
};

// Screen dimensions
export const SCREEN = {
  width,
  height,
  isSmall: width < 375,
  isMedium: width >= 375 && width < 414,
  isLarge: width >= 414,
};

// Animation durations
export const ANIMATION = {
  fast: 200,
  medium: 300,
  slow: 500,
};

// Z-index values
export const Z_INDEX = {
  modal: 1000,
  overlay: 900,
  dropdown: 800,
  header: 700,
  fab: 600,
};

// Exclusive Blue-Navy-Black Gradients
export const GRADIENTS = {
  // Primary gradients
  primary: ['#1E3A8A', '#0F172A'] as const,           // Deep Blue to Black Navy
  primaryLight: ['#3B82F6', '#1E3A8A'] as const,     // Bright Blue to Deep Blue
  primaryReverse: ['#0F172A', '#1E3A8A'] as const,   // Black Navy to Deep Blue

  // Secondary gradients
  royal: ['#1D4ED8', '#1E40AF'] as const,            // Royal Blue to Dark Blue
  ocean: ['#0EA5E9', '#1E3A8A'] as const,            // Sky Blue to Deep Blue
  midnight: ['#1E293B', '#0F172A'] as const,         // Very Dark Navy to Black Navy

  // Semantic gradients
  success: ['#059669', '#047857'] as const,          // Emerald Green
  warning: ['#D97706', '#B45309'] as const,          // Amber Orange
  error: ['#DC2626', '#B91C1C'] as const,            // Red
  info: ['#0EA5E9', '#0284C7'] as const,             // Sky Blue

  // Glass effects
  glass: ['rgba(30, 58, 138, 0.15)', 'rgba(15, 23, 42, 0.1)'] as const,
  glassDark: ['rgba(15, 23, 42, 0.8)', 'rgba(30, 41, 59, 0.6)'] as const,

  // Special effects
  aurora: ['#1E3A8A', '#3B82F6', '#0EA5E9'] as const, // Multi-blue aurora
  depth: ['#0F172A', '#1E293B', '#334155'] as const,  // Deep navy layers
};

// Icon sizes
export const ICON_SIZES = {
  xs: 16,
  sm: 20,
  md: 24,
  lg: 32,
  xl: 40,
  xxl: 48,
};

// Component specific constants
export const HEADER_HEIGHT = 60;
export const TAB_BAR_HEIGHT = 80;
export const CARD_BORDER_RADIUS = BORDER_RADIUS.md;
export const BUTTON_HEIGHT = 48;
export const INPUT_HEIGHT = 48;

export default {
  COLORS,
  FONTS,
  FONT_SIZES,
  FONT_WEIGHTS,
  SPACING,
  BORDER_RADIUS,
  SHADOWS,
  SCREEN,
  ANIMATION,
  Z_INDEX,
  GRADIENTS,
  ICON_SIZES,
  HEADER_HEIGHT,
  TAB_BAR_HEIGHT,
  CARD_BORDER_RADIUS,
  BUTTON_HEIGHT,
  INPUT_HEIGHT,
};
