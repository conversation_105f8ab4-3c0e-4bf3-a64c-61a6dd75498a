import React from 'react';
import { StatusBar } from 'expo-status-bar';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { useFonts } from 'expo-font';
import { Slot } from 'expo-router';
import 'react-native-reanimated';

// Import web polyfills and gesture handler configuration
import '../src/utils/webPolyfills';
import '../src/utils/gestureHandlerWeb';

import { AuthProvider } from '../src/contexts/AuthContext';
import { DataProvider } from '../src/contexts/DataContext';
import { ThemeProvider } from '../src/contexts/ThemeContext';
import { PersonalizationEngineProvider, SmartRecommendationEngineProvider } from '../src/components/ai';
import { IntelligentCacheProvider, NetworkOptimizerProvider } from '../src/components/performance';
import { OfflineIndicator } from '../src/components';
import { StyleSheet } from 'react-native';

export default function RootLayout() {
  const [loaded] = useFonts({
    SpaceMono: require('../assets/fonts/SpaceMono-Regular.ttf'),
  });

  if (!loaded) {
    // Async font loading only occurs in development.
    return null;
  }

  return (
    <GestureHandlerRootView style={styles.container}>
      <SafeAreaProvider>
        <ThemeProvider>
          <AuthProvider>
            <DataProvider>
              <IntelligentCacheProvider>
                <NetworkOptimizerProvider>
                  <PersonalizationEngineProvider>
                    <SmartRecommendationEngineProvider>
                      <Slot />
                      <OfflineIndicator />
                      <StatusBar style="auto" />
                    </SmartRecommendationEngineProvider>
                  </PersonalizationEngineProvider>
                </NetworkOptimizerProvider>
              </IntelligentCacheProvider>
            </DataProvider>
          </AuthProvider>
        </ThemeProvider>
      </SafeAreaProvider>
    </GestureHandlerRootView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});
