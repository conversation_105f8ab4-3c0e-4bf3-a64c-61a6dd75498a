import React, { useState } from 'react';
import { View, StyleSheet, StatusBar, Platform, Animated } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { useTheme, useI18n } from '../../hooks';
import { RTLView, RTLText, RTLIcon, RTLTouchableOpacity } from '../RTL';
import { SPACING, FONT_SIZES, FONT_WEIGHTS, GRADIENTS, SHADOWS } from '../../constants/theme';

export interface RTLNavigationHeaderProps {
  title?: string;
  subtitle?: string;
  showBackButton?: boolean;
  onBackPress?: () => void;
  rightComponent?: React.ReactNode;
  leftComponent?: React.ReactNode;
  backgroundColor?: string;
  useGradient?: boolean;
  gradientColors?: readonly [string, string, ...string[]];
  showShadow?: boolean;
  statusBarStyle?: 'light-content' | 'dark-content' | 'default';
  transparent?: boolean;
  centerTitle?: boolean;
  showHamburgerMenu?: boolean;
  onHamburgerPress?: () => void;
}

export const RTLNavigationHeader: React.FC<RTLNavigationHeaderProps> = ({
  title,
  subtitle,
  showBackButton = false,
  onBackPress,
  rightComponent,
  leftComponent,
  backgroundColor,
  useGradient = true,
  gradientColors,
  showShadow = true,
  statusBarStyle = 'light-content',
  transparent = false,
  centerTitle = true,
  showHamburgerMenu = false,
  onHamburgerPress,
}) => {
  const { colors, isDark } = useTheme();
  const { isRTL, t } = useI18n();
  const insets = useSafeAreaInsets();

  // Animation for hamburger menu
  const [hamburgerPressed, setHamburgerPressed] = useState(false);
  const hamburgerScale = new Animated.Value(1);

  // Determine gradient colors based on theme
  const defaultGradientColors = gradientColors || (isDark ? GRADIENTS.midnight : GRADIENTS.primary);
  
  // Determine background color
  const headerBackgroundColor = transparent
    ? 'transparent'
    : backgroundColor || colors.surface;

  // Hamburger menu animation handlers
  const handleHamburgerPressIn = () => {
    setHamburgerPressed(true);
    Animated.spring(hamburgerScale, {
      toValue: 0.9,
      useNativeDriver: true,
      tension: 300,
      friction: 10,
    }).start();
  };

  const handleHamburgerPressOut = () => {
    setHamburgerPressed(false);
    Animated.spring(hamburgerScale, {
      toValue: 1,
      useNativeDriver: true,
      tension: 300,
      friction: 10,
    }).start();
  };

  const handleHamburgerPress = () => {
    if (onHamburgerPress) {
      onHamburgerPress();
    }
  };

  // Render hamburger menu
  const renderHamburgerMenu = () => {
    if (!showHamburgerMenu) return null;

    return (
      <Animated.View style={{ transform: [{ scale: hamburgerScale }] }}>
        <RTLTouchableOpacity
          style={[
            styles.hamburgerButton,
            hamburgerPressed && styles.hamburgerButtonPressed,
            { backgroundColor: hamburgerPressed ? 'rgba(255,255,255,0.1)' : 'transparent' }
          ]}
          onPress={handleHamburgerPress}
          onPressIn={handleHamburgerPressIn}
          onPressOut={handleHamburgerPressOut}
          accessibilityRole="button"
          accessibilityLabel={t('nav.menu')}
          accessibilityHint={t('nav.openMenu')}
          activeOpacity={0.7}
        >
          <RTLView style={styles.hamburgerIcon}>
            <RTLView style={[styles.hamburgerLine, { backgroundColor: useGradient ? colors.textOnPrimary : colors.textPrimary }]} />
            <RTLView style={[styles.hamburgerLine, { backgroundColor: useGradient ? colors.textOnPrimary : colors.textPrimary }]} />
            <RTLView style={[styles.hamburgerLine, { backgroundColor: useGradient ? colors.textOnPrimary : colors.textPrimary }]} />
          </RTLView>
        </RTLTouchableOpacity>
      </Animated.View>
    );
  };

  // Status bar configuration
  React.useEffect(() => {
    if (Platform.OS === 'ios') {
      StatusBar.setBarStyle(statusBarStyle, true);
    }
  }, [statusBarStyle]);

  const renderContent = () => (
    <RTLView style={[
      styles.container,
      { 
        paddingTop: insets.top,
        minHeight: 60 + insets.top,
      },
      showShadow && SHADOWS.small,
    ]}>
      <RTLView style={styles.content}>
        {/* Left side (Hamburger menu, Back button or custom component) */}
        <RTLView style={styles.leftSection}>
          {/* Hamburger menu positioned based on RTL */}
          {showHamburgerMenu && !isRTL && renderHamburgerMenu()}

          {showBackButton && (
            <RTLTouchableOpacity
              style={styles.backButton}
              onPress={onBackPress}
              accessibilityLabel={t('nav.back')}
              accessibilityRole="button"
            >
              <RTLIcon
                name={isRTL ? 'chevron-forward' : 'chevron-back'}
                size={24}
                color={useGradient ? colors.textOnPrimary : colors.textPrimary}
              />
            </RTLTouchableOpacity>
          )}
          {leftComponent && (
            <RTLView style={styles.customComponent}>
              {leftComponent}
            </RTLView>
          )}
        </RTLView>

        {/* Center section (Title and subtitle) */}
        <RTLView style={[
          styles.centerSection,
          centerTitle && styles.centerSectionCentered,
        ]}>
          {title && (
            <RTLText
              style={[
                styles.title,
                {
                  color: useGradient ? colors.textOnPrimary : colors.textPrimary,
                  textAlign: centerTitle ? 'center' : (isRTL ? 'right' : 'left'),
                }
              ]}
              numberOfLines={1}
              ellipsizeMode="tail"
            >
              {title}
            </RTLText>
          )}
          {subtitle && (
            <RTLText
              style={[
                styles.subtitle,
                {
                  color: useGradient ? colors.textOnPrimary : colors.textSecondary,
                  textAlign: centerTitle ? 'center' : (isRTL ? 'right' : 'left'),
                }
              ]}
              numberOfLines={1}
              ellipsizeMode="tail"
            >
              {subtitle}
            </RTLText>
          )}
        </RTLView>

        {/* Right side (Hamburger menu for RTL or custom component) */}
        <RTLView style={styles.rightSection}>
          {/* Hamburger menu positioned based on RTL */}
          {showHamburgerMenu && isRTL && renderHamburgerMenu()}

          {rightComponent && (
            <RTLView style={styles.customComponent}>
              {rightComponent}
            </RTLView>
          )}
        </RTLView>
      </RTLView>
    </RTLView>
  );

  if (transparent) {
    return (
      <View style={[styles.transparentContainer, { paddingTop: insets.top }]}>
        {renderContent()}
      </View>
    );
  }

  if (useGradient) {
    return (
      <LinearGradient
        colors={defaultGradientColors}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.gradientContainer}
      >
        {renderContent()}
      </LinearGradient>
    );
  }

  return (
    <View style={[styles.solidContainer, { backgroundColor: headerBackgroundColor }]}>
      {renderContent()}
    </View>
  );
};

const styles = StyleSheet.create({
  transparentContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 1000,
  },
  gradientContainer: {
    width: '100%',
  },
  solidContainer: {
    width: '100%',
  },
  container: {
    width: '100%',
    justifyContent: 'flex-end',
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: SPACING.md,
    paddingBottom: SPACING.sm,
    minHeight: 44, // Standard navigation bar height
  },
  leftSection: {
    flexDirection: 'row',
    alignItems: 'center',
    minWidth: 60,
    justifyContent: 'flex-start',
  },
  centerSection: {
    flex: 1,
    justifyContent: 'center',
    paddingHorizontal: SPACING.sm,
  },
  centerSectionCentered: {
    alignItems: 'center',
  },
  rightSection: {
    flexDirection: 'row',
    alignItems: 'center',
    minWidth: 60,
    justifyContent: 'flex-end',
  },
  backButton: {
    padding: SPACING.xs,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  customComponent: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  title: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.semibold,
    lineHeight: FONT_SIZES.lg * 1.2,
  },
  subtitle: {
    fontSize: FONT_SIZES.sm,
    fontWeight: FONT_WEIGHTS.regular,
    lineHeight: FONT_SIZES.sm * 1.2,
    marginTop: 2,
  },
  hamburgerButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
    padding: SPACING.xs,
    marginRight: SPACING.xs,
  },
  hamburgerButtonPressed: {
    transform: [{ scale: 0.95 }],
  },
  hamburgerIcon: {
    width: 24,
    height: 18,
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  hamburgerLine: {
    width: 24,
    height: 3,
    borderRadius: 1.5,
    marginVertical: 1,
  },
});
