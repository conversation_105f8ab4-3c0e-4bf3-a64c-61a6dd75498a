#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Configuration
const config = {
  srcDir: path.join(__dirname, '../src'),
  appDir: path.join(__dirname, '../app'),
  componentsDir: path.join(__dirname, '../components'),
  outputFile: path.join(__dirname, '../AUTO_RTL_CONVERSION_REPORT.md'),
  fileExtensions: ['.tsx', '.ts', '.jsx', '.js'],
  
  // Component mappings
  componentMappings: {
    'View': 'RTLView',
    'Text': 'RTLText',
    'SafeAreaView': 'RTLSafeAreaView',
    'ScrollView': 'RTLScrollView',
    'FlatList': 'RTLFlatList',
    'SectionList': 'RTLSectionList',
    'TextInput': 'RTLInput',
    'TouchableOpacity': 'RTLTouchableOpacity',
    'TouchableHighlight': 'RTLTouchableOpacity',
    'TouchableWithoutFeedback': 'RTLTouchableOpacity',
    'Pressable': 'RTLTouchableOpacity'
  },
  
  iconMappings: {
    'Ionicons': 'RTLIcon',
    'MaterialIcons': 'RTLIcon',
    'FontAwesome': 'RTLIcon',
    'AntDesign': 'RTLIcon',
    'Entypo': 'RTLIcon',
    'EvilIcons': 'RTLIcon',
    'Feather': 'RTLIcon',
    'Foundation': 'RTLIcon',
    'MaterialCommunityIcons': 'RTLIcon',
    'Octicons': 'RTLIcon',
    'SimpleLineIcons': 'RTLIcon',
    'Zocial': 'RTLIcon'
  }
};

class AutoRTLConverter {
  constructor() {
    this.results = {
      convertedFiles: [],
      errors: [],
      totalFilesProcessed: 0,
      totalReplacements: 0
    };
  }

  convertFile(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      let newContent = content;
      let replacements = 0;
      const fileReplacements = [];

      // Check if file already imports RTL components
      const hasRTLImports = /from\s+['"][^'"]*RTL['"]/.test(content);
      
      // Step 1: Update imports
      const importUpdates = this.updateImports(newContent);
      newContent = importUpdates.content;
      replacements += importUpdates.replacements;
      fileReplacements.push(...importUpdates.details);

      // Step 2: Replace component usage
      const componentUpdates = this.replaceComponents(newContent);
      newContent = componentUpdates.content;
      replacements += componentUpdates.replacements;
      fileReplacements.push(...componentUpdates.details);

      // Step 3: Replace icon usage
      const iconUpdates = this.replaceIcons(newContent);
      newContent = iconUpdates.content;
      replacements += iconUpdates.replacements;
      fileReplacements.push(...iconUpdates.details);

      // Only write if changes were made
      if (replacements > 0) {
        fs.writeFileSync(filePath, newContent);
        
        this.results.convertedFiles.push({
          path: path.relative(process.cwd(), filePath),
          replacements,
          details: fileReplacements
        });
        
        this.results.totalReplacements += replacements;
        console.log(`✅ Converted: ${path.relative(process.cwd(), filePath)} (${replacements} changes)`);
      }

      this.results.totalFilesProcessed++;
      
    } catch (error) {
      this.results.errors.push({
        file: path.relative(process.cwd(), filePath),
        error: error.message
      });
      console.error(`❌ Error converting ${filePath}:`, error.message);
    }
  }

  updateImports(content) {
    let newContent = content;
    let replacements = 0;
    const details = [];

    // Check for React Native imports
    const rnImportRegex = /import\s*\{([^}]+)\}\s*from\s*['"]react-native['"];?/g;
    const iconImportRegex = /import\s*\{([^}]+)\}\s*from\s*['"]@expo\/vector-icons['"];?/g;

    // Handle React Native imports
    newContent = newContent.replace(rnImportRegex, (match, imports) => {
      const importList = imports.split(',').map(imp => imp.trim());
      const basicComponents = [];
      const otherImports = [];
      
      importList.forEach(imp => {
        if (config.componentMappings[imp]) {
          basicComponents.push(imp);
        } else {
          otherImports.push(imp);
        }
      });

      if (basicComponents.length === 0) {
        return match; // No changes needed
      }

      let result = '';
      
      // Keep other React Native imports
      if (otherImports.length > 0) {
        result += `import { ${otherImports.join(', ')} } from 'react-native';\n`;
      }
      
      // Add RTL import
      const rtlComponents = basicComponents.map(comp => config.componentMappings[comp]);
      result += `import { ${rtlComponents.join(', ')} } from '../components/RTL';`;
      
      replacements++;
      details.push(`Updated imports: ${basicComponents.join(', ')} → ${rtlComponents.join(', ')}`);
      
      return result;
    });

    // Handle icon imports
    newContent = newContent.replace(iconImportRegex, (match, imports) => {
      const importList = imports.split(',').map(imp => imp.trim());
      const iconComponents = [];
      
      importList.forEach(imp => {
        if (config.iconMappings[imp]) {
          iconComponents.push(imp);
        }
      });

      if (iconComponents.length === 0) {
        return match; // No changes needed
      }

      // Replace with RTLIcon import
      const result = `import { RTLIcon } from '../components/RTL';`;
      
      replacements++;
      details.push(`Updated icon imports: ${iconComponents.join(', ')} → RTLIcon`);
      
      return result;
    });

    return { content: newContent, replacements, details };
  }

  replaceComponents(content) {
    let newContent = content;
    let replacements = 0;
    const details = [];

    // Replace component usage in JSX
    Object.entries(config.componentMappings).forEach(([oldComp, newComp]) => {
      const regex = new RegExp(`<${oldComp}\\b`, 'g');
      const matches = newContent.match(regex);
      
      if (matches) {
        newContent = newContent.replace(regex, `<${newComp}`);
        replacements += matches.length;
        details.push(`${oldComp} → ${newComp} (${matches.length} occurrences)`);
      }
    });

    return { content: newContent, replacements, details };
  }

  replaceIcons(content) {
    let newContent = content;
    let replacements = 0;
    const details = [];

    // Replace icon usage in JSX
    Object.keys(config.iconMappings).forEach(iconComp => {
      const regex = new RegExp(`<${iconComp}\\b`, 'g');
      const matches = newContent.match(regex);
      
      if (matches) {
        newContent = newContent.replace(regex, '<RTLIcon');
        replacements += matches.length;
        details.push(`${iconComp} → RTLIcon (${matches.length} occurrences)`);
      }
    });

    return { content: newContent, replacements, details };
  }

  processDirectory(dirPath) {
    if (!fs.existsSync(dirPath)) {
      console.log(`Directory not found: ${dirPath}`);
      return;
    }

    const items = fs.readdirSync(dirPath);
    
    for (const item of items) {
      const fullPath = path.join(dirPath, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        // Skip certain directories
        if (!/node_modules|\.git|\.expo|dist|build/.test(item)) {
          this.processDirectory(fullPath);
        }
      } else if (stat.isFile() && config.fileExtensions.some(ext => fullPath.endsWith(ext))) {
        // Skip RTL component files themselves
        if (!/RTL.*\.tsx?$/.test(fullPath)) {
          this.convertFile(fullPath);
        }
      }
    }
  }

  generateReport() {
    const timestamp = new Date().toLocaleString();
    
    let report = `# Auto RTL Conversion Report\n\n`;
    report += `**Generated:** ${timestamp}\n`;
    report += `**Files Processed:** ${this.results.totalFilesProcessed}\n`;
    report += `**Files Converted:** ${this.results.convertedFiles.length}\n`;
    report += `**Total Replacements:** ${this.results.totalReplacements}\n\n`;
    
    if (this.results.convertedFiles.length > 0) {
      report += `## ✅ Successfully Converted Files\n\n`;
      
      this.results.convertedFiles.forEach(file => {
        report += `### ${file.path}\n`;
        report += `**Changes:** ${file.replacements}\n\n`;
        
        if (file.details.length > 0) {
          report += `**Details:**\n`;
          file.details.forEach(detail => {
            report += `- ${detail}\n`;
          });
          report += `\n`;
        }
      });
    }
    
    if (this.results.errors.length > 0) {
      report += `## ❌ Errors\n\n`;
      
      this.results.errors.forEach(error => {
        report += `- **${error.file}:** ${error.error}\n`;
      });
      report += `\n`;
    }
    
    return report;
  }

  run() {
    console.log('🔄 Starting automatic RTL conversion...');
    
    // Process directories
    [config.srcDir, config.appDir, config.componentsDir].forEach(dir => {
      if (fs.existsSync(dir)) {
        console.log(`Processing: ${dir}`);
        this.processDirectory(dir);
      }
    });
    
    // Generate and save report
    const report = this.generateReport();
    fs.writeFileSync(config.outputFile, report);
    
    console.log(`\n📄 Report saved to: ${config.outputFile}`);
    console.log(`📊 Files processed: ${this.results.totalFilesProcessed}`);
    console.log(`✅ Files converted: ${this.results.convertedFiles.length}`);
    console.log(`🔧 Total replacements: ${this.results.totalReplacements}`);
    
    if (this.results.errors.length > 0) {
      console.log(`❌ Errors: ${this.results.errors.length}`);
    }
    
    return this.results.convertedFiles.length;
  }
}

// Run the converter
if (require.main === module) {
  const converter = new AutoRTLConverter();
  const convertedCount = converter.run();
  process.exit(0);
}

module.exports = AutoRTLConverter;
