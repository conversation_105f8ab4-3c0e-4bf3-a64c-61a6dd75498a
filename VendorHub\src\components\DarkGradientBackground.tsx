import React from 'react';
import { StyleSheet, ViewStyle, Dimensions } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { RTLView } from './RTL';
import { PREMIUM_GRADIENTS } from '../constants/advancedColors';

const { width, height } = Dimensions.get('window');

export type DarkGradientVariant = 
  | 'elegant'      // أسود → كحلي أسود → كحلي داكن
  | 'mystic'       // كحلي عميق → كحلي أسود → كحلي متوسط
  | 'royal'        // أسود → أزرق عميق → كحلي أسود
  | 'ocean'        // كحلي أسود → أزرق داكن → أسود
  | 'storm'        // رمادي داكن → كحلي داكن → كحلي أسود
  | 'void'         // أسود → رمادي عميق → رمادي داكن
  | 'abyss'        // كحلي عميق → كحلي أسود → كحلي داكن → كحلي متوسط
  | 'nebula';      // أسود → كحلي أسود → أزرق عميق → كحلي داكن

export interface DarkGradientBackgroundProps {
  variant?: DarkGradientVariant;
  children?: React.ReactNode;
  style?: ViewStyle;
  direction?: 'vertical' | 'horizontal' | 'diagonal' | 'radial';
  animated?: boolean;
}

export const DarkGradientBackground: React.FC<DarkGradientBackgroundProps> = ({
  variant = 'elegant',
  children,
  style,
  direction = 'vertical',
  animated = false,
}) => {
  const getGradientColors = (): readonly string[] => {
    switch (variant) {
      case 'elegant':
        return PREMIUM_GRADIENTS.darkElegant;
      case 'mystic':
        return PREMIUM_GRADIENTS.darkMystic;
      case 'royal':
        return PREMIUM_GRADIENTS.darkRoyal;
      case 'ocean':
        return PREMIUM_GRADIENTS.darkOcean;
      case 'storm':
        return PREMIUM_GRADIENTS.darkStorm;
      case 'void':
        return PREMIUM_GRADIENTS.darkVoid;
      case 'abyss':
        return PREMIUM_GRADIENTS.darkAbyss;
      case 'nebula':
        return PREMIUM_GRADIENTS.darkNebula;
      default:
        return PREMIUM_GRADIENTS.darkElegant;
    }
  };

  const getGradientDirection = () => {
    switch (direction) {
      case 'horizontal':
        return { start: { x: 0, y: 0 }, end: { x: 1, y: 0 } };
      case 'diagonal':
        return { start: { x: 0, y: 0 }, end: { x: 1, y: 1 } };
      case 'radial':
        return { start: { x: 0.5, y: 0.5 }, end: { x: 1, y: 1 } };
      default: // vertical
        return { start: { x: 0, y: 0 }, end: { x: 0, y: 1 } };
    }
  };

  const gradientDirection = getGradientDirection();

  return (
    <LinearGradient
      colors={getGradientColors()}
      style={[styles.container, style]}
      start={gradientDirection.start}
      end={gradientDirection.end}
    >
      {children}
    </LinearGradient>
  );
};

// مكون خاص للخلفيات الزجاجية الداكنة
export interface DarkGlassBackgroundProps {
  variant?: 'elegant' | 'mystic' | 'royal';
  children?: React.ReactNode;
  style?: ViewStyle;
  blur?: boolean;
}

export const DarkGlassBackground: React.FC<DarkGlassBackgroundProps> = ({
  variant = 'elegant',
  children,
  style,
  blur = true,
}) => {
  const getGlassColors = (): readonly string[] => {
    switch (variant) {
      case 'elegant':
        return PREMIUM_GRADIENTS.glassDarkElegant;
      case 'mystic':
        return PREMIUM_GRADIENTS.glassDarkMystic;
      case 'royal':
        return PREMIUM_GRADIENTS.glassDarkRoyal;
      default:
        return PREMIUM_GRADIENTS.glassDarkElegant;
    }
  };

  return (
    <LinearGradient
      colors={getGlassColors()}
      style={[styles.glassContainer, style]}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
    >
      {children}
    </LinearGradient>
  );
};

// مكون للخلفيات المتحركة
export interface AnimatedDarkBackgroundProps {
  children?: React.ReactNode;
  style?: ViewStyle;
}

export const AnimatedDarkBackground: React.FC<AnimatedDarkBackgroundProps> = ({
  children,
  style,
}) => {
  // يمكن إضافة الرسوم المتحركة هنا لاحقاً
  return (
    <DarkGradientBackground
      variant="nebula"
      direction="diagonal"
      style={[styles.animatedContainer, style]}
    >
      {children}
    </DarkGradientBackground>
  );
};

// مكون للخلفيات متعددة الطبقات
export interface LayeredDarkBackgroundProps {
  children?: React.ReactNode;
  style?: ViewStyle;
}

export const LayeredDarkBackground: React.FC<LayeredDarkBackgroundProps> = ({
  children,
  style,
}) => {
  return (
    <RTLView style={[styles.layeredContainer, style]}>
      {/* الطبقة الأساسية */}
      <LinearGradient
        colors={PREMIUM_GRADIENTS.darkAbyss}
        style={styles.baseLayer}
        start={{ x: 0, y: 0 }}
        end={{ x: 0, y: 1 }}
      />
      
      {/* طبقة التأثير */}
      <LinearGradient
        colors={PREMIUM_GRADIENTS.glassDarkRoyal}
        style={styles.effectLayer}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      />
      
      {/* المحتوى */}
      <RTLView style={styles.contentLayer}>
        {children}
      </RTLView>
    </RTLView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    width: '100%',
    minHeight: height,
  },
  glassContainer: {
    flex: 1,
    width: '100%',
    borderRadius: 12,
    overflow: 'hidden',
  },
  animatedContainer: {
    flex: 1,
    width: '100%',
    minHeight: height,
  },
  layeredContainer: {
    flex: 1,
    width: '100%',
    minHeight: height,
    position: 'relative',
  },
  baseLayer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    width: '100%',
    height: '100%',
  },
  effectLayer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    width: '100%',
    height: '100%',
    opacity: 0.7,
  },
  contentLayer: {
    flex: 1,
    zIndex: 10,
  },
});

export default DarkGradientBackground;
