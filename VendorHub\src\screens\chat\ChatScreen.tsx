import React, { useState, useEffect, useRef } from 'react';
import {
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  Alert,
  Image } from 'react-native';
import { useThemedStyles, useAuth, useI18n } from '../../hooks';
import { <PERSON>, Button } from '../../components';
import { RTLView, RTLText, RTLIcon, RTLSafeAreaView, RTLInput, RTLFlatList, RTLTouchableOpacity } from '../../components/RTL';
import ChatService, { ChatMessage, ChatConversation } from '../../services/ChatService';
import {
  SPACING,
  FONT_SIZES,
  FONT_WEIGHTS,
  BORDER_RADIUS } from '../../constants/theme';
import { formatCurrency } from '../../utils';
import type ThemeColors  from '../../contexts/ThemeContext';

interface ChatScreenProps {
  navigation: any;
  route: {
    params: {
      chatId?: string;
      otherUserId?: string;
      otherUserName?: string;
      otherUserAvatar?: string;
    };
  };
}

export const ChatScreen: React.FC<ChatScreenProps> = ({ navigation, route }) => {
  const styles = useThemedStyles(createStyles);
  const { user } = useAuth();
  const { t } = useI18n();
  const flatListRef = useRef<any>(null);
  
  const [conversation, setConversation] = useState<ChatConversation | null>(null);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputText, setInputText] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [typingUsers, setTypingUsers] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  const { chatId, otherUserId, otherUserName, otherUserAvatar } = route.params;

  useEffect(() => {
    initializeChat();
    setupEventListeners();

    return () => {
      ChatService.removeAllListeners();
    };
  }, []);

  useEffect(() => {
    if (conversation && user) {
      // Mark messages as read when screen is focused
      ChatService.markMessagesAsRead(conversation.id, user.id);
    }
  }, [conversation, user]);

  const initializeChat = async () => {
    if (!user) return;

    try {
      let conv: ChatConversation;

      if (chatId) {
        // Load existing conversation
        const existingConv = ChatService.getConversation(chatId);
        if (!existingConv) {
          Alert.alert('Error', 'Conversation not found');
          navigation.goBack();
          return;
        }
        conv = existingConv;
      } else if (otherUserId && otherUserName) {
        // Create or get conversation with specific user
        conv = await ChatService.getOrCreateConversation(
          otherUserId,
          otherUserName,
          otherUserAvatar
        );
      } else {
        Alert.alert('Error', 'Invalid chat parameters');
        navigation.goBack();
        return;
      }

      setConversation(conv);
      setMessages(ChatService.getMessages(conv.id));
      setIsLoading(false);

      // Set navigation title
      const otherParticipant = conv.participants.find(id => id !== user.id);
      const title = otherParticipant ? conv.participantNames[otherParticipant] : 'Chat';
      navigation.setOptions({ title });

    } catch (error) {
      console.error('Error initializing chat:', error);
      Alert.alert(t('common.error'), t('chat.failedToLoadChat'));
      navigation.goBack();
    }
  };

  const setupEventListeners = () => {
    ChatService.on('messageReceived', handleMessageReceived);
    ChatService.on('typingStarted', handleTypingStarted);
    ChatService.on('typingStopped', handleTypingStopped);
    ChatService.on('messageUpdated', handleMessageUpdated);
    ChatService.on('messageDeleted', handleMessageDeleted);
  };

  const handleMessageReceived = (message: ChatMessage) => {
    if (conversation && message.chatId === conversation.id) {
      setMessages(prev => [...prev, message]);
      scrollToBottom();
    }
  };

  const handleTypingStarted = (indicator: any) => {
    if (conversation && indicator.chatId === conversation.id && indicator.userId !== user?.id) {
      setTypingUsers(prev => {
        if (!prev.includes(indicator.userName)) {
          return [...prev, indicator.userName];
        }
        return prev;
      });
    }
  };

  const handleTypingStopped = (data: any) => {
    if (conversation && data.chatId === conversation.id) {
      setTypingUsers(prev => prev.filter(name => name !== data.userName));
    }
  };

  const handleMessageUpdated = (message: ChatMessage) => {
    if (conversation && message.chatId === conversation.id) {
      setMessages(prev => prev.map(m => m.id === message.id ? message : m));
    }
  };

  const handleMessageDeleted = (data: any) => {
    if (conversation && data.chatId === conversation.id) {
      setMessages(prev => prev.filter(m => m.id !== data.messageId));
    }
  };

  const sendMessage = async () => {
    if (!inputText.trim() || !conversation || !user) return;

    const messageText = inputText.trim();
    setInputText('');
    stopTyping();

    try {
      await ChatService.sendMessage(conversation.id, messageText);
      scrollToBottom();
    } catch (error) {
      console.error('Error sending message:', error);
      Alert.alert(t('common.error'), t('chat.failedToSendMessage'));
      setInputText(messageText); // Restore input text
    }
  };

  const startTyping = () => {
    if (!isTyping && conversation) {
      setIsTyping(true);
      ChatService.startTyping(conversation.id);
    }
  };

  const stopTyping = () => {
    if (isTyping && conversation) {
      setIsTyping(false);
      ChatService.stopTyping(conversation.id);
    }
  };

  const scrollToBottom = () => {
    setTimeout(() => {
      flatListRef.current?.scrollToEnd({ animated: true });
    }, 100);
  };

  const handleLongPress = (message: ChatMessage) => {
    if (message.senderId === user?.id) {
      Alert.alert(
        'Message Options',
        'What would you like to do?',
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Edit',
            onPress: () => editMessage(message),
          },
          {
            text: 'Delete',
            style: 'destructive',
            onPress: () => deleteMessage(message),
          },
        ]
      );
    }
  };

  const editMessage = (message: ChatMessage) => {
    Alert.prompt(
      'Edit Message',
      'Enter new message:',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Save',
          onPress: (newText) => {
            if (newText && newText.trim()) {
              ChatService.editMessage(message.id, newText.trim());
            }
          },
        },
      ],
      'plain-text',
      message.content
    );
  };

  const deleteMessage = (message: ChatMessage) => {
    Alert.alert(
      'Delete Message',
      'Are you sure you want to delete this message?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => ChatService.deleteMessage(message.id),
        },
      ]
    );
  };

  const renderMessage = ({ item, index }: { item: ChatMessage; index: number }) => {
    const isOwnMessage = item.senderId === user?.id;
    const showAvatar = !isOwnMessage && (index === 0 || messages[index - 1].senderId !== item.senderId);
    const showTimestamp = index === 0 || 
      new Date(item.timestamp).getTime() - new Date(messages[index - 1].timestamp).getTime() > 300000; // 5 minutes

    return (
      <RTLView style={styles.messageContainer}>
        {showTimestamp && (
          <RTLText style={styles.timestamp}>
            {new Date(item.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
          </RTLText>
        )}

        <RTLView style={[styles.messageRow, isOwnMessage && styles.ownMessageRow].filter(Boolean) as any}>
          {showAvatar && !isOwnMessage && (
            <RTLView style={styles.avatarContainer}>
              {item.senderAvatar ? (
                <Image source={{ uri: item.senderAvatar }} style={styles.avatar} />
              ) : (
                <RTLView style={styles.avatarPlaceholder}>
                  <RTLIcon name="person" size={16} color="#666" />
                </RTLView>
              )}
            </RTLView>
          )}

          <RTLTouchableOpacity
            style={[
              styles.messageBubble,
              isOwnMessage ? styles.ownMessageBubble : styles.otherMessageBubble,
            ]}
            onLongPress={() => handleLongPress(item)}
          >
            {!isOwnMessage && showAvatar && (
              <RTLText style={styles.senderName}>{item.senderName}</RTLText>
            )}

            <RTLText style={[
              styles.messageText,
              isOwnMessage ? styles.ownMessageText : styles.otherMessageText,
            ]}>
              {item.content}
            </RTLText>

            <RTLView style={styles.messageFooter}>
              {item.edited && (
                <RTLText style={styles.editedLabel}>{t('chat.edited')}</RTLText>
              )}
              <RTLText style={[
                styles.messageTime,
                isOwnMessage ? styles.ownMessageTime : styles.otherMessageTime,
              ]}>
                {new Date(item.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
              </RTLText>
              {isOwnMessage && (
                <RTLIcon
                  name={item.read ? "checkmark-done" : "checkmark"}
                  size={12}
                  color={item.read ? "#4CAF50" : "#999"}
                />
              )}
            </RTLView>
          </RTLTouchableOpacity>
        </RTLView>
      </RTLView>
    );
  };

  const renderTypingIndicator = () => {
    if (typingUsers.length === 0) return null;

    return (
      <RTLView style={styles.typingContainer}>
        <RTLText style={styles.typingText}>
          {typingUsers.join(', ')} {typingUsers.length === 1 ? t('chat.isTyping') : t('chat.areTyping')}
        </RTLText>
      </RTLView>
    );
  };

  const renderInputArea = () => (
    <RTLView style={styles.inputContainer}>
      <RTLView style={styles.inputRow}>
        <RTLTouchableOpacity style={styles.attachButton}>
          <RTLIcon name="add" size={24} color="#667eea" />
        </RTLTouchableOpacity>

        <RTLInput
          style={styles.textInput}
          value={inputText}
          onChangeText={(text) => {
            setInputText(text);
            if (text.length > 0) {
              startTyping();
            } else {
              stopTyping();
            }
          }}
          placeholder={t('chat.typeMessage')}
          placeholderTextColor="#999"
          multiline
          maxLength={1000}
          onBlur={stopTyping}
        />

        <RTLTouchableOpacity
          style={[styles.sendButton, inputText.trim() && styles.sendButtonActive]}
          onPress={sendMessage}
          disabled={!inputText.trim()}
        >
          <RTLIcon
            name="send"
            size={20}
            color={inputText.trim() ? "#FFFFFF" : "#999"}
          />
        </RTLTouchableOpacity>
      </RTLView>
    </RTLView>
  );

  if (isLoading) {
    return (
      <RTLSafeAreaView style={styles.container}>
        <RTLView style={styles.loadingContainer}>
          <RTLText style={styles.loadingText}>{t('chat.loadingChat')}</RTLText>
        </RTLView>
      </RTLSafeAreaView>
    );
  }

  return (
    <RTLSafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        style={styles.keyboardAvoidingView}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 0}
      >
        <RTLFlatList

          data={messages}
          renderItem={renderMessage}
          keyExtractor={(item) => item.id}
          style={styles.messagesList}
          contentContainerStyle={styles.messagesContent}
          onContentSizeChange={scrollToBottom}
          showsVerticalScrollIndicator={false}
        />

        {renderTypingIndicator()}
        {renderInputArea()}
      </KeyboardAvoidingView>
    </RTLSafeAreaView>
  );
};

const createStyles = (colors: ThemeColors) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: FONT_SIZES.md,
    color: colors.textSecondary,
  },
  messagesList: {
    flex: 1,
  },
  messagesContent: {
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.lg,
  },
  messageContainer: {
    marginBottom: SPACING.md,
  },
  timestamp: {
    fontSize: FONT_SIZES.xs,
    color: colors.textSecondary,
    textAlign: 'center',
    marginBottom: SPACING.sm,
  },
  messageRow: {
    flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
    alignItems: 'flex-end',
  },
  ownMessageRow: {
    justifyContent: 'flex-end',
  },
  avatarContainer: {
    marginRight: SPACING.sm,
    marginBottom: SPACING.xs,
  },
  avatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
  },
  avatarPlaceholder: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: colors.surface,
    justifyContent: 'center',
    alignItems: 'center',
  },
  messageBubble: {
    maxWidth: '75%',
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    borderRadius: BORDER_RADIUS.lg,
    marginBottom: SPACING.xs,
  },
  ownMessageBubble: {
    backgroundColor: '#667eea',
    borderBottomRightRadius: BORDER_RADIUS.sm,
  },
  otherMessageBubble: {
    backgroundColor: colors.surface,
    borderBottomLeftRadius: BORDER_RADIUS.sm,
  },
  senderName: {
    fontSize: FONT_SIZES.xs,
    fontWeight: FONT_WEIGHTS.semiBold,
    color: colors.text,
    marginBottom: SPACING.xs,
  },
  messageText: {
    fontSize: FONT_SIZES.md,
    lineHeight: 20,
  },
  ownMessageText: {
    color: '#FFFFFF',
  },
  otherMessageText: {
    color: colors.text,
  },
  messageFooter: {
    flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
    alignItems: 'center',
    justifyContent: 'flex-end',
    marginTop: SPACING.xs,
    gap: SPACING.xs,
  },
  editedLabel: {
    fontSize: FONT_SIZES.xs,
    fontStyle: 'italic',
    color: 'rgba(255, 255, 255, 0.7)',
  },
  messageTime: {
    fontSize: FONT_SIZES.xs,
  },
  ownMessageTime: {
    color: 'rgba(255, 255, 255, 0.7)',
  },
  otherMessageTime: {
    color: colors.textSecondary,
  },
  typingContainer: {
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.sm,
  },
  typingText: {
    fontSize: FONT_SIZES.sm,
    fontStyle: 'italic',
    color: colors.textSecondary,
  },
  inputContainer: {
    backgroundColor: colors.surface,
    borderTopWidth: 1,
    borderTopColor: colors.border,
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
  },
  inputRow: {
    flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
    alignItems: 'flex-end',
    gap: SPACING.sm,
  },
  attachButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.background,
    justifyContent: 'center',
    alignItems: 'center',
  },
  textInput: {
    flex: 1,
    maxHeight: 100,
    minHeight: 40,
    backgroundColor: colors.background,
    borderRadius: BORDER_RADIUS.lg,
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    fontSize: FONT_SIZES.md,
    color: colors.text,
    textAlignVertical: 'center',
  },
  sendButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.surface,
    justifyContent: 'center',
    alignItems: 'center',
  },
  sendButtonActive: {
    backgroundColor: '#667eea',
  },
});
