import React, { useState, useEffect } from 'react';
import { StyleSheet, Modal, Dimensions, Animated, PanResponder } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { RTLView, RTLText, RTLTouchableOpacity, RTLIcon, RTLScrollView } from '../RTL';
import { useThemedStyles, useProducts, useI18n } from '../../hooks';
import { SPACING, FONT_SIZES, FONT_WEIGHTS, BORDER_RADIUS } from '../../constants/theme';
import { PREMIUM_GRADIENTS } from '../../constants/advancedColors';
import { SubtleGlow } from '../MoonlightEffects';
import type { ThemeColors } from '../../contexts/ThemeContext';
import type { Vendor, Product } from '../../contexts/DataContext';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');
const MODAL_HEIGHT = screenHeight * 0.7;

interface VendorPreviewModalProps {
  vendor: Vendor | null;
  visible: boolean;
  onClose: () => void;
  onVisitShop: (vendorId: string) => void;
}

export const VendorPreviewModal: React.FC<VendorPreviewModalProps> = ({
  vendor,
  visible,
  onClose,
  onVisitShop,
}) => {
  const styles = useThemedStyles(createStyles);
  const { getProductsByVendor } = useProducts();
  const { t } = useI18n();
  
  const [slideAnim] = useState(new Animated.Value(screenHeight));
  const [backdropOpacity] = useState(new Animated.Value(0));

  const vendorProducts = vendor ? getProductsByVendor(vendor.id) : [];
  const activeProducts = vendorProducts.filter(p => p.isActive);
  const featuredProducts = activeProducts.slice(0, 6);

  // Pan responder for swipe to close
  const panResponder = PanResponder.create({
    onMoveShouldSetPanResponder: (_, gestureState) => {
      return Math.abs(gestureState.dy) > 20;
    },
    onPanResponderMove: (_, gestureState) => {
      if (gestureState.dy > 0) {
        slideAnim.setValue(gestureState.dy);
      }
    },
    onPanResponderRelease: (_, gestureState) => {
      if (gestureState.dy > 100) {
        closeModal();
      } else {
        Animated.spring(slideAnim, {
          toValue: 0,
          useNativeDriver: true,
        }).start();
      }
    },
  });

  useEffect(() => {
    if (visible) {
      Animated.parallel([
        Animated.spring(slideAnim, {
          toValue: 0,
          useNativeDriver: true,
          tension: 100,
          friction: 8,
        }),
        Animated.timing(backdropOpacity, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      closeModal();
    }
  }, [visible]);

  const closeModal = () => {
    Animated.parallel([
      Animated.timing(slideAnim, {
        toValue: screenHeight,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(backdropOpacity, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start(() => {
      onClose();
    });
  };

  const handleVisitShop = () => {
    if (vendor) {
      closeModal();
      setTimeout(() => onVisitShop(vendor.id), 300);
    }
  };

  const renderProductPreview = (product: Product) => (
    <RTLView key={product.id} style={styles.productPreview}>
      <RTLView style={styles.productImage}>
        <RTLIcon name="cube-outline" size={20} color="#3B82F6" />
      </RTLView>
      <RTLView style={styles.productInfo}>
        <RTLText style={styles.productName} numberOfLines={1}>
          {product.name}
        </RTLText>
        <RTLText style={styles.productPrice}>
          {product.salePrice ? (
            <RTLView style={styles.priceContainer}>
              <RTLText style={styles.salePrice}>BHD {product.salePrice.toFixed(2)}</RTLText>
              <RTLText style={styles.originalPrice}>BHD {product.price.toFixed(2)}</RTLText>
            </RTLView>
          ) : (
            `BHD ${product.price.toFixed(2)}`
          )}
        </RTLText>
      </RTLView>
      {product.salePrice && (
        <RTLView style={styles.saleBadge}>
          <RTLText style={styles.saleBadgeText}>{t('vendor.onSale')}</RTLText>
        </RTLView>
      )}
    </RTLView>
  );

  if (!vendor) return null;

  return (
    <Modal
      visible={visible}
      transparent
      animationType="none"
      onRequestClose={closeModal}
    >
      <RTLView style={styles.modalContainer}>
        {/* Backdrop */}
        <Animated.View
          style={[
            styles.backdrop,
            { opacity: backdropOpacity }
          ]}
        >
          <RTLTouchableOpacity
            style={styles.backdropTouchable}
            onPress={closeModal}
            activeOpacity={1}
          />
        </Animated.View>

        {/* Modal Content */}
        <Animated.View
          style={[
            styles.modalContent,
            {
              transform: [{ translateY: slideAnim }]
            }
          ]}
          {...panResponder.panHandlers}
        >
          <SubtleGlow intensity={0.8}>
            <LinearGradient
              colors={PREMIUM_GRADIENTS.elegantDepth}
              style={styles.modalGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
            >
              {/* Handle */}
              <RTLView style={styles.handle} />

              {/* Header */}
              <RTLView style={styles.header}>
                <RTLView style={styles.vendorHeader}>
                  <RTLView style={styles.vendorLogo}>
                    <RTLIcon name="storefront" size={32} color="#3B82F6" />
                  </RTLView>
                  <RTLView style={styles.vendorInfo}>
                    <RTLText style={styles.vendorName}>{vendor.businessName}</RTLText>
                    <RTLText style={styles.vendorDescription} numberOfLines={2}>
                      {vendor.businessDescription || t('vendor.qualityProducts')}
                    </RTLText>
                  </RTLView>
                </RTLView>

                <RTLTouchableOpacity style={styles.closeButton} onPress={closeModal}>
                  <RTLIcon name="close" size={24} color="rgba(255, 255, 255, 0.8)" />
                </RTLTouchableOpacity>
              </RTLView>

              {/* Stats */}
              <RTLView style={styles.statsContainer}>
                <RTLView style={styles.statItem}>
                  <RTLIcon name="star" size={16} color="#FFD700" />
                  <RTLText style={styles.statText}>{(vendor.rating || 0).toFixed(1)}</RTLText>
                  <RTLText style={styles.statLabel}>{t('vendor.rating')}</RTLText>
                </RTLView>
                <RTLView style={styles.statItem}>
                  <RTLIcon name="cube-outline" size={16} color="#3B82F6" />
                  <RTLText style={styles.statText}>{activeProducts.length}</RTLText>
                  <RTLText style={styles.statLabel}>{t('vendor.products')}</RTLText>
                </RTLView>
                <RTLView style={styles.statItem}>
                  <RTLIcon name="time-outline" size={16} color="#10B981" />
                  <RTLText style={styles.statText}>{t('vendor.active')}</RTLText>
                  <RTLText style={styles.statLabel}>{t('common.status')}</RTLText>
                </RTLView>
              </RTLView>

              {/* Products Preview */}
              <RTLScrollView style={styles.content} showsVerticalScrollIndicator={false}>
                <RTLView style={styles.section}>
                  <RTLText style={styles.sectionTitle}>{t('vendor.featuredProducts')}</RTLText>
                  {featuredProducts.length > 0 ? (
                    <RTLView style={styles.productsGrid}>
                      {featuredProducts.map(renderProductPreview)}
                    </RTLView>
                  ) : (
                    <RTLView style={styles.emptyProducts}>
                      <RTLIcon name="cube-outline" size={32} color="rgba(255, 255, 255, 0.5)" />
                      <RTLText style={styles.emptyProductsText}>
                        {t('products.noProductsYet')}
                      </RTLText>
                    </RTLView>
                  )}
                </RTLView>

                {/* Contact Info */}
                {vendor.phone && (
                  <RTLView style={styles.section}>
                    <RTLText style={styles.sectionTitle}>{t('vendor.contactInfo')}</RTLText>
                    <RTLView style={styles.contactItem}>
                      <RTLIcon name="call" size={16} color="#3B82F6" />
                      <RTLText style={styles.contactText}>{vendor.phone}</RTLText>
                    </RTLView>
                  </RTLView>
                )}
              </RTLScrollView>

              {/* Action Buttons */}
              <RTLView style={styles.actions}>
                <RTLTouchableOpacity style={styles.visitButton} onPress={handleVisitShop}>
                  <LinearGradient
                    colors={['#3B82F6', '#1D4ED8']}
                    style={styles.visitButtonGradient}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 0 }}
                  >
                    <RTLText style={styles.visitButtonText}>{t('vendor.visitShop')}</RTLText>
                    <RTLIcon name="arrow-forward" size={16} color="#FFFFFF" />
                  </LinearGradient>
                </RTLTouchableOpacity>
              </RTLView>
            </LinearGradient>
          </SubtleGlow>
        </Animated.View>
      </RTLView>
    </Modal>
  );
};

const createStyles = (colors: ThemeColors) => StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  backdrop: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  backdropTouchable: {
    flex: 1,
  },
  modalContent: {
    height: MODAL_HEIGHT,
    borderTopLeftRadius: BORDER_RADIUS.xl,
    borderTopRightRadius: BORDER_RADIUS.xl,
    overflow: 'hidden',
  },
  modalGradient: {
    flex: 1,
    paddingTop: SPACING.md,
  },
  handle: {
    width: 40,
    height: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 2,
    alignSelf: 'center',
    marginBottom: SPACING.md,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    paddingHorizontal: SPACING.lg,
    marginBottom: SPACING.lg,
  },
  vendorHeader: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  vendorLogo: {
    width: 48,
    height: 48,
    borderRadius: BORDER_RADIUS.lg,
    backgroundColor: 'rgba(59, 130, 246, 0.1)',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: SPACING.md,
  },
  vendorInfo: {
    flex: 1,
  },
  vendorName: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.bold,
    color: '#FFFFFF',
    marginBottom: SPACING.xs,
  },
  vendorDescription: {
    fontSize: FONT_SIZES.sm,
    color: 'rgba(255, 255, 255, 0.8)',
    lineHeight: 18,
  },
  closeButton: {
    padding: SPACING.sm,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: SPACING.lg,
    marginBottom: SPACING.lg,
  },
  statItem: {
    alignItems: 'center',
  },
  statText: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.bold,
    color: '#FFFFFF',
    marginTop: SPACING.xs,
  },
  statLabel: {
    fontSize: FONT_SIZES.xs,
    color: 'rgba(255, 255, 255, 0.7)',
    marginTop: SPACING.xs,
  },
  content: {
    flex: 1,
    paddingHorizontal: SPACING.lg,
  },
  section: {
    marginBottom: SPACING.lg,
  },
  sectionTitle: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.semiBold,
    color: '#FFFFFF',
    marginBottom: SPACING.md,
  },
  productsGrid: {
    gap: SPACING.md,
  },
  productPreview: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: BORDER_RADIUS.md,
    padding: SPACING.md,
    borderWidth: 1,
    borderColor: 'rgba(59, 130, 246, 0.2)',
  },
  productImage: {
    width: 40,
    height: 40,
    borderRadius: BORDER_RADIUS.sm,
    backgroundColor: 'rgba(59, 130, 246, 0.1)',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: SPACING.md,
  },
  productInfo: {
    flex: 1,
  },
  productName: {
    fontSize: FONT_SIZES.sm,
    fontWeight: FONT_WEIGHTS.medium,
    color: '#FFFFFF',
    marginBottom: SPACING.xs,
  },
  productPrice: {
    fontSize: FONT_SIZES.sm,
    fontWeight: FONT_WEIGHTS.semiBold,
    color: '#3B82F6',
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  salePrice: {
    fontSize: FONT_SIZES.sm,
    fontWeight: FONT_WEIGHTS.semiBold,
    color: '#10B981',
    marginRight: SPACING.sm,
  },
  originalPrice: {
    fontSize: FONT_SIZES.xs,
    color: 'rgba(255, 255, 255, 0.6)',
    textDecorationLine: 'line-through',
  },
  saleBadge: {
    backgroundColor: 'rgba(245, 158, 11, 0.2)',
    paddingHorizontal: SPACING.sm,
    paddingVertical: SPACING.xs,
    borderRadius: BORDER_RADIUS.sm,
    borderWidth: 1,
    borderColor: '#F59E0B',
  },
  saleBadgeText: {
    fontSize: FONT_SIZES.xs,
    fontWeight: FONT_WEIGHTS.semiBold,
    color: '#F59E0B',
  },
  emptyProducts: {
    alignItems: 'center',
    paddingVertical: SPACING.xl,
  },
  emptyProductsText: {
    fontSize: FONT_SIZES.sm,
    color: 'rgba(255, 255, 255, 0.6)',
    marginTop: SPACING.md,
  },
  contactItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: BORDER_RADIUS.md,
    padding: SPACING.md,
    borderWidth: 1,
    borderColor: 'rgba(59, 130, 246, 0.2)',
  },
  contactText: {
    fontSize: FONT_SIZES.sm,
    color: '#FFFFFF',
    marginLeft: SPACING.md,
  },
  actions: {
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.lg,
  },
  visitButton: {
    borderRadius: BORDER_RADIUS.lg,
    overflow: 'hidden',
  },
  visitButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.lg,
  },
  visitButtonText: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.semiBold,
    color: '#FFFFFF',
    marginRight: SPACING.sm,
  },
});
