// Web polyfills for React Native components and APIs
import { Platform } from 'react-native';

// Only apply polyfills on web platform
if (Platform.OS === 'web') {
  // Polyfill for window object if not available
  if (typeof window === 'undefined') {
    // Create a minimal window object for server-side rendering
    (global as any).window = {
      localStorage: {
        getItem: () => null,
        setItem: () => {},
        removeItem: () => {},
        clear: () => {},
      },
      location: {
        href: '',
        origin: '',
        pathname: '',
      },
      navigator: {
        userAgent: '',
      },
    };
  }

  // Ensure localStorage is available
  if (typeof window !== 'undefined' && !window.localStorage) {
    // Fallback localStorage implementation
    const storage: { [key: string]: string } = {};

    window.localStorage = {
      getItem: (key: string) => storage[key] || null,
      setItem: (key: string, value: string) => {
        storage[key] = value;
      },
      removeItem: (key: string) => {
        delete storage[key];
      },
      clear: () => {
        Object.keys(storage).forEach(key => delete storage[key]);
      },
      length: 0,
      key: () => null,
    };
  }

  // Polyfill for console if not available
  if (typeof console === 'undefined') {
    (global as any).console = {
      log: () => {},
      warn: () => {},
      error: () => {},
      info: () => {},
      debug: () => {},
    };
  }

  // Suppress React Native Web warnings for touch handlers
  const originalWarn = console.warn;
  console.warn = (...args) => {
    const message = args[0];
    if (typeof message === 'string') {
      // Suppress specific React Native Web warnings
      if (
        message.includes('onStartShouldSetResponder') ||
        message.includes('onResponderTerminationRequest') ||
        message.includes('onResponderGrant') ||
        message.includes('onResponderMove') ||
        message.includes('onResponderRelease') ||
        message.includes('onResponderTerminate') ||
        message.includes('transform-origin') ||
        message.includes('Unknown event handler property') ||
        message.includes('"shadow*" style props are deprecated') ||
        message.includes('props.pointerEvents is deprecated') ||
        message.includes('useNativeDriver') ||
        message.includes('Animated: `useNativeDriver` is not supported') ||
        message.includes('Reduced motion setting is enabled') ||
        message.includes('expo-notifications') ||
        message.includes('Listening to push token changes')
      ) {
        return; // Suppress these warnings
      }
    }
    originalWarn.apply(console, args);
  };
}

export {};
