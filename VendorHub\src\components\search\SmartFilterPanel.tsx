import React, { useState, useRef } from 'react';
import { StyleSheet, Animated, Dimensions } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { RTLView, RTLText, RTLTouchableOpacity, RTLIcon, RTLScrollView } from '../RTL';
import { useThemedStyles, useVendors, useProducts, useI18n } from '../../hooks';
import { SPACING, FONT_SIZES, FONT_WEIGHTS, BORDER_RADIUS } from '../../constants/theme';
import { PREMIUM_GRADIENTS } from '../../constants/advancedColors';
import { FadeInView, AnimatedPressable } from '../visual/MicroAnimations';
import { GlassmorphismCard } from '../visual/GlassmorphismCard';
import type { ThemeColors } from '../../contexts/ThemeContext';

const { width: screenWidth } = Dimensions.get('window');

interface FilterOption {
  id: string;
  label: string;
  value: any;
  count?: number;
  icon?: string;
  color?: string;
}

interface FilterSection {
  id: string;
  title: string;
  type: 'single' | 'multiple' | 'range' | 'rating';
  options: FilterOption[];
  selectedValues: any[];
}

interface SmartFilterPanelProps {
  visible: boolean;
  onClose: () => void;
  onApplyFilters: (filters: Record<string, any>) => void;
  onResetFilters: () => void;
  initialFilters?: Record<string, any>;
}

export const SmartFilterPanel: React.FC<SmartFilterPanelProps> = ({
  visible,
  onClose,
  onApplyFilters,
  onResetFilters,
  initialFilters = {},
}) => {
  const styles = useThemedStyles(createStyles);
  const { getApprovedVendors } = useVendors();
  const { getAllProducts } = useProducts();
  const { t } = useI18n();

  const [filters, setFilters] = useState<Record<string, any>>(initialFilters);
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set(['category']));
  
  const slideAnim = useRef(new Animated.Value(screenWidth)).current;
  const backdropOpacity = useRef(new Animated.Value(0)).current;

  const vendors = getApprovedVendors();
  const products = getAllProducts();

  // Generate dynamic filter sections based on data
  const filterSections: FilterSection[] = React.useMemo(() => {
    // Categories
    const categories = [...new Set(products.map(p => p.category).filter(Boolean))];
    const categoryOptions: FilterOption[] = categories.map(category => {
      const count = products.filter(p => p.category === category).length;
      return {
        id: category,
        label: category,
        value: category,
        count,
        icon: getCategoryIcon(category),
        color: getCategoryColor(category),
      };
    });

    // Price ranges
    const prices = products.map(p => p.price).filter(p => p > 0);
    const minPrice = Math.min(...prices);
    const maxPrice = Math.max(...prices);
    const priceRanges = generatePriceRanges(minPrice, maxPrice);

    // Ratings
    const ratingOptions: FilterOption[] = [
      { id: '4+', label: '4+ Stars', value: 4, count: vendors.filter(v => v.rating >= 4).length, icon: 'star' },
      { id: '3+', label: '3+ Stars', value: 3, count: vendors.filter(v => v.rating >= 3).length, icon: 'star' },
      { id: '2+', label: '2+ Stars', value: 2, count: vendors.filter(v => v.rating >= 2).length, icon: 'star' },
    ];

    // Locations (simulated)
    const locationOptions: FilterOption[] = [
      { id: 'manama', label: 'Manama', value: 'manama', count: Math.floor(vendors.length * 0.4), icon: 'location' },
      { id: 'muharraq', label: 'Muharraq', value: 'muharraq', count: Math.floor(vendors.length * 0.3), icon: 'location' },
      { id: 'riffa', label: 'Riffa', value: 'riffa', count: Math.floor(vendors.length * 0.2), icon: 'location' },
      { id: 'hamad-town', label: 'Hamad Town', value: 'hamad-town', count: Math.floor(vendors.length * 0.1), icon: 'location' },
    ];

    // Sort options
    const sortOptions: FilterOption[] = [
      { id: 'relevance', label: t('search.relevance'), value: 'relevance', icon: 'search' },
      { id: 'rating', label: t('search.rating'), value: 'rating', icon: 'star' },
      { id: 'price-low', label: t('search.priceLowToHigh'), value: 'price-asc', icon: 'arrow-up' },
      { id: 'price-high', label: t('search.priceHighToLow'), value: 'price-desc', icon: 'arrow-down' },
      { id: 'distance', label: t('search.distance'), value: 'distance', icon: 'location' },
      { id: 'popularity', label: t('search.popularity'), value: 'popularity', icon: 'trending-up' },
    ];

    return [
      {
        id: 'category',
        title: t('search.category'),
        type: 'multiple',
        options: categoryOptions,
        selectedValues: filters.categories || [],
      },
      {
        id: 'price',
        title: t('search.priceRange'),
        type: 'multiple',
        options: priceRanges,
        selectedValues: filters.priceRanges || [],
      },
      {
        id: 'rating',
        title: t('search.rating'),
        type: 'single',
        options: ratingOptions,
        selectedValues: filters.rating ? [filters.rating] : [],
      },
      {
        id: 'location',
        title: t('search.location'),
        type: 'multiple',
        options: locationOptions,
        selectedValues: filters.locations || [],
      },
      {
        id: 'sort',
        title: t('search.sortBy'),
        type: 'single',
        options: sortOptions,
        selectedValues: filters.sortBy ? [filters.sortBy] : ['relevance'],
      },
    ];
  }, [products, vendors, filters, t]);

  React.useEffect(() => {
    if (visible) {
      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(backdropOpacity, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: screenWidth,
          duration: 250,
          useNativeDriver: true,
        }),
        Animated.timing(backdropOpacity, {
          toValue: 0,
          duration: 250,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [visible]);

  const getCategoryIcon = (category: string): string => {
    const iconMap: Record<string, string> = {
      'Electronics': 'phone-portrait',
      'Fashion': 'shirt',
      'Home & Garden': 'home',
      'Sports': 'fitness',
      'Books': 'book',
      'Automotive': 'car',
      'Health': 'medical',
      'Food': 'restaurant',
    };
    return iconMap[category] || 'cube-outline';
  };

  const getCategoryColor = (category: string): string => {
    const colorMap: Record<string, string> = {
      'Electronics': '#3B82F6',
      'Fashion': '#EC4899',
      'Home & Garden': '#10B981',
      'Sports': '#F59E0B',
      'Books': '#8B5CF6',
      'Automotive': '#EF4444',
      'Health': '#06B6D4',
      'Food': '#84CC16',
    };
    return colorMap[category] || '#6B7280';
  };

  const generatePriceRanges = (min: number, max: number): FilterOption[] => {
    const ranges = [
      { min: 0, max: 10, label: 'Under BHD 10' },
      { min: 10, max: 25, label: 'BHD 10 - 25' },
      { min: 25, max: 50, label: 'BHD 25 - 50' },
      { min: 50, max: 100, label: 'BHD 50 - 100' },
      { min: 100, max: Infinity, label: 'Over BHD 100' },
    ];

    return ranges.map((range, index) => {
      const count = products.filter(p => p.price >= range.min && p.price < range.max).length;
      return {
        id: `price-${index}`,
        label: range.label,
        value: [range.min, range.max === Infinity ? max : range.max],
        count,
        icon: 'pricetag',
        color: '#10B981',
      };
    }).filter(option => option.count > 0);
  };

  const toggleSection = (sectionId: string) => {
    setExpandedSections(prev => {
      const newSet = new Set(prev);
      if (newSet.has(sectionId)) {
        newSet.delete(sectionId);
      } else {
        newSet.add(sectionId);
      }
      return newSet;
    });
  };

  const handleFilterChange = (sectionId: string, optionValue: any, isSelected: boolean) => {
    setFilters(prev => {
      const section = filterSections.find(s => s.id === sectionId);
      if (!section) return prev;

      const newFilters = { ...prev };

      if (section.type === 'single') {
        newFilters[sectionId] = isSelected ? optionValue : null;
      } else if (section.type === 'multiple') {
        const currentValues = newFilters[sectionId] || [];
        if (isSelected) {
          newFilters[sectionId] = [...currentValues, optionValue];
        } else {
          newFilters[sectionId] = currentValues.filter((v: any) => v !== optionValue);
        }
      }

      return newFilters;
    });
  };

  const getActiveFilterCount = () => {
    return Object.values(filters).filter(value => {
      if (Array.isArray(value)) return value.length > 0;
      return value !== null && value !== undefined;
    }).length;
  };

  const handleApplyFilters = () => {
    onApplyFilters(filters);
    onClose();
  };

  const handleResetFilters = () => {
    setFilters({});
    onResetFilters();
  };

  const renderFilterOption = (section: FilterSection, option: FilterOption) => {
    const isSelected = section.type === 'single' 
      ? section.selectedValues.includes(option.value)
      : section.selectedValues.some(v => JSON.stringify(v) === JSON.stringify(option.value));

    return (
      <AnimatedPressable
        key={option.id}
        style={[
          styles.filterOption,
          isSelected && styles.filterOptionSelected,
        ]}
        onPress={() => handleFilterChange(section.id, option.value, !isSelected)}
      >
        <RTLView style={styles.filterOptionContent}>
          {option.icon && (
            <RTLView style={[styles.optionIcon, { backgroundColor: option.color || '#6B7280' }]}>
              <RTLIcon name={option.icon} size={14} color="#FFFFFF" />
            </RTLView>
          )}
          
          <RTLView style={styles.optionText}>
            <RTLText style={[styles.optionLabel, isSelected && styles.optionLabelSelected]}>
              {option.label}
            </RTLText>
            {option.count !== undefined && (
              <RTLText style={styles.optionCount}>({option.count})</RTLText>
            )}
          </RTLView>
          
          <RTLView style={[styles.checkbox, isSelected && styles.checkboxSelected]}>
            {isSelected && <RTLIcon name="checkmark" size={12} color="#FFFFFF" />}
          </RTLView>
        </RTLView>
      </AnimatedPressable>
    );
  };

  const renderFilterSection = (section: FilterSection, index: number) => {
    const isExpanded = expandedSections.has(section.id);
    const selectedCount = section.selectedValues.length;

    return (
      <FadeInView key={section.id} duration={300} delay={index * 100}>
        <GlassmorphismCard intensity="light" style={styles.sectionCard}>
          <RTLTouchableOpacity
            style={styles.sectionHeader}
            onPress={() => toggleSection(section.id)}
          >
            <RTLText style={styles.sectionTitle}>{section.title}</RTLText>
            <RTLView style={styles.sectionHeaderRight}>
              {selectedCount > 0 && (
                <RTLView style={styles.selectedBadge}>
                  <RTLText style={styles.selectedBadgeText}>{selectedCount}</RTLText>
                </RTLView>
              )}
              <RTLIcon 
                name={isExpanded ? 'chevron-up' : 'chevron-down'} 
                size={20} 
                color="rgba(255, 255, 255, 0.7)" 
              />
            </RTLView>
          </RTLTouchableOpacity>

          {isExpanded && (
            <RTLView style={styles.sectionContent}>
              {section.options.map(option => renderFilterOption(section, option))}
            </RTLView>
          )}
        </GlassmorphismCard>
      </FadeInView>
    );
  };

  if (!visible) return null;

  return (
    <RTLView style={styles.container}>
      {/* Backdrop */}
      <Animated.View
        style={[styles.backdrop, { opacity: backdropOpacity }]}
      >
        <RTLTouchableOpacity
          style={styles.backdropTouchable}
          onPress={onClose}
          activeOpacity={1}
        />
      </Animated.View>

      {/* Filter Panel */}
      <Animated.View
        style={[
          styles.panel,
          { transform: [{ translateX: slideAnim }] }
        ]}
      >
        <LinearGradient
          colors={PREMIUM_GRADIENTS.elegantDepth}
          style={styles.panelGradient}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        >
          {/* Header */}
          <RTLView style={styles.header}>
            <RTLText style={styles.headerTitle}>{t('search.filters')}</RTLText>
            <RTLTouchableOpacity style={styles.closeButton} onPress={onClose}>
              <RTLIcon name="close" size={24} color="rgba(255, 255, 255, 0.8)" />
            </RTLTouchableOpacity>
          </RTLView>

          {/* Filter Sections */}
          <RTLScrollView style={styles.content} showsVerticalScrollIndicator={false}>
            {filterSections.map((section, index) => renderFilterSection(section, index))}
          </RTLScrollView>

          {/* Footer Actions */}
          <RTLView style={styles.footer}>
            <RTLTouchableOpacity
              style={styles.resetButton}
              onPress={handleResetFilters}
            >
              <RTLText style={styles.resetButtonText}>{t('search.reset')}</RTLText>
            </RTLTouchableOpacity>

            <RTLTouchableOpacity
              style={styles.applyButton}
              onPress={handleApplyFilters}
            >
              <LinearGradient
                colors={['#3B82F6', '#1D4ED8']}
                style={styles.applyButtonGradient}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
              >
                <RTLText style={styles.applyButtonText}>
                  {t('search.applyFilters')} {getActiveFilterCount() > 0 && `(${getActiveFilterCount()})`}
                </RTLText>
              </LinearGradient>
            </RTLTouchableOpacity>
          </RTLView>
        </LinearGradient>
      </Animated.View>
    </RTLView>
  );
};

const createStyles = (colors: ThemeColors) => StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 2000,
  },
  backdrop: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  backdropTouchable: {
    flex: 1,
  },
  panel: {
    position: 'absolute',
    top: 0,
    right: 0,
    bottom: 0,
    width: screenWidth * 0.85,
    maxWidth: 400,
  },
  panelGradient: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.lg,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
  },
  headerTitle: {
    fontSize: FONT_SIZES.xl,
    fontWeight: FONT_WEIGHTS.bold,
    color: '#FFFFFF',
  },
  closeButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  content: {
    flex: 1,
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
  },

  // Filter Sections
  sectionCard: {
    marginBottom: SPACING.md,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.md,
  },
  sectionTitle: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.semiBold,
    color: '#FFFFFF',
  },
  sectionHeaderRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  selectedBadge: {
    backgroundColor: '#3B82F6',
    borderRadius: 10,
    paddingHorizontal: SPACING.sm,
    paddingVertical: SPACING.xs,
    marginRight: SPACING.sm,
    minWidth: 20,
    alignItems: 'center',
  },
  selectedBadgeText: {
    fontSize: FONT_SIZES.xs,
    fontWeight: FONT_WEIGHTS.bold,
    color: '#FFFFFF',
  },
  sectionContent: {
    paddingHorizontal: SPACING.md,
    paddingBottom: SPACING.md,
  },

  // Filter Options
  filterOption: {
    marginBottom: SPACING.sm,
    borderRadius: BORDER_RADIUS.md,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
  },
  filterOptionSelected: {
    backgroundColor: 'rgba(59, 130, 246, 0.2)',
    borderColor: '#3B82F6',
  },
  filterOptionContent: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.md,
  },
  optionIcon: {
    width: 24,
    height: 24,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: SPACING.md,
  },
  optionText: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  optionLabel: {
    fontSize: FONT_SIZES.sm,
    color: 'rgba(255, 255, 255, 0.8)',
    fontWeight: FONT_WEIGHTS.medium,
  },
  optionLabelSelected: {
    color: '#FFFFFF',
    fontWeight: FONT_WEIGHTS.semiBold,
  },
  optionCount: {
    fontSize: FONT_SIZES.xs,
    color: 'rgba(255, 255, 255, 0.5)',
    marginLeft: SPACING.sm,
  },
  checkbox: {
    width: 20,
    height: 20,
    borderRadius: 4,
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.3)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  checkboxSelected: {
    backgroundColor: '#3B82F6',
    borderColor: '#3B82F6',
  },

  // Footer
  footer: {
    flexDirection: 'row',
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.lg,
    borderTopWidth: 1,
    borderTopColor: 'rgba(255, 255, 255, 0.1)',
    gap: SPACING.md,
  },
  resetButton: {
    flex: 1,
    paddingVertical: SPACING.md,
    borderRadius: BORDER_RADIUS.lg,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  resetButtonText: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.semiBold,
    color: 'rgba(255, 255, 255, 0.8)',
  },
  applyButton: {
    flex: 2,
    borderRadius: BORDER_RADIUS.lg,
    overflow: 'hidden',
  },
  applyButtonGradient: {
    paddingVertical: SPACING.md,
    alignItems: 'center',
    justifyContent: 'center',
  },
  applyButtonText: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.bold,
    color: '#FFFFFF',
  },
});
