# Text Node Issues Report

Generated on: 2025-07-17T01:29:09.002Z
Files scanned: 141
Files with potential issues: 8

## Files with Potential Text Node Issues

### src\components\media\AdaptiveImageGallery.tsx

⚠️ **Line 318**: Function call in JSX - ensure it returns JSX or is wrapped in Text
```tsx
return <RTLView style={styles.container}>{renderLayout()}</RTLView>;
```

### src\components\media\VideoPreview.tsx

⚠️ **Line 214**: Function call in JSX - ensure it returns JSX or is wrapped in Text
```tsx
return <RTLView style={containerStyle}>{renderError()}</RTLView>;
```

### src\components\PremiumCard.tsx

⚠️ **Line 39**: Variable in JSX - ensure it returns JSX or is wrapped in Text
```tsx
{children && <RTLView style={styles.body}>{children}</RTLView>}
```

### src\components\RTL\RTLSafeAreaView.tsx

⚠️ **Line 102**: Variable in JSX - ensure it returns JSX or is wrapped in Text
```tsx
return <SafeAreaView style={rtlStyle} {...props}>{children}</SafeAreaView>;
```

### src\components\RTL\RTLView.tsx

⚠️ **Line 102**: Variable in JSX - ensure it returns JSX or is wrapped in Text
```tsx
return <View style={rtlStyle} {...props}>{children}</View>;
```

### src\components\ui\SwipeableRow.tsx

⚠️ **Line 183**: Variable in JSX - ensure it returns JSX or is wrapped in Text
```tsx
return <RTLView style={style}>{children}</RTLView>;
```

### components\Collapsible.tsx

⚠️ **Line 29**: Variable in JSX - ensure it returns JSX or is wrapped in Text
```tsx
<ThemedText type="defaultSemiBold">{title}</ThemedText>
```

⚠️ **Line 31**: Variable in JSX - ensure it returns JSX or is wrapped in Text
```tsx
{isOpen && <ThemedView style={styles.content}>{children}</ThemedView>}
```

### components\ParallaxScrollView.tsx

⚠️ **Line 62**: Variable in JSX - ensure it returns JSX or is wrapped in Text
```tsx
<ThemedView style={styles.content}>{children}</ThemedView>
```

## Recommendations

1. **Wrap strings in Text components**: All text content should be wrapped in `<RTLText>` or `<Text>` components
2. **Check conditional expressions**: Ensure conditional expressions that might return strings are properly wrapped
3. **Validate function returns**: Ensure functions called in JSX return proper JSX elements or are wrapped in Text
4. **Remove empty expressions**: Clean up empty JSX expressions `{}`
5. **Handle whitespace**: Be careful with whitespace between JSX elements