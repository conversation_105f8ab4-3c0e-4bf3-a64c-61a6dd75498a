import React, { useRef, useState, useEffect } from 'react';
import { StyleSheet, Animated, Vibration } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { RTLView, RTLText, RTLIcon, RTLTouchableOpacity } from '../RTL';
import { useThemedStyles, useI18n } from '../../hooks';
import { SPACING, FONT_SIZES, FONT_WEIGHTS, BORDER_RADIUS } from '../../constants/theme';
import { PREMIUM_GRADIENTS } from '../../constants/advancedColors';
import { PulseView, FadeInView, BouncyView } from '../visual/MicroAnimations';
import type { ThemeColors } from '../../contexts/ThemeContext';

interface ShakeToShuffleProps {
  onShuffle?: () => void;
  onShakeDetected?: () => void;
  enabled?: boolean;
  shakeThreshold?: number;
  cooldownDuration?: number;
  hapticFeedback?: boolean;
  showIndicator?: boolean;
  indicatorPosition?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right' | 'center';
}

export const ShakeToShuffle: React.FC<ShakeToShuffleProps> = ({
  onShuffle,
  onShakeDetected,
  enabled = true,
  shakeThreshold = 2.5,
  cooldownDuration = 2000,
  hapticFeedback = true,
  showIndicator = true,
  indicatorPosition = 'top-right',
}) => {
  const styles = useThemedStyles(createStyles);
  const { t } = useI18n();
  
  const [isShaking, setIsShaking] = useState(false);
  const [isOnCooldown, setIsOnCooldown] = useState(false);
  const [shakeIntensity, setShakeIntensity] = useState(0);
  
  const shakeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const rotateAnim = useRef(new Animated.Value(0)).current;
  const opacityAnim = useRef(new Animated.Value(0.7)).current;
  
  const lastShakeTime = useRef(0);
  const shakeCount = useRef(0);
  const cooldownTimer = useRef<NodeJS.Timeout>();

  const triggerHaptic = (pattern: 'light' | 'medium' | 'heavy' | 'success' = 'medium') => {
    if (!hapticFeedback) return;
    
    switch (pattern) {
      case 'light':
        Vibration.vibrate(25);
        break;
      case 'medium':
        Vibration.vibrate(50);
        break;
      case 'heavy':
        Vibration.vibrate(100);
        break;
      case 'success':
        Vibration.vibrate([0, 50, 100, 50]);
        break;
    }
  };

  const animateShake = (intensity: number) => {
    const shakeDistance = Math.min(intensity * 10, 20);
    
    Animated.sequence([
      Animated.timing(shakeAnim, {
        toValue: shakeDistance,
        duration: 50,
        useNativeDriver: true,
      }),
      Animated.timing(shakeAnim, {
        toValue: -shakeDistance,
        duration: 50,
        useNativeDriver: true,
      }),
      Animated.timing(shakeAnim, {
        toValue: shakeDistance * 0.5,
        duration: 50,
        useNativeDriver: true,
      }),
      Animated.timing(shakeAnim, {
        toValue: 0,
        duration: 50,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const animateSuccess = () => {
    Animated.parallel([
      Animated.sequence([
        Animated.timing(scaleAnim, {
          toValue: 1.3,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.spring(scaleAnim, {
          toValue: 1,
          useNativeDriver: true,
          tension: 300,
          friction: 10,
        }),
      ]),
      Animated.timing(rotateAnim, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.sequence([
        Animated.timing(opacityAnim, {
          toValue: 1,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(opacityAnim, {
          toValue: 0.7,
          duration: 400,
          useNativeDriver: true,
        }),
      ]),
    ]).start(() => {
      rotateAnim.setValue(0);
    });
  };

  const handleShake = (intensity: number) => {
    const now = Date.now();
    
    if (isOnCooldown || !enabled) return;
    
    setIsShaking(true);
    setShakeIntensity(intensity);
    onShakeDetected?.();
    
    // Animate shake indicator
    animateShake(intensity);
    
    // Count consecutive shakes
    if (now - lastShakeTime.current < 1000) {
      shakeCount.current += 1;
    } else {
      shakeCount.current = 1;
    }
    
    lastShakeTime.current = now;
    
    // Trigger shuffle after 2-3 consecutive shakes
    if (shakeCount.current >= 2) {
      triggerShuffle();
    } else {
      triggerHaptic('light');
      setTimeout(() => setIsShaking(false), 500);
    }
  };

  const triggerShuffle = () => {
    setIsOnCooldown(true);
    shakeCount.current = 0;
    
    triggerHaptic('success');
    animateSuccess();
    onShuffle?.();
    
    // Start cooldown
    cooldownTimer.current = setTimeout(() => {
      setIsOnCooldown(false);
      setIsShaking(false);
    }, cooldownDuration);
  };

  // Fallback shake detection setup
  useEffect(() => {
    if (!enabled) return;

    // Fallback: Use rapid tap detection as shake simulation
    console.warn('Shake detection requires expo-sensors package. Install with: expo install expo-sensors');

    // Alternative: Use rapid gesture patterns as shake detection
    let rapidTapCount = 0;
    const rapidTapThreshold = 3;
    const rapidTapWindow = 1000; // 1 second

    const detectRapidTaps = () => {
      rapidTapCount++;
      if (rapidTapCount >= rapidTapThreshold) {
        // Simulate shake with medium intensity
        handleShake(2.0);
        rapidTapCount = 0;
      }

      setTimeout(() => {
        rapidTapCount = Math.max(0, rapidTapCount - 1);
      }, rapidTapWindow);
    };

    // Store the detection function globally for manual triggering
    (window as any).__detectShakeGesture = detectRapidTaps;

    return () => {
      if (cooldownTimer.current) {
        clearTimeout(cooldownTimer.current);
      }
      delete (window as any).__detectShakeGesture;
    };
  }, [enabled, shakeThreshold]);

  const getIndicatorStyle = () => {
    const baseStyle = {
      position: 'absolute' as const,
      zIndex: 1000,
    };

    switch (indicatorPosition) {
      case 'top-left':
        return { ...baseStyle, top: 60, left: SPACING.md };
      case 'top-right':
        return { ...baseStyle, top: 60, right: SPACING.md };
      case 'bottom-left':
        return { ...baseStyle, bottom: 100, left: SPACING.md };
      case 'bottom-right':
        return { ...baseStyle, bottom: 100, right: SPACING.md };
      case 'center':
        return { 
          ...baseStyle, 
          top: '50%', 
          left: '50%', 
          transform: [{ translateX: -30 }, { translateY: -30 }] 
        };
      default:
        return { ...baseStyle, top: 60, right: SPACING.md };
    }
  };

  if (!showIndicator) return null;

  return (
    <Animated.View
      style={[
        styles.container,
        getIndicatorStyle(),
        {
          opacity: opacityAnim,
          transform: [
            { translateX: shakeAnim },
            { scale: scaleAnim },
            { 
              rotate: rotateAnim.interpolate({
                inputRange: [0, 1],
                outputRange: ['0deg', '360deg'],
              })
            },
          ],
        },
      ]}
    >
      <LinearGradient
        colors={isShaking ? PREMIUM_GRADIENTS.royalSpotlight : PREMIUM_GRADIENTS.elegantDepth}
        style={styles.indicator}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        {isOnCooldown ? (
          <FadeInView duration={300}>
            <RTLIcon name="checkmark-circle" size={20} color="#10B981" />
          </FadeInView>
        ) : isShaking ? (
          <PulseView minScale={0.8} maxScale={1.2} duration={200}>
            <RTLIcon name="shuffle" size={20} color="#FFD700" />
          </PulseView>
        ) : (
          <BouncyView trigger={false}>
            <RTLIcon name="phone-portrait-outline" size={20} color="#3B82F6" />
          </BouncyView>
        )}
        
        {/* Shake intensity indicator */}
        {isShaking && (
          <RTLView style={styles.intensityBar}>
            <RTLView 
              style={[
                styles.intensityFill,
                { width: `${Math.min(shakeIntensity * 50, 100)}%` }
              ]}
            />
          </RTLView>
        )}
      </LinearGradient>
      
      {/* Tooltip */}
      {!isOnCooldown && (
        <RTLView style={styles.tooltip}>
          <RTLText style={styles.tooltipText}>
            {isShaking ? t('gestures.keepShaking') : t('gestures.shakeToShuffle')}
          </RTLText>
        </RTLView>
      )}

      {/* Manual trigger for testing (when accelerometer not available) */}
      {__DEV__ && (
        <RTLTouchableOpacity
          style={styles.testButton}
          onPress={() => handleShake(2.0)}
        >
          <RTLText style={styles.testButtonText}>Test Shake</RTLText>
        </RTLTouchableOpacity>
      )}
    </Animated.View>
  );
};

const createStyles = (colors: ThemeColors) => StyleSheet.create({
  container: {
    width: 60,
    height: 60,
  },
  indicator: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  intensityBar: {
    position: 'absolute',
    bottom: 8,
    left: 8,
    right: 8,
    height: 3,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 1.5,
    overflow: 'hidden',
  },
  intensityFill: {
    height: '100%',
    backgroundColor: '#FFD700',
    borderRadius: 1.5,
  },
  tooltip: {
    position: 'absolute',
    top: -35,
    left: -20,
    right: -20,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    paddingHorizontal: SPACING.sm,
    paddingVertical: SPACING.xs,
    borderRadius: BORDER_RADIUS.md,
    alignItems: 'center',
  },
  tooltipText: {
    fontSize: FONT_SIZES.xs,
    color: '#FFFFFF',
    fontWeight: FONT_WEIGHTS.medium,
    textAlign: 'center',
  },
  testButton: {
    position: 'absolute',
    bottom: -40,
    left: '50%',
    transform: [{ translateX: -30 }],
    backgroundColor: 'rgba(59, 130, 246, 0.8)',
    paddingHorizontal: SPACING.sm,
    paddingVertical: SPACING.xs,
    borderRadius: BORDER_RADIUS.sm,
  },
  testButtonText: {
    fontSize: FONT_SIZES.xs,
    color: '#FFFFFF',
    fontWeight: FONT_WEIGHTS.medium,
  },
});
