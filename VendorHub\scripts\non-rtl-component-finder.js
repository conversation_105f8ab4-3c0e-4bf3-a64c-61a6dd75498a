#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Configuration
const config = {
  srcDir: path.join(__dirname, '../src'),
  appDir: path.join(__dirname, '../app'),
  componentsDir: path.join(__dirname, '../components'),
  outputFile: path.join(__dirname, '../NON_RTL_COMPONENTS_REPORT.md'),
  excludePatterns: [
    /node_modules/,
    /\.git/,
    /\.expo/,
    /dist/,
    /build/,
    /coverage/,
    /\.DS_Store/,
    /\.env/,
    /package-lock\.json/,
    /yarn\.lock/,
    /\.log$/,
    /\.md$/,
    /\.json$/,
    /\.js\.map$/,
    /\.d\.ts$/,
    /RTL.*\.tsx?$/,  // Exclude RTL components themselves
    /index\.ts$/,    // Exclude index files
    /types\.ts$/,    // Exclude type definition files
    /constants/,     // Exclude constants
    /utils/,         // Exclude utility files
    /services/,      // Exclude service files
    /hooks/,         // Exclude hooks
    /contexts/,      // Exclude context files
    /navigation/,    // Exclude navigation files
    /scripts/,       // Exclude scripts
    /assets/,        // Exclude assets
  ],
  fileExtensions: ['.tsx', '.ts', '.jsx', '.js'],
  
  // Components to check for
  basicComponents: [
    'View',
    'Text', 
    'SafeAreaView',
    'ScrollView',
    'FlatList',
    'SectionList',
    'TextInput',
    'TouchableOpacity',
    'TouchableHighlight',
    'TouchableWithoutFeedback',
    'Pressable'
  ],
  
  iconComponents: [
    'Ionicons',
    'MaterialIcons',
    'FontAwesome',
    'AntDesign',
    'Entypo',
    'EvilIcons',
    'Feather',
    'Foundation',
    'MaterialCommunityIcons',
    'Octicons',
    'SimpleLineIcons',
    'Zocial'
  ]
};

class NonRTLComponentFinder {
  constructor() {
    this.results = {
      filesWithBasicComponents: [],
      filesWithIconComponents: [],
      totalFilesScanned: 0,
      summary: {
        basicComponentUsage: {},
        iconComponentUsage: {},
        filesNeedingConversion: new Set()
      }
    };
  }

  shouldExcludeFile(filePath) {
    const relativePath = path.relative(process.cwd(), filePath);
    return config.excludePatterns.some(pattern => pattern.test(relativePath));
  }

  hasValidExtension(filePath) {
    return config.fileExtensions.some(ext => filePath.endsWith(ext));
  }

  analyzeFileContent(filePath, content) {
    const relativePath = path.relative(process.cwd(), filePath);
    const lines = content.split('\n');
    
    const fileResult = {
      path: relativePath,
      basicComponents: [],
      iconComponents: [],
      issues: []
    };

    // Check for basic React Native components
    config.basicComponents.forEach(component => {
      const importRegex = new RegExp(`import.*\\b${component}\\b.*from\\s+['"]react-native['"]`, 'g');
      const usageRegex = new RegExp(`<${component}\\b`, 'g');
      
      let hasImport = false;
      let usageCount = 0;
      
      lines.forEach((line, index) => {
        if (importRegex.test(line)) {
          hasImport = true;
        }
        const matches = line.match(usageRegex);
        if (matches) {
          usageCount += matches.length;
          fileResult.issues.push({
            line: index + 1,
            content: line.trim(),
            component: component,
            type: 'basic-component'
          });
        }
      });
      
      if (hasImport || usageCount > 0) {
        fileResult.basicComponents.push({
          component,
          hasImport,
          usageCount
        });
        
        if (!this.results.summary.basicComponentUsage[component]) {
          this.results.summary.basicComponentUsage[component] = 0;
        }
        this.results.summary.basicComponentUsage[component] += usageCount;
      }
    });

    // Check for icon components
    config.iconComponents.forEach(component => {
      const importRegex = new RegExp(`import.*\\b${component}\\b.*from\\s+['"]@expo/vector-icons['"]`, 'g');
      const usageRegex = new RegExp(`<${component}\\b`, 'g');
      
      let hasImport = false;
      let usageCount = 0;
      
      lines.forEach((line, index) => {
        if (importRegex.test(line)) {
          hasImport = true;
        }
        const matches = line.match(usageRegex);
        if (matches) {
          usageCount += matches.length;
          fileResult.issues.push({
            line: index + 1,
            content: line.trim(),
            component: component,
            type: 'icon-component'
          });
        }
      });
      
      if (hasImport || usageCount > 0) {
        fileResult.iconComponents.push({
          component,
          hasImport,
          usageCount
        });
        
        if (!this.results.summary.iconComponentUsage[component]) {
          this.results.summary.iconComponentUsage[component] = 0;
        }
        this.results.summary.iconComponentUsage[component] += usageCount;
      }
    });

    // Add to results if any non-RTL components found
    if (fileResult.basicComponents.length > 0) {
      this.results.filesWithBasicComponents.push(fileResult);
      this.results.summary.filesNeedingConversion.add(relativePath);
    }
    
    if (fileResult.iconComponents.length > 0) {
      this.results.filesWithIconComponents.push(fileResult);
      this.results.summary.filesNeedingConversion.add(relativePath);
    }
  }

  scanDirectory(dirPath) {
    if (!fs.existsSync(dirPath)) {
      console.log(`Directory not found: ${dirPath}`);
      return;
    }

    const items = fs.readdirSync(dirPath);
    
    for (const item of items) {
      const fullPath = path.join(dirPath, item);
      
      if (this.shouldExcludeFile(fullPath)) {
        continue;
      }
      
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        this.scanDirectory(fullPath);
      } else if (stat.isFile() && this.hasValidExtension(fullPath)) {
        try {
          const content = fs.readFileSync(fullPath, 'utf8');
          this.analyzeFileContent(fullPath, content);
          this.results.totalFilesScanned++;
        } catch (error) {
          console.error(`Error reading file ${fullPath}:`, error.message);
        }
      }
    }
  }

  generateReport() {
    const timestamp = new Date().toLocaleString();
    const totalIssues = this.results.summary.filesNeedingConversion.size;
    
    let report = `# Non-RTL Components Detection Report\n\n`;
    report += `**Generated:** ${timestamp}\n`;
    report += `**Files Scanned:** ${this.results.totalFilesScanned}\n`;
    report += `**Files Needing Conversion:** ${totalIssues}\n\n`;
    
    if (totalIssues === 0) {
      report += `## 🎉 Excellent! No Non-RTL Components Found\n\n`;
      report += `All files are using RTL components correctly. The application is fully RTL compliant.\n\n`;
      return report;
    }
    
    report += `## 📊 Summary\n\n`;
    
    // Basic components summary
    if (Object.keys(this.results.summary.basicComponentUsage).length > 0) {
      report += `### Basic React Native Components Found\n\n`;
      report += `| Component | Usage Count | Replacement |\n`;
      report += `|-----------|-------------|-------------|\n`;
      
      Object.entries(this.results.summary.basicComponentUsage).forEach(([component, count]) => {
        const replacement = this.getReplacementComponent(component);
        report += `| ${component} | ${count} | ${replacement} |\n`;
      });
      report += `\n`;
    }
    
    // Icon components summary
    if (Object.keys(this.results.summary.iconComponentUsage).length > 0) {
      report += `### Icon Components Found\n\n`;
      report += `| Component | Usage Count | Replacement |\n`;
      report += `|-----------|-------------|-------------|\n`;
      
      Object.entries(this.results.summary.iconComponentUsage).forEach(([component, count]) => {
        report += `| ${component} | ${count} | RTLIcon |\n`;
      });
      report += `\n`;
    }
    
    // Detailed file analysis
    if (this.results.filesWithBasicComponents.length > 0) {
      report += `## 📁 Files with Basic React Native Components\n\n`;

      this.results.filesWithBasicComponents.forEach(file => {
        report += `### ${file.path}\n\n`;

        file.basicComponents.forEach(comp => {
          report += `**${comp.component}** (${comp.usageCount} usage${comp.usageCount > 1 ? 's' : ''})\n`;
        });

        if (file.issues.length > 0) {
          report += `\n**Issues found:**\n`;
          file.issues.filter(issue => issue.type === 'basic-component').forEach(issue => {
            report += `- Line ${issue.line}: \`${issue.content}\`\n`;
          });
        }
        report += `\n`;
      });
    }

    if (this.results.filesWithIconComponents.length > 0) {
      report += `## 🎨 Files with Icon Components\n\n`;

      this.results.filesWithIconComponents.forEach(file => {
        report += `### ${file.path}\n\n`;

        file.iconComponents.forEach(comp => {
          report += `**${comp.component}** (${comp.usageCount} usage${comp.usageCount > 1 ? 's' : ''})\n`;
        });

        if (file.issues.length > 0) {
          report += `\n**Issues found:**\n`;
          file.issues.filter(issue => issue.type === 'icon-component').forEach(issue => {
            report += `- Line ${issue.line}: \`${issue.content}\`\n`;
          });
        }
        report += `\n`;
      });
    }

    // Conversion guide
    report += `## 🔧 Conversion Guide\n\n`;
    report += `### Step 1: Update Imports\n\n`;
    report += `Replace React Native imports with RTL equivalents:\n\n`;
    report += `\`\`\`typescript\n`;
    report += `// Before\n`;
    report += `import { View, Text, ScrollView } from 'react-native';\n`;
    report += `import { Ionicons } from '@expo/vector-icons';\n\n`;
    report += `// After\n`;
    report += `import { RTLView, RTLText, RTLScrollView, RTLIcon } from '../components/RTL';\n`;
    report += `\`\`\`\n\n`;

    report += `### Step 2: Update Component Usage\n\n`;
    report += `Replace component names in JSX:\n\n`;
    report += `\`\`\`typescript\n`;
    report += `// Before\n`;
    report += `<View><Text>Hello</Text></View>\n`;
    report += `<Ionicons name="arrow-back" />\n\n`;
    report += `// After\n`;
    report += `<RTLView><RTLText>Hello</RTLText></RTLView>\n`;
    report += `<RTLIcon name="arrow-back" />\n`;
    report += `\`\`\`\n\n`;

    return report;
  }

  getReplacementComponent(component) {
    const replacements = {
      'View': 'RTLView',
      'Text': 'RTLText',
      'SafeAreaView': 'RTLSafeAreaView',
      'ScrollView': 'RTLScrollView',
      'FlatList': 'RTLFlatList',
      'SectionList': 'RTLSectionList',
      'TextInput': 'RTLInput',
      'TouchableOpacity': 'RTLTouchableOpacity',
      'TouchableHighlight': 'RTLTouchableOpacity',
      'TouchableWithoutFeedback': 'RTLTouchableOpacity',
      'Pressable': 'RTLTouchableOpacity'
    };

    return replacements[component] || 'RTL equivalent needed';
  }

  run() {
    console.log('🔍 Scanning for non-RTL components...');
    
    // Scan directories
    [config.srcDir, config.appDir, config.componentsDir].forEach(dir => {
      if (fs.existsSync(dir)) {
        console.log(`Scanning: ${dir}`);
        this.scanDirectory(dir);
      }
    });
    
    // Generate and save report
    const report = this.generateReport();
    fs.writeFileSync(config.outputFile, report);
    
    console.log(`\n📄 Report saved to: ${config.outputFile}`);
    console.log(`📊 Files scanned: ${this.results.totalFilesScanned}`);
    console.log(`⚠️  Files needing conversion: ${this.results.summary.filesNeedingConversion.size}`);
    
    if (this.results.summary.filesNeedingConversion.size > 0) {
      console.log('\n🔧 Files that need RTL conversion:');
      Array.from(this.results.summary.filesNeedingConversion).forEach(file => {
        console.log(`   - ${file}`);
      });
    } else {
      console.log('\n🎉 All files are RTL compliant!');
    }
    
    return this.results.summary.filesNeedingConversion.size === 0;
  }
}

// Run the scanner
if (require.main === module) {
  const scanner = new NonRTLComponentFinder();
  const isCompliant = scanner.run();
  process.exit(isCompliant ? 0 : 1);
}

module.exports = NonRTLComponentFinder;
