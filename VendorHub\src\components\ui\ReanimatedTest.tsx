import React, { useEffect } from 'react';
import { StyleSheet } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  withRepeat,
  withSequence,
  runOnJS,
  interpolate,
  Extrapolation,
} from 'react-native-reanimated';
import { RTLView, RTLText, RTLTouchableOpacity } from '../RTL';
import { useThemedStyles } from '../../hooks';
import type ThemeColors from '../../contexts/ThemeContext';

interface ReanimatedTestProps {
  onTestComplete?: (success: boolean) => void;
}

export const ReanimatedTest: React.FC<ReanimatedTestProps> = ({ onTestComplete }) => {
  const styles = useThemedStyles(createStyles);
  
  // Test shared values
  const scale = useSharedValue(1);
  const rotation = useSharedValue(0);
  const opacity = useSharedValue(1);
  const translateX = useSharedValue(0);
  
  // Test worklet functions
  const testWorklets = () => {
    'worklet';
    console.log('Worklet function executed successfully');
    return true;
  };

  // Test animations
  const runAnimationTests = () => {
    // Test 1: Spring animation
    scale.value = withSpring(1.2, {
      damping: 10,
      stiffness: 100,
    }, (finished) => {
      'worklet';
      if (finished) {
        scale.value = withSpring(1);
      }
    });

    // Test 2: Timing animation with sequence
    rotation.value = withRepeat(
      withSequence(
        withTiming(360, { duration: 1000 }),
        withTiming(0, { duration: 1000 })
      ),
      2,
      false
    );

    // Test 3: Interpolation
    translateX.value = withTiming(100, { duration: 1000 }, (finished) => {
      'worklet';
      if (finished) {
        translateX.value = withTiming(0, { duration: 1000 });
        // Test runOnJS
        runOnJS(handleTestComplete)(true);
      }
    });

    // Test 4: Opacity animation
    opacity.value = withSequence(
      withTiming(0.3, { duration: 500 }),
      withTiming(1, { duration: 500 })
    );
  };

  const handleTestComplete = (success: boolean) => {
    console.log('Reanimated 3 test completed:', success ? 'SUCCESS' : 'FAILED');
    onTestComplete?.(success);
  };

  // Animated styles using worklets
  const animatedStyle = useAnimatedStyle(() => {
    const interpolatedScale = interpolate(
      scale.value,
      [1, 1.2],
      [1, 1.2],
      Extrapolation.CLAMP
    );

    return {
      transform: [
        { scale: interpolatedScale },
        { rotate: `${rotation.value}deg` },
        { translateX: translateX.value },
      ],
      opacity: opacity.value,
    };
  }, []);

  const containerStyle = useAnimatedStyle(() => {
    return {
      backgroundColor: interpolate(
        scale.value,
        [1, 1.2],
        [0, 0.1],
        Extrapolation.CLAMP
      ) > 0.05 ? '#667eea20' : 'transparent',
    };
  }, []);

  useEffect(() => {
    // Test worklet execution
    try {
      const workletResult = testWorklets();
      console.log('Worklet test result:', workletResult);
    } catch (error) {
      console.error('Worklet test failed:', error);
      handleTestComplete(false);
    }
  }, []);

  return (
    <Animated.View style={[styles.container, containerStyle]}>
      <RTLText style={styles.title}>Reanimated 3 Test</RTLText>
      
      <RTLTouchableOpacity onPress={runAnimationTests} activeOpacity={0.8}>
        <Animated.View style={[styles.testBox, animatedStyle]}>
          <RTLText style={styles.testText}>Tap to Test</RTLText>
        </Animated.View>
      </RTLTouchableOpacity>
      
      <RTLView style={styles.infoContainer}>
        <RTLText style={styles.infoText}>
          Tests: Spring, Timing, Sequence, Repeat, Interpolation, Worklets, runOnJS
        </RTLText>
      </RTLView>
    </Animated.View>
  );
};

const createStyles = (theme: ThemeColors) => StyleSheet.create({
  container: {
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 200,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: theme.border,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.text,
    marginBottom: 20,
  },
  testBox: {
    width: 100,
    height: 100,
    backgroundColor: '#667eea',
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20,
  },
  testText: {
    color: 'white',
    fontWeight: 'bold',
    textAlign: 'center',
  },
  infoContainer: {
    marginTop: 10,
    paddingHorizontal: 10,
  },
  infoText: {
    fontSize: 12,
    color: theme.textSecondary,
    textAlign: 'center',
    lineHeight: 16,
  },
});
