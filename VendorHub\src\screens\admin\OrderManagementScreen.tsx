import React from 'react';
import {
  StyleSheet,
  RefreshControl } from 'react-native';
import { useThemedStyles, useOrders, useVendors, useI18n } from '../../hooks';
import { Card, Button, EmptyState, StatusBadge } from '../../components';
import { RTLView, RTLText, RTLIcon, RTLSafeAreaView, RTLFlatList, RTLInput, RTLTouchableOpacity } from '../../components/RTL';
import {
  SPACING,
  FONT_SIZES,
  FONT_WEIGHTS,
  BORDER_RADIUS } from '../../constants/theme';
import { formatCurrency } from '../../utils';
import type ThemeColors  from '../../contexts/ThemeContext';
import type Order  from '../../contexts/DataContext';

interface OrderManagementScreenProps {
  navigation: any;
}

export const OrderManagementScreen: React.FC<OrderManagementScreenProps> = ({ navigation }) => {
  const styles = useThemedStyles(createStyles);
  const { getAllOrders } = useOrders();
  const { getVendorById } = useVendors();
  const { t, currentLanguage } = useI18n();
  const [refreshing, setRefreshing] = React.useState(false);
  const [searchQuery, setSearchQuery] = React.useState('');
  const [selectedStatus, setSelectedStatus] = React.useState<string | null>(null);
  const [sortBy, setSortBy] = React.useState<'date' | 'amount' | 'status'>('date');

  const allOrders = getAllOrders();

  const onRefresh = React.useCallback(async () => {
    setRefreshing(true);
    // Simulate refresh delay
    setTimeout(() => setRefreshing(false), 1000);
  }, []);

  // Filter and sort orders
  const filteredOrders = React.useMemo(() => {
    let filtered = allOrders.filter(order => {
      const matchesSearch = order.id.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           order.customerName.toLowerCase().includes(searchQuery.toLowerCase());
      const matchesStatus = !selectedStatus || order.status === selectedStatus;
      
      return matchesSearch && matchesStatus;
    });

    // Sort orders
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'date':
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
        case 'amount':
          return b.totalAmount - a.totalAmount;
        case 'status':
          return a.status.localeCompare(b.status);
        default:
          return 0;
      }
    });

    return filtered;
  }, [allOrders, searchQuery, selectedStatus, sortBy]);

  const statuses = ['pending', 'processing', 'shipped', 'completed', 'cancelled'];

  const getStatusVariant = (status: string) => {
    switch (status) {
      case 'pending': return 'warning';
      case 'processing': return 'info';
      case 'shipped': return 'primary';
      case 'completed': return 'success';
      case 'cancelled': return 'error';
      default: return 'default';
    }
  };

  const renderOrderItem = ({ item }: { item: Order }) => {
    const vendor = getVendorById(item.items[0]?.vendorId || '');
    
    return (
      <Card style={styles.orderCard} variant="elevated">
        <RTLTouchableOpacity
          style={styles.orderContent}
          onPress={() => navigation.navigate('OrderDetails', { orderId: item.id })}
        >
          <RTLView style={styles.orderHeader}>
            <RTLView style={styles.orderInfo}>
              <RTLText style={styles.orderId}>{t('orders.orderNumber')} #{item.id}</RTLText>
              <RTLText style={styles.orderDate}>
                {new Date(item.createdAt).toLocaleDateString(currentLanguage === 'ar' ? 'ar-BH' : 'en-US', {
                  year: 'numeric',
                  month: 'short',
                  day: 'numeric',
                  hour: '2-digit',
                  minute: '2-digit',
                })}
              </RTLText>
            </RTLView>
            <StatusBadge
              status={item.status}
              type="order"
            />
          </RTLView>

          <RTLView style={styles.orderDetails}>
            <RTLView style={styles.customerInfo}>
              <RTLIcon name="person-outline" size={16} color="#666" />
              <RTLText style={styles.customerName}>{item.customerName}</RTLText>
            </RTLView>

            <RTLView style={styles.vendorInfo}>
              <RTLIcon name="storefront-outline" size={16} color="#666" />
              <RTLText style={styles.vendorName}>
                {vendor?.businessName || t('search.unknownVendor')}
              </RTLText>
            </RTLView>
          </RTLView>

          <RTLView style={styles.orderItems}>
            <RTLText style={styles.itemsLabel}>
              {t('admin.itemsCount', { count: item.items.length })}
            </RTLText>
            <RTLView style={styles.itemsList}>
              {item.items.slice(0, 2).map((orderItem, index) => (
                <RTLText key={index} style={styles.itemText} numberOfLines={1}>
                  {orderItem.quantity}x {orderItem.productName}
                </RTLText>
              ))}
              {item.items.length > 2 && (
                <RTLText style={styles.moreItemsText}>
                  {t('admin.moreItems', { count: item.items.length - 2 })}
                </RTLText>
              )}
            </RTLView>
          </RTLView>

          <RTLView style={styles.orderFooter}>
            <RTLView style={styles.totalContainer}>
              <RTLText style={styles.totalLabel}>{t('admin.total')}:</RTLText>
              <RTLText style={styles.totalAmount}>
                {formatCurrency(item.totalAmount)}
              </RTLText>
            </RTLView>

            <RTLTouchableOpacity style={styles.actionButton}>
              <RTLIcon name="chevron-forward" size={20} color="#666" />
            </RTLTouchableOpacity>
          </RTLView>
        </RTLTouchableOpacity>
      </Card>
    );
  };

  const renderHeader = () => (
    <RTLView style={styles.header}>
      {/* Search Bar */}
      <RTLView style={styles.searchContainer}>
        <RTLIcon name="search-outline" size={20} color="#999" style={styles.searchIcon} />
        <RTLInput
          style={styles.searchInput}
          placeholder={t('admin.searchOrdersCustomers')}
          value={searchQuery}
          onChangeText={setSearchQuery}
          placeholderTextColor="#999"
        />
        {searchQuery.length > 0 && (
          <RTLTouchableOpacity onPress={() => setSearchQuery('')}>
            <RTLIcon name="close-circle" size={20} color="#999" />
          </RTLTouchableOpacity>
        )}
      </RTLView>

      {/* Filters and Sort */}
      <RTLView style={styles.controlsContainer}>
        {/* Status Filter */}
        <RTLView style={styles.filterSection}>
          <RTLText style={styles.filterLabel}>{t('admin.status')}:</RTLText>
          <RTLView style={styles.filterChips}>
            <RTLTouchableOpacity
              style={[styles.filterChip, !selectedStatus && styles.filterChipActive]}
              onPress={() => setSelectedStatus(null)}
            >
              <RTLText style={[styles.filterChipText, !selectedStatus && styles.filterChipTextActive].filter(Boolean) as any}>
                {t('common.all')}
              </RTLText>
            </RTLTouchableOpacity>

            {statuses.map(status => (
              <RTLTouchableOpacity
                key={status}
                style={[styles.filterChip, selectedStatus === status && styles.filterChipActive]}
                onPress={() => setSelectedStatus(selectedStatus === status ? null : status)}
              >
                <RTLText style={[styles.filterChipText, selectedStatus === status && styles.filterChipTextActive].filter(Boolean) as any}>
                  {status}
                </RTLText>
              </RTLTouchableOpacity>
            ))}
          </RTLView>
        </RTLView>

        {/* Sort Options */}
        <RTLView style={styles.sortSection}>
          <RTLText style={styles.filterLabel}>{t('admin.sortBy')}:</RTLText>
          <RTLView style={styles.filterChips}>
            {[
              { key: 'date', label: t('common.date') },
              { key: 'amount', label: t('common.amount') },
              { key: 'status', label: t('common.status') },
            ].map(option => (
              <RTLTouchableOpacity
                key={option.key}
                style={[styles.filterChip, sortBy === option.key && styles.filterChipActive]}
                onPress={() => setSortBy(option.key as any)}
              >
                <RTLText style={[styles.filterChipText, sortBy === option.key && styles.filterChipTextActive].filter(Boolean) as any}>
                  {option.label}
                </RTLText>
              </RTLTouchableOpacity>
            ))}
          </RTLView>
        </RTLView>
      </RTLView>

      {/* Results Count */}
      <RTLView style={styles.resultsContainer}>
        <RTLText style={styles.resultsText}>
          {t('admin.ordersFound', { count: filteredOrders.length })}
        </RTLText>

        {(searchQuery || selectedStatus) && (
          <RTLTouchableOpacity
            style={styles.clearFiltersButton}
            onPress={() => {
              setSearchQuery('');
              setSelectedStatus(null);
            }}
          >
            <RTLText style={styles.clearFiltersText}>{t('admin.clearFilters')}</RTLText>
          </RTLTouchableOpacity>
        )}
      </RTLView>
    </RTLView>
  );

  return (
    <RTLSafeAreaView style={styles.container}>
      <RTLFlatList
        data={filteredOrders}
        renderItem={renderOrderItem}
        keyExtractor={(item) => item.id}
        ListHeaderComponent={renderHeader}
        ListEmptyComponent={
          <EmptyState
            icon="receipt-outline"
            title={t('orders.noOrdersFound')}
            description={
              searchQuery || selectedStatus
                ? t('admin.adjustSearchFilters')
                : t('admin.noOrdersPlaced')
            }
          />
        }
        contentContainerStyle={styles.listContent}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      />
    </RTLSafeAreaView>
  );
};

const createStyles = (colors: ThemeColors) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    listContent: {
      padding: SPACING.lg,
      paddingBottom: SPACING.xl,
    },
    header: {
      marginBottom: SPACING.lg,
    },
    searchContainer: {
      flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
      alignItems: 'center',
      backgroundColor: colors.surface,
      borderRadius: BORDER_RADIUS.md,
      paddingHorizontal: SPACING.md,
      paddingVertical: SPACING.sm,
      marginBottom: SPACING.md,
      borderWidth: 1,
      borderColor: colors.border,
    },
    searchIcon: {
      marginRight: SPACING.sm,
    },
    searchInput: {
      flex: 1,
      fontSize: FONT_SIZES.md,
      color: colors.textPrimary,
    },
    controlsContainer: {
      marginBottom: SPACING.md,
    },
    filterSection: {
      marginBottom: SPACING.md,
    },
    sortSection: {
      marginBottom: SPACING.md,
    },
    filterLabel: {
      fontSize: FONT_SIZES.sm,
      fontWeight: FONT_WEIGHTS.semibold,
      color: colors.textPrimary,
      marginBottom: SPACING.sm,
    },
    filterChips: {
      flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
      flexWrap: 'wrap',
      gap: SPACING.sm,
    },
    filterChip: {
      paddingHorizontal: SPACING.md,
      paddingVertical: SPACING.sm,
      borderRadius: BORDER_RADIUS.full,
      backgroundColor: colors.surface,
      borderWidth: 1,
      borderColor: colors.border,
    },
    filterChipActive: {
      backgroundColor: colors.primary,
      borderColor: colors.primary,
    },
    filterChipText: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
      textTransform: 'capitalize',
    },
    filterChipTextActive: {
      color: '#FFFFFF',
      fontWeight: FONT_WEIGHTS.semibold,
    },
    resultsContainer: {
      flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    resultsText: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
    },
    clearFiltersButton: {
      paddingHorizontal: SPACING.md,
      paddingVertical: SPACING.sm,
    },
    clearFiltersText: {
      fontSize: FONT_SIZES.sm,
      color: colors.primary,
      fontWeight: FONT_WEIGHTS.medium,
    },
    orderCard: {
      marginBottom: SPACING.md,
    },
    orderContent: {
      padding: SPACING.md,
    },
    orderHeader: {
      flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
      justifyContent: 'space-between',
      alignItems: 'flex-start',
      marginBottom: SPACING.md,
    },
    orderInfo: {
      flex: 1,
    },
    orderId: {
      fontSize: FONT_SIZES.md,
      fontWeight: FONT_WEIGHTS.semibold,
      color: colors.textPrimary,
      marginBottom: SPACING.xs,
    },
    orderDate: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
    },
    orderDetails: {
      marginBottom: SPACING.md,
    },
    customerInfo: {
      flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
      alignItems: 'center',
      marginBottom: SPACING.sm,
    },
    customerName: {
      fontSize: FONT_SIZES.sm,
      color: colors.textPrimary,
      marginLeft: SPACING.sm,
      fontWeight: FONT_WEIGHTS.medium,
    },
    vendorInfo: {
      flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
      alignItems: 'center',
    },
    vendorName: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
      marginLeft: SPACING.sm,
    },
    orderItems: {
      marginBottom: SPACING.md,
    },
    itemsLabel: {
      fontSize: FONT_SIZES.sm,
      fontWeight: FONT_WEIGHTS.semibold,
      color: colors.textPrimary,
      marginBottom: SPACING.sm,
    },
    itemsList: {
      gap: SPACING.xs,
    },
    itemText: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
    },
    moreItemsText: {
      fontSize: FONT_SIZES.sm,
      color: colors.primary,
      fontStyle: 'italic',
    },
    orderFooter: {
      flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    totalContainer: {
      flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
      alignItems: 'center',
    },
    totalLabel: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
      marginRight: SPACING.sm,
    },
    totalAmount: {
      fontSize: FONT_SIZES.lg,
      fontWeight: FONT_WEIGHTS.bold,
      color: colors.textPrimary,
    },
    actionButton: {
      padding: SPACING.sm,
    },
  });
