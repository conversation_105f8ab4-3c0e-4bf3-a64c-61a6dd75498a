#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Configuration
const config = {
  srcDir: path.join(__dirname, '../src'),
  outputFile: path.join(__dirname, '../IMPORT_PATHS_FIXES_REPORT.md'),
  excludePatterns: [
    /node_modules/,
    /\.git/,
    /\.expo/,
    /dist/,
    /build/,
    /coverage/,
    /\.DS_Store/,
    /\.env/,
    /package-lock\.json/,
    /yarn\.lock/,
    /\.log$/,
    /\.md$/,
    /\.json$/,
    /\.js\.map$/,
    /\.d\.ts$/,
    /scripts/,
    /assets/,
    /RTL\//,  // Exclude RTL directory itself
  ],
  fileExtensions: ['.tsx', '.ts', '.jsx', '.js'],
  
  // Import path fixes
  importFixes: [
    {
      name: 'wrongRTLPath',
      pattern: /from\s+['"]\.\.\/components\/RTL['"]/g,
      replacement: "from '../RTL'",
      description: 'Fixed incorrect RTL import path'
    },
    {
      name: 'wrongRTLPathDeep',
      pattern: /from\s+['"]\.\.\/\.\.\/components\/RTL['"]/g,
      replacement: "from '../RTL'",
      description: 'Fixed deep RTL import path'
    },
    {
      name: 'wrongRTLPathShallow',
      pattern: /from\s+['"]\.\/RTL['"]/g,
      replacement: "from './RTL'",
      description: 'Fixed shallow RTL import path'
    },
    {
      name: 'navigationRTLPath',
      pattern: /from\s+['"]\.\.\/RTL['"]/g,
      replacement: "from '../components/RTL'",
      description: 'Fixed navigation RTL import path'
    },
    {
      name: 'duplicateTypeKeyword',
      pattern: /import\s+\{\s*type\s+\{\s*([^}]+)\s*\}\s*,/g,
      replacement: "import { type $1,",
      description: 'Fixed duplicate type keyword in import'
    }
  ]
};

class ImportPathsFixer {
  constructor() {
    this.results = {
      filesProcessed: 0,
      filesFixed: 0,
      totalFixes: 0,
      fixesByType: {},
      fixedFiles: []
    };
  }

  scanDirectory(dirPath) {
    const items = fs.readdirSync(dirPath);
    
    for (const item of items) {
      const fullPath = path.join(dirPath, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        // Skip excluded directories
        if (config.excludePatterns.some(pattern => pattern.test(fullPath))) {
          continue;
        }
        this.scanDirectory(fullPath);
      } else if (stat.isFile()) {
        // Check if file should be processed
        if (this.shouldProcessFile(fullPath)) {
          this.processFile(fullPath);
        }
      }
    }
  }

  shouldProcessFile(filePath) {
    // Check file extension
    const ext = path.extname(filePath);
    if (!config.fileExtensions.includes(ext)) {
      return false;
    }
    
    // Check exclude patterns
    if (config.excludePatterns.some(pattern => pattern.test(filePath))) {
      return false;
    }
    
    return true;
  }

  processFile(filePath) {
    this.results.filesProcessed++;
    
    try {
      let content = fs.readFileSync(filePath, 'utf8');
      let originalContent = content;
      let fileFixed = false;
      let fileFixes = [];

      // Apply each fix pattern
      config.importFixes.forEach(fix => {
        const matches = (content.match(fix.pattern) || []).length;
        
        if (matches > 0) {
          content = content.replace(fix.pattern, fix.replacement);
          fileFixed = true;
          fileFixes.push({
            type: fix.name,
            count: matches,
            description: fix.description
          });
          
          if (!this.results.fixesByType[fix.name]) {
            this.results.fixesByType[fix.name] = 0;
          }
          this.results.fixesByType[fix.name] += matches;
          this.results.totalFixes += matches;
        }
      });

      // Write file if changes were made
      if (fileFixed && content !== originalContent) {
        fs.writeFileSync(filePath, content, 'utf8');
        this.results.filesFixed++;
        this.results.fixedFiles.push({
          path: path.relative(process.cwd(), filePath),
          fixes: fileFixes,
          totalFixes: fileFixes.reduce((sum, fix) => sum + fix.count, 0)
        });
        
        console.log(`✅ Fixed: ${path.relative(process.cwd(), filePath)} (${fileFixes.reduce((sum, fix) => sum + fix.count, 0)} fixes)`);
      }
    } catch (error) {
      console.error(`❌ Error processing ${filePath}:`, error.message);
    }
  }

  generateReport() {
    const report = [
      '# Import Paths Fixes Report',
      '',
      `**Generated:** ${new Date().toLocaleString()}`,
      `**Files Processed:** ${this.results.filesProcessed}`,
      `**Files Fixed:** ${this.results.filesFixed}`,
      `**Total Fixes Applied:** ${this.results.totalFixes}`,
      '',
      '## 📊 Fixes Summary',
      '',
      '| Fix Type | Count | Description |',
      '|----------|-------|-------------|'
    ];

    // Add fixes summary
    Object.entries(this.results.fixesByType).forEach(([type, count]) => {
      const fix = config.importFixes.find(f => f.name === type);
      const description = fix ? fix.description : type;
      report.push(`| ${type} | ${count} | ${description} |`);
    });

    if (this.results.fixedFiles.length > 0) {
      report.push('', '## 📁 Fixed Files', '');
      
      this.results.fixedFiles.forEach(file => {
        report.push(`### ${file.path}`, '');
        report.push(`**Total fixes:** ${file.totalFixes}`, '');
        
        file.fixes.forEach(fix => {
          report.push(`- **${fix.type}** (${fix.count} occurrences): ${fix.description}`);
        });
        
        report.push('');
      });
    }

    // Add common import patterns
    report.push(
      '## 📋 Correct Import Patterns',
      '',
      '### RTL Components Import',
      '',
      '```typescript',
      '// ✅ Correct - from components directory',
      "import { RTLView, RTLText, RTLIcon } from './RTL';",
      '',
      '// ✅ Correct - from subdirectory',
      "import { RTLView, RTLText, RTLIcon } from '../RTL';",
      '',
      '// ❌ Wrong - incorrect path',
      "import { RTLView, RTLText, RTLIcon } from '../components/RTL';",
      '```',
      '',
      '### File Structure Reference',
      '',
      '```',
      'src/',
      '├── components/',
      '│   ├── RTL/',
      '│   │   ├── index.ts',
      '│   │   ├── RTLView.tsx',
      '│   │   ├── RTLText.tsx',
      '│   │   └── ...',
      '│   ├── ui/',
      '│   │   └── Component.tsx  // import from "../RTL"',
      '│   └── Component.tsx      // import from "./RTL"',
      '└── screens/',
      '    └── Screen.tsx         // import from "../../components/RTL"',
      '```'
    );

    return report.join('\n');
  }

  run() {
    console.log('🔍 Scanning for incorrect import paths...');
    
    // Scan source directory
    if (fs.existsSync(config.srcDir)) {
      this.scanDirectory(config.srcDir);
    }

    // Generate and save report
    const report = this.generateReport();
    fs.writeFileSync(config.outputFile, report, 'utf8');
    
    console.log('\n📊 Summary:');
    console.log(`Files processed: ${this.results.filesProcessed}`);
    console.log(`Files fixed: ${this.results.filesFixed}`);
    console.log(`Total fixes: ${this.results.totalFixes}`);
    console.log(`Report saved: ${config.outputFile}`);
    
    if (this.results.totalFixes > 0) {
      console.log('\n✅ Import paths have been fixed!');
    } else {
      console.log('\n✅ No incorrect import paths found.');
    }
  }
}

// Run the fixer
const fixer = new ImportPathsFixer();
fixer.run();
