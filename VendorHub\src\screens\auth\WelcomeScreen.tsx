import React from 'react';
import { StyleSheet, Image } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useThemedStyles, useI18n } from '../../hooks';
import { <PERSON><PERSON>, Card, HeroLogo, WelcomeBackground } from '../../components';
import { RTLView, RTLText, RTLScrollView, RTLSafeAreaView } from '../../components/RTL';
import {
  SPACING,
  FONT_SIZES,
  FONT_WEIGHTS,
  GRADIENTS,
  SCREEN } from '../../constants/theme';
import { APP_NAME, USER_ROLES, type UserRole  } from '../../constants';
import type ThemeColors  from '../../contexts/ThemeContext';

interface WelcomeScreenProps {
  navigation: any;
}

export const WelcomeScreen: React.FC<WelcomeScreenProps> = ({ navigation }) => {
  const styles = useThemedStyles(createStyles);
  const { t } = useI18n();

  const handleRoleSelection = (role: UserRole) => {
    navigation.navigate('Login', { role });
  };

  const handleRegister = () => {
    navigation.navigate('Register');
  };

  return (
    <WelcomeBackground>
      <RTLSafeAreaView style={styles.safeArea}>
        <RTLScrollView
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {/* Header */}
          <RTLView style={styles.header}>
            <RTLView style={styles.logoContainer}>
              <HeroLogo />
            </RTLView>
            <RTLText style={styles.tagline}>
              {t('auth.welcome')}
            </RTLText>
            <RTLText style={styles.description}>
              {t('auth.welcomeDescription')}
            </RTLText>
          </RTLView>

          {/* Role Selection Cards */}
          <RTLView style={styles.rolesContainer}>
            <RTLText style={styles.sectionTitle}>{t('auth.chooseRole')}</RTLText>

            <Card
              style={styles.roleCard}
              variant="glass"
              onPress={() => handleRoleSelection(USER_ROLES.ADMIN)}
            >
              <RTLView style={styles.roleContent}>
                <RTLText style={styles.roleIcon}>👑</RTLText>
                <RTLText style={styles.roleTitle}>{t('auth.admin')}</RTLText>
                <RTLText style={styles.roleDescription}>
                  {t('auth.adminDescription')}
                </RTLText>
              </RTLView>
            </Card>

            <Card
              style={styles.roleCard}
              variant="glass"
              onPress={() => handleRoleSelection(USER_ROLES.VENDOR)}
            >
              <RTLView style={styles.roleContent}>
                <RTLText style={styles.roleIcon}>🏪</RTLText>
                <RTLText style={styles.roleTitle}>{t('auth.vendor')}</RTLText>
                <RTLText style={styles.roleDescription}>
                  {t('auth.vendorDescription')}
                </RTLText>
              </RTLView>
            </Card>

            <Card
              style={styles.roleCard}
              variant="glass"
              onPress={() => handleRoleSelection(USER_ROLES.CUSTOMER)}
            >
              <RTLView style={styles.roleContent}>
                <RTLText style={styles.roleIcon}>🛍️</RTLText>
                <RTLText style={styles.roleTitle}>{t('auth.customer')}</RTLText>
                <RTLText style={styles.roleDescription}>
                  {t('auth.customerDescription')}
                </RTLText>
              </RTLView>
            </Card>
          </RTLView>

          {/* Actions */}
          <RTLView style={styles.actionsContainer}>
            <Button
              title={t('auth.newToVendorHub')}
              onPress={handleRegister}
              variant="ghost"
              style={styles.registerButton}
              textStyle={styles.registerButtonText}
            />
          </RTLView>
        </RTLScrollView>
      </RTLSafeAreaView>
    </WelcomeBackground>
  );
};

const createStyles = (colors: ThemeColors) =>
  StyleSheet.create({
    container: {
      flex: 1,
    },
    safeArea: {
      flex: 1,
    },
    scrollContent: {
      flexGrow: 1,
      paddingHorizontal: SPACING.lg,
    },
    header: {
      alignItems: 'center',
      paddingTop: SPACING.xxl,
      paddingBottom: SPACING.xl,
    },
    logoContainer: {
      marginBottom: SPACING.md,
    },
    logoText: {
      fontSize: FONT_SIZES.display,
      fontWeight: FONT_WEIGHTS.bold,
      color: colors.textOnPrimary,
      textAlign: 'center',
    },
    tagline: {
      fontSize: FONT_SIZES.lg,
      fontWeight: FONT_WEIGHTS.medium,
      color: colors.textOnPrimary,
      textAlign: 'center',
      marginBottom: SPACING.sm,
      opacity: 0.9,
    },
    description: {
      fontSize: FONT_SIZES.md,
      color: colors.textOnPrimary,
      textAlign: 'center',
      opacity: 0.8,
      lineHeight: FONT_SIZES.md * 1.4,
    },
    rolesContainer: {
      flex: 1,
      paddingVertical: SPACING.lg,
    },
    sectionTitle: {
      fontSize: FONT_SIZES.xl,
      fontWeight: FONT_WEIGHTS.semibold,
      color: colors.textOnPrimary,
      textAlign: 'center',
      marginBottom: SPACING.lg,
    },
    roleCard: {
      marginBottom: SPACING.md,
    },
    roleContent: {
      alignItems: 'center',
      paddingVertical: SPACING.md,
    },
    roleIcon: {
      fontSize: 40,
      marginBottom: SPACING.sm,
    },
    roleTitle: {
      fontSize: FONT_SIZES.lg,
      fontWeight: FONT_WEIGHTS.semibold,
      color: colors.textOnPrimary,
      marginBottom: SPACING.xs,
    },
    roleDescription: {
      fontSize: FONT_SIZES.sm,
      color: colors.textOnPrimary,
      textAlign: 'center',
      opacity: 0.8,
      lineHeight: FONT_SIZES.sm * 1.4,
    },
    actionsContainer: {
      paddingVertical: SPACING.lg,
    },
    registerButton: {
      backgroundColor: 'transparent',
      borderWidth: 1,
      borderColor: colors.textOnPrimary,
    },
    registerButtonText: {
      color: colors.textOnPrimary,
    },
  });
