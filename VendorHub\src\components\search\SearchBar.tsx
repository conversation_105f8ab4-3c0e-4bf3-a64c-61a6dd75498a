import React, { useState, useRef, useEffect } from 'react';
import {
  StyleSheet,
  Animated,
  Dimensions } from 'react-native';
import { useThemedStyles, useProducts, useVendors, useDebounce, useI18n } from '../../hooks';
import { Input } from '../Input';
import { Card } from '../Card';
import { RTLView, RTLText, RTLIcon, RTLTouchableOpacity, RTLFlatList } from '../RTL';
import {
  SPACING,
  FONT_SIZES,
  FONT_WEIGHTS,
  BORDER_RADIUS } from '../../constants/theme';
import { formatCurrency } from '../../utils';
import type ThemeColors  from '../../contexts/ThemeContext';
import type { Product, Vendor } from '../../contexts/DataContext';

const { width: screenWidth } = Dimensions.get('window');

export interface SearchResult {
  type: 'product' | 'vendor' | 'category';
  id: string;
  title: string;
  subtitle?: string;
  price?: number;
  image?: string;
  data: Product | Vendor | { name: string; count: number };
}

export interface SearchBarProps {
  placeholder?: string;
  onResultPress?: (result: SearchResult) => void;
  onSearchChange?: (query: string) => void;
  showSuggestions?: boolean;
  maxSuggestions?: number;
  style?: any;
}

export const SearchBar: React.FC<SearchBarProps> = ({
  placeholder = "Search products, vendors, categories...",
  onResultPress,
  onSearchChange,
  showSuggestions = true,
  maxSuggestions = 8,
  style,
}) => {
  const styles = useThemedStyles(createStyles);
  const { getAllProducts } = useProducts();
  const { vendors } = useVendors();
  const { t } = useI18n();
  
  const [query, setQuery] = useState('');
  const [isFocused, setIsFocused] = useState(false);
  const [suggestions, setSuggestions] = useState<SearchResult[]>([]);
  const [recentSearches, setRecentSearches] = useState<string[]>([]);
  
  const debouncedQuery = useDebounce(query, 300);
  const animatedHeight = useRef(new Animated.Value(0)).current;
  const inputRef = useRef<any>(null);

  const allProducts = getAllProducts();
  const allVendors = vendors;

  // Get categories with product counts
  const categories = React.useMemo(() => {
    const categoryMap = new Map<string, number>();
    allProducts.forEach(product => {
      if (product.isActive) {
        categoryMap.set(product.category, (categoryMap.get(product.category) || 0) + 1);
      }
    });
    return Array.from(categoryMap.entries()).map(([name, count]) => ({ name, count }));
  }, [allProducts]);

  useEffect(() => {
    if (debouncedQuery.length > 0) {
      generateSuggestions(debouncedQuery);
    } else {
      setSuggestions([]);
    }
    onSearchChange?.(debouncedQuery);
  }, [debouncedQuery]);

  useEffect(() => {
    const shouldShow = isFocused && (suggestions.length > 0 || recentSearches.length > 0);
    Animated.timing(animatedHeight, {
      toValue: shouldShow ? 1 : 0,
      duration: 200,
      useNativeDriver: false,
    }).start();
  }, [isFocused, suggestions.length, recentSearches.length]);

  const generateSuggestions = (searchQuery: string) => {
    const query = searchQuery.toLowerCase().trim();
    if (!query) {
      setSuggestions([]);
      return;
    }

    const results: SearchResult[] = [];

    // Search products
    const matchingProducts = allProducts
      .filter(product => 
        product.isActive && (
          product.name.toLowerCase().includes(query) ||
          product.description.toLowerCase().includes(query) ||
          product.tags.some(tag => tag.toLowerCase().includes(query)) ||
          product.category.toLowerCase().includes(query)
        )
      )
      .slice(0, 4)
      .map(product => ({
        type: 'product' as const,
        id: product.id,
        title: product.name,
        subtitle: product.category,
        price: product.price,
        image: product.images[0],
        data: product,
      }));

    results.push(...matchingProducts);

    // Search vendors
    const matchingVendors = allVendors
      .filter((vendor: any) =>
        vendor.status === 'approved' && (
          vendor.businessName.toLowerCase().includes(query) ||
          vendor.businessDescription.toLowerCase().includes(query) ||
          vendor.address?.city?.toLowerCase().includes(query)
        )
      )
      .slice(0, 2)
      .map((vendor: any) => ({
        type: 'vendor' as const,
        id: vendor.id,
        title: vendor.businessName,
        subtitle: `${vendor.address.city}, ${vendor.address.state}`,
        data: vendor,
      }));

    results.push(...matchingVendors);

    // Search categories
    const matchingCategories = categories
      .filter(category => category.name.toLowerCase().includes(query))
      .slice(0, 2)
      .map(category => ({
        type: 'category' as const,
        id: category.name,
        title: category.name,
        subtitle: `${category.count} products`,
        data: category,
      }));

    results.push(...matchingCategories);

    setSuggestions(results.slice(0, maxSuggestions));
  };

  const handleQueryChange = (text: string) => {
    setQuery(text);
  };

  const handleFocus = () => {
    setIsFocused(true);
  };

  const handleBlur = () => {
    // Delay blur to allow suggestion tap
    setTimeout(() => setIsFocused(false), 150);
  };

  const handleSuggestionPress = (result: SearchResult) => {
    setQuery(result.title);
    setIsFocused(false);
    
    // Add to recent searches
    const newRecentSearches = [result.title, ...recentSearches.filter(s => s !== result.title)].slice(0, 5);
    setRecentSearches(newRecentSearches);
    
    onResultPress?.(result);
    inputRef.current?.blur();
  };

  const handleRecentSearchPress = (searchTerm: string) => {
    setQuery(searchTerm);
    setIsFocused(false);
    onSearchChange?.(searchTerm);
    inputRef.current?.blur();
  };

  const clearRecentSearches = () => {
    setRecentSearches([]);
  };

  const getResultIcon = (type: SearchResult['type']) => {
    switch (type) {
      case 'product':
        return 'cube-outline';
      case 'vendor':
        return 'storefront-outline';
      case 'category':
        return 'grid-outline';
      default:
        return 'search-outline';
    }
  };

  const renderSuggestion = ({ item }: { item: SearchResult }) => (
    <RTLTouchableOpacity
      style={styles.suggestionItem}
      onPress={() => handleSuggestionPress(item)}
    >
      <RTLView style={styles.suggestionIcon}>
        <RTLIcon name={getResultIcon(item.type)} size={20} color="#667eea" />
      </RTLView>
      <RTLView style={styles.suggestionContent}>
        <RTLText style={styles.suggestionTitle} numberOfLines={1}>
          {item.title}
        </RTLText>
        {item.subtitle && (
          <RTLText style={styles.suggestionSubtitle} numberOfLines={1}>
            {item.subtitle}
          </RTLText>
        )}
      </RTLView>
      {item.price && (
        <RTLText style={styles.suggestionPrice}>
          {formatCurrency(item.price)}
        </RTLText>
      )}
      <RTLIcon name="arrow-up-outline" size={16} color="#CCCCCC" style={styles.suggestionArrow} />
    </RTLTouchableOpacity>
  );

  const renderRecentSearch = ({ item }: { item: string }) => (
    <RTLTouchableOpacity
      style={styles.recentItem}
      onPress={() => handleRecentSearchPress(item)}
    >
      <RTLIcon name="time-outline" size={16} color="#CCCCCC" />
      <RTLText style={styles.recentText}>{item}</RTLText>
    </RTLTouchableOpacity>
  );

  const renderSuggestions = () => {
    if (!isFocused) return null;

    const showRecent = query.length === 0 && recentSearches.length > 0;
    const showSuggestions = query.length > 0 && suggestions.length > 0;

    if (!showRecent && !showSuggestions) return null;

    return (
      <Animated.View
        style={[
          styles.suggestionsContainer,
          {
            maxHeight: animatedHeight.interpolate({
              inputRange: [0, 1],
              outputRange: [0, 300],
            }),
            opacity: animatedHeight,
          },
        ]}
      >
        <Card style={styles.suggestionsCard} variant="elevated">
          {showRecent && (
            <>
              <RTLView style={styles.sectionHeader}>
                <RTLText style={styles.sectionTitle}>{t('search.recentSearches')}</RTLText>
                <RTLTouchableOpacity onPress={clearRecentSearches}>
                  <RTLText style={styles.clearText}>{t('common.clear')}</RTLText>
                </RTLTouchableOpacity>
              </RTLView>
              <RTLFlatList
                data={recentSearches}
                renderItem={renderRecentSearch}
                keyExtractor={(item, index) => `recent-${index}`}
                scrollEnabled={false}
              />
            </>
          )}

          {showSuggestions && (
            <>
              {showRecent && <RTLView style={styles.separator} />}
              <RTLView style={styles.sectionHeader}>
                <RTLText style={styles.sectionTitle}>{t('search.suggestions')}</RTLText>
              </RTLView>
              <RTLFlatList
                data={suggestions}
                renderItem={renderSuggestion}
                keyExtractor={(item) => `${item.type}-${item.id}`}
                scrollEnabled={false}
              />
            </>
          )}
        </Card>
      </Animated.View>
    );
  };

  return (
    <RTLView style={[styles.container, style]}>
      <Input

        placeholder={placeholder}
        value={query}
        onChangeText={handleQueryChange}
        onFocus={handleFocus}
        onBlur={handleBlur}
        leftIcon="search-outline"
        rightIcon={query.length > 0 ? "close-circle" : undefined}
        onRightIconPress={() => setQuery('')}
        style={styles.searchInput}
      />

      {showSuggestions && renderSuggestions()}
    </RTLView>
  );
};

const createStyles = (colors: ThemeColors) =>
  StyleSheet.create({
    container: {
      position: 'relative',
      zIndex: 1000,
    },
    searchInput: {
      marginBottom: 0,
    },
    suggestionsContainer: {
      position: 'absolute',
      top: '100%',
      left: 0,
      right: 0,
      zIndex: 1001,
      overflow: 'hidden',
    },
    suggestionsCard: {
      marginTop: SPACING.xs,
      maxHeight: 300,
    },
    sectionHeader: {
      flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingVertical: SPACING.sm,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    sectionTitle: {
      fontSize: FONT_SIZES.sm,
      fontWeight: FONT_WEIGHTS.semibold,
      color: colors.textPrimary,
    },
    clearText: {
      fontSize: FONT_SIZES.sm,
      color: colors.primary,
      fontWeight: FONT_WEIGHTS.medium,
    },
    separator: {
      height: 1,
      backgroundColor: colors.border,
      marginVertical: SPACING.xs,
    },
    suggestionItem: {
      flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
      alignItems: 'center',
      paddingVertical: SPACING.sm,
      paddingHorizontal: SPACING.xs,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    suggestionIcon: {
      width: 32,
      height: 32,
      borderRadius: 16,
      backgroundColor: colors.surface,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: SPACING.sm,
    },
    suggestionContent: {
      flex: 1,
      marginRight: SPACING.sm,
    },
    suggestionTitle: {
      fontSize: FONT_SIZES.md,
      fontWeight: FONT_WEIGHTS.medium,
      color: colors.textPrimary,
      marginBottom: SPACING.xs,
    },
    suggestionSubtitle: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
    },
    suggestionPrice: {
      fontSize: FONT_SIZES.md,
      fontWeight: FONT_WEIGHTS.bold,
      color: '#667eea',
      marginRight: SPACING.sm,
    },
    suggestionArrow: {
      transform: [{ rotate: '45deg' }],
    },
    recentItem: {
      flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
      alignItems: 'center',
      paddingVertical: SPACING.sm,
      paddingHorizontal: SPACING.xs,
    },
    recentText: {
      fontSize: FONT_SIZES.md,
      color: colors.textSecondary,
      marginLeft: SPACING.sm,
    },
  });
