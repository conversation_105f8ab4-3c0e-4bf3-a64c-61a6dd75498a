import React from 'react';
import {
  StyleSheet } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useThemedStyles, useAuth } from '../hooks';
import { RTLView, RTLText, RTLIcon, RTLTouchableOpacity } from './RTL';
import ChatService from '../services/ChatService';
import {
  SPACING,
  FONT_SIZES,
  FONT_WEIGHTS,
  BORDER_RADIUS } from '../constants/theme';
import type ThemeColors  from '../contexts/ThemeContext';

interface ChatButtonProps {
  targetUserId: string;
  targetUserName: string;
  targetUserAvatar?: string;
  style?: any;
  variant?: 'primary' | 'secondary' | 'icon';
  size?: 'small' | 'medium' | 'large';
  disabled?: boolean;
  onPress?: () => void;
}

export const ChatButton: React.FC<ChatButtonProps> = ({
  targetUserId,
  targetUserName,
  targetUserAvatar,
  style,
  variant = 'primary',
  size = 'medium',
  disabled = false,
  onPress,
}) => {
  const styles = useThemedStyles(createStyles);
  const navigation = useNavigation();
  const { user } = useAuth();

  const handlePress = async () => {
    if (disabled || !user) return;

    try {
      // Custom onPress handler takes precedence
      if (onPress) {
        onPress();
        return;
      }

      // Connect to chat service if not already connected
      if (!ChatService.getConnectionStatus()) {
        await ChatService.connect(user.id);
      }

      // Navigate to chat screen
      (navigation as any).navigate('Chat', {
        otherUserId: targetUserId,
        otherUserName: targetUserName,
        otherUserAvatar: targetUserAvatar,
      });
    } catch (error) {
      console.error('Error opening chat:', error);
    }
  };

  const getButtonStyle = () => {
    return [
      styles.button,
      variant === 'primary' && styles.primaryButton,
      variant === 'secondary' && styles.secondaryButton,
      variant === 'icon' && styles.iconButton,
      size === 'small' && styles.smallButton,
      size === 'medium' && styles.mediumButton,
      size === 'large' && styles.largeButton,
      disabled && styles.disabledButton,
    ].filter(Boolean);
  };

  const getTextStyle = () => {
    return [
      styles.buttonText,
      variant === 'primary' && styles.primaryButtonText,
      variant === 'secondary' && styles.secondaryButtonText,
      size === 'small' && styles.smallButtonText,
      size === 'medium' && styles.mediumButtonText,
      size === 'large' && styles.largeButtonText,
      disabled && styles.disabledButtonText,
    ].filter(Boolean);
  };

  const getIconSize = () => {
    switch (size) {
      case 'small': return 16;
      case 'medium': return 20;
      case 'large': return 24;
      default: return 20;
    }
  };

  const getIconColor = () => {
    if (disabled) return '#999';
    
    switch (variant) {
      case 'primary': return '#FFFFFF';
      case 'secondary': return '#667eea';
      case 'icon': return '#667eea';
      default: return '#FFFFFF';
    }
  };

  const renderContent = () => {
    if (variant === 'icon') {
      return (
        <RTLIcon
          name="chatbubble-outline"
          size={getIconSize()}
          color={getIconColor()}
        />
      );
    }

    return (
      <RTLView style={styles.buttonContent}>
        <RTLIcon
          name="chatbubble-outline"
          size={getIconSize()}
          color={getIconColor()}
        />
        <RTLText style={getTextStyle()}>
          {size === 'small' ? 'Chat' : 'Message'}
        </RTLText>
      </RTLView>
    );
  };

  // Don't show chat button if user is trying to chat with themselves
  if (user?.id === targetUserId) {
    return null;
  }

  return (
    <RTLTouchableOpacity
      style={[getButtonStyle(), style]}
      onPress={handlePress}
      disabled={disabled}
      activeOpacity={0.7}
    >
      {renderContent()}
    </RTLTouchableOpacity>
  );
};

const createStyles = (colors: ThemeColors) => StyleSheet.create({
  button: {
    borderRadius: BORDER_RADIUS.md,
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
  },
  primaryButton: {
    backgroundColor: colors.primary,
  },
  secondaryButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: colors.primary,
  },
  iconButton: {
    backgroundColor: 'transparent',
    borderRadius: BORDER_RADIUS.full,
    width: 40,
    height: 40,
  },
  smallButton: {
    paddingHorizontal: SPACING.sm,
    paddingVertical: SPACING.xs,
    minHeight: 32,
  },
  mediumButton: {
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    minHeight: 40,
  },
  largeButton: {
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
    minHeight: 48,
  },
  disabledButton: {
    backgroundColor: colors.surface,
    borderColor: colors.border,
  },
  buttonContent: {
    flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
    alignItems: 'center',
    gap: SPACING.xs,
  },
  buttonText: {
    fontWeight: FONT_WEIGHTS.medium,
  },
  primaryButtonText: {
    color: '#FFFFFF',
  },
  secondaryButtonText: {
    color: '#667eea',
  },
  smallButtonText: {
    fontSize: FONT_SIZES.sm,
  },
  mediumButtonText: {
    fontSize: FONT_SIZES.md,
  },
  largeButtonText: {
    fontSize: FONT_SIZES.lg,
  },
  disabledButtonText: {
    color: '#999',
  },
});
