import React, { useRef, useEffect, useState } from 'react';
import { StyleSheet, Platform } from 'react-native';
import { RTLView, RTLText } from '../RTL';
import { useThemedStyles } from '../../hooks';
import { ReanimatedLoader } from './ReanimatedLoader';
import { SPACING } from '../../constants/theme';
import type ThemeColors from '../../contexts/ThemeContext';

// Conditional import for <PERSON><PERSON> to handle web compatibility
let LottieView: any = null;
try {
  LottieView = require('lottie-react-native').default;
} catch (error) {
  console.warn('Lottie React Native not available, using fallback animations');
}

export interface LottieLoaderProps {
  type?: 'loading' | 'success' | 'error' | 'warning' | 'processing' | 'uploading' | 'downloading';
  size?: 'small' | 'medium' | 'large';
  speed?: 'slow' | 'normal' | 'fast';
  loop?: boolean;
  autoPlay?: boolean;
  fallbackType?: 'dots' | 'spinner' | 'pulse' | 'bounce';
  fallbackColor?: string;
  style?: any;
  onAnimationFinish?: () => void;
  customSource?: any; // For custom Lottie files
}

export const LottieLoader: React.FC<LottieLoaderProps> = ({
  type = 'loading',
  size = 'medium',
  speed = 'normal',
  loop = true,
  autoPlay = true,
  fallbackType = 'spinner',
  fallbackColor = '#667eea',
  style,
  onAnimationFinish,
  customSource,
}) => {
  const styles = useThemedStyles(createStyles);
  const animationRef = useRef<LottieView>(null);
  const [useFallback, setUseFallback] = useState(false);
  const [isLoaded, setIsLoaded] = useState(false);

  const getSize = () => {
    switch (size) {
      case 'small':
        return 40;
      case 'large':
        return 120;
      default:
        return 80;
    }
  };

  const getSpeed = () => {
    switch (speed) {
      case 'slow':
        return 0.5;
      case 'fast':
        return 2;
      default:
        return 1;
    }
  };

  // Built-in Lottie animations (using JSON data)
  const getLottieSource = () => {
    if (customSource) return customSource;

    try {
      switch (type) {
        case 'loading':
          return require('./lottie-animations/loading.json');
        case 'success':
          // For now, fallback to loading animation for missing files
          return require('./lottie-animations/loading.json');
        case 'error':
          return require('./lottie-animations/loading.json');
        case 'warning':
          return require('./lottie-animations/loading.json');
        case 'processing':
          return require('./lottie-animations/loading.json');
        case 'uploading':
          return require('./lottie-animations/loading.json');
        case 'downloading':
          return require('./lottie-animations/loading.json');
        default:
          return null;
      }
    } catch (error) {
      console.warn('Failed to load Lottie animation file:', error);
      return null;
    }
  };

  const handleAnimationFailure = (error: any) => {
    console.warn('Lottie animation failed, falling back to Reanimated loader:', error);
    setUseFallback(true);
  };

  const handleAnimationFinish = () => {
    setIsLoaded(true);
    onAnimationFinish?.();
  };

  useEffect(() => {
    // Auto-fallback for web platform, missing LottieView, or if Lottie source is not available
    if (Platform.OS === 'web' || !LottieView || !getLottieSource()) {
      setUseFallback(true);
      return;
    }

    // Reset animation when type changes
    if (animationRef.current && isLoaded) {
      animationRef.current.reset();
      if (autoPlay) {
        animationRef.current.play();
      }
    }
  }, [type, autoPlay, isLoaded]);

  const loaderSize = getSize();

  // Fallback to Reanimated loader
  if (useFallback) {
    return (
      <RTLView style={[styles.container, style]}>
        <ReanimatedLoader
          type={fallbackType}
          size={size}
          color={fallbackColor}
          speed={speed}
        />
        {__DEV__ && (
          <RTLText style={styles.fallbackText}>
            Using fallback animation
          </RTLText>
        )}
      </RTLView>
    );
  }

  // Lottie animation - only render if LottieView is available
  if (!LottieView) {
    return (
      <RTLView style={[styles.container, style]}>
        <ReanimatedLoader
          type={fallbackType}
          size={size}
          color={fallbackColor}
          speed={speed}
        />
        {__DEV__ && (
          <RTLText style={styles.fallbackText}>
            LottieView not available, using fallback
          </RTLText>
        )}
      </RTLView>
    );
  }

  return (
    <RTLView style={[styles.container, style]}>
      <LottieView
        ref={animationRef}
        source={getLottieSource()}
        style={[
          styles.lottie,
          {
            width: loaderSize,
            height: loaderSize,
          },
        ]}
        autoPlay={autoPlay}
        loop={loop}
        speed={getSpeed()}
        onAnimationFinish={handleAnimationFinish}
        onError={handleAnimationFailure}
        resizeMode="contain"
      />
    </RTLView>
  );
};

// Preset Lottie loaders for common use cases
export const LottieLoadingSpinner: React.FC<Omit<LottieLoaderProps, 'type'>> = (props) => (
  <LottieLoader {...props} type="loading" />
);

export const LottieSuccessAnimation: React.FC<Omit<LottieLoaderProps, 'type' | 'loop'>> = (props) => (
  <LottieLoader {...props} type="success" loop={false} />
);

export const LottieErrorAnimation: React.FC<Omit<LottieLoaderProps, 'type' | 'loop'>> = (props) => (
  <LottieLoader {...props} type="error" loop={false} />
);

export const LottieWarningAnimation: React.FC<Omit<LottieLoaderProps, 'type' | 'loop'>> = (props) => (
  <LottieLoader {...props} type="warning" loop={false} />
);

export const LottieProcessingAnimation: React.FC<Omit<LottieLoaderProps, 'type'>> = (props) => (
  <LottieLoader {...props} type="processing" />
);

export const LottieUploadAnimation: React.FC<Omit<LottieLoaderProps, 'type'>> = (props) => (
  <LottieLoader {...props} type="uploading" />
);

export const LottieDownloadAnimation: React.FC<Omit<LottieLoaderProps, 'type'>> = (props) => (
  <LottieLoader {...props} type="downloading" />
);

// Hook for programmatic Lottie control
export const useLottieAnimation = () => {
  const animationRef = useRef<LottieView>(null);

  const play = () => {
    animationRef.current?.play();
  };

  const pause = () => {
    animationRef.current?.pause();
  };

  const reset = () => {
    animationRef.current?.reset();
  };

  const playFromProgress = (startProgress: number, endProgress: number) => {
    animationRef.current?.play(startProgress, endProgress);
  };

  return {
    animationRef,
    play,
    pause,
    reset,
    playFromProgress,
  };
};

const createStyles = (theme: ThemeColors) => StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  lottie: {
    // Lottie styles handled inline
  },
  fallbackText: {
    fontSize: 10,
    color: theme.textSecondary,
    marginTop: SPACING.xs,
    textAlign: 'center',
  },
});
