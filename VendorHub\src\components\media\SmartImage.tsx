import React, { useState, useEffect, useRef } from 'react';
import { StyleSheet, Animated, Dimensions, ViewStyle, ImageStyle } from 'react-native';
import { Image } from 'expo-image';
import { LinearGradient } from 'expo-linear-gradient';
import { RTLView, RTLIcon } from '../RTL';
import { useThemedStyles } from '../../hooks';
import { BORDER_RADIUS, SPACING } from '../../constants/theme';
import { ShimmerView } from '../visual/MicroAnimations';
import type { ThemeColors } from '../../contexts/ThemeContext';

const { width: screenWidth } = Dimensions.get('window');

interface SmartImageProps {
  source: string | { uri: string } | number;
  style?: ViewStyle | ImageStyle;
  aspectRatio?: number;
  placeholder?: string;
  fallbackIcon?: string;
  borderRadius?: number;
  showShimmer?: boolean;
  progressive?: boolean;
  cachePolicy?: 'memory' | 'disk' | 'memory-disk' | 'none';
  priority?: 'low' | 'normal' | 'high';
  onLoad?: () => void;
  onError?: () => void;
  resizeMode?: 'cover' | 'contain' | 'stretch' | 'repeat' | 'center';
  blurRadius?: number;
  tint?: string;
  overlayGradient?: readonly string[];
  children?: React.ReactNode;
}

export const SmartImage: React.FC<SmartImageProps> = ({
  source,
  style,
  aspectRatio,
  placeholder,
  fallbackIcon = 'image-outline',
  borderRadius = BORDER_RADIUS.md,
  showShimmer = true,
  progressive = true,
  cachePolicy = 'memory-disk',
  priority = 'normal',
  onLoad,
  onError,
  resizeMode = 'cover',
  blurRadius,
  tint,
  overlayGradient,
  children,
}) => {
  const styles = useThemedStyles(createStyles);
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [imageSize, setImageSize] = useState<{ width: number; height: number } | null>(null);
  
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(1.1)).current;

  // Smart aspect ratio calculation
  const calculatedAspectRatio = aspectRatio || (imageSize ? imageSize.width / imageSize.height : 1);
  
  // Optimize image source based on screen density and size
  const getOptimizedSource = () => {
    if (typeof source === 'string') {
      // Add optimization parameters for remote images
      const separator = source.includes('?') ? '&' : '?';
      const density = Dimensions.get('screen').scale;
      const width = Math.round(screenWidth * density);
      
      return {
        uri: `${source}${separator}w=${width}&q=80&f=auto`,
        headers: {
          'Cache-Control': 'max-age=3600',
        },
      };
    }
    return source;
  };

  const handleLoad = () => {
    setIsLoading(false);
    setHasError(false);
    
    // Smooth fade-in animation
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 400,
        useNativeDriver: true,
      }),
    ]).start();
    
    onLoad?.();
  };

  const handleError = () => {
    setIsLoading(false);
    setHasError(true);
    onError?.();
  };

  const handleLoadStart = () => {
    setIsLoading(true);
    setHasError(false);
  };

  // Get image dimensions for smart sizing
  useEffect(() => {
    if (typeof source === 'string' || (typeof source === 'object' && 'uri' in source)) {
      const uri = typeof source === 'string' ? source : source.uri;
      Image.getSize(
        uri,
        (width, height) => setImageSize({ width, height }),
        () => setImageSize(null)
      );
    }
  }, [source]);

  const containerStyle = [
    styles.container,
    {
      aspectRatio: calculatedAspectRatio,
      borderRadius,
    },
    style,
  ];

  const imageStyle = [
    styles.image,
    {
      borderRadius,
      opacity: fadeAnim,
      transform: [{ scale: scaleAnim }],
    },
  ];

  const renderPlaceholder = () => (
    <RTLView style={[styles.placeholder, { borderRadius }]}>
      {showShimmer ? (
        <ShimmerView style={styles.shimmerContainer}>
          <RTLView style={styles.placeholderContent}>
            <RTLIcon name={fallbackIcon} size={32} color="rgba(255, 255, 255, 0.5)" />
          </RTLView>
        </ShimmerView>
      ) : (
        <RTLView style={styles.placeholderContent}>
          <RTLIcon name={fallbackIcon} size={32} color="rgba(255, 255, 255, 0.5)" />
        </RTLView>
      )}
    </RTLView>
  );

  const renderError = () => (
    <RTLView style={[styles.errorContainer, { borderRadius }]}>
      <RTLIcon name="image-off-outline" size={32} color="rgba(255, 255, 255, 0.6)" />
    </RTLView>
  );

  return (
    <RTLView style={containerStyle}>
      {/* Placeholder/Loading State */}
      {(isLoading || hasError) && (
        hasError ? renderError() : renderPlaceholder()
      )}

      {/* Main Image */}
      {!hasError && (
        <Animated.View style={imageStyle}>
          <Image
            source={getOptimizedSource()}
            style={styles.imageContent}
            contentFit={resizeMode}
            transition={progressive ? 200 : 0}
            placeholder={placeholder}
            cachePolicy={cachePolicy}
            priority={priority}
            onLoad={handleLoad}
            onError={handleError}
            onLoadStart={handleLoadStart}
            blurRadius={blurRadius}
            tint={tint}
          />
        </Animated.View>
      )}

      {/* Overlay Gradient */}
      {overlayGradient && !isLoading && !hasError && (
        <LinearGradient
          colors={overlayGradient}
          style={[styles.overlay, { borderRadius }]}
          start={{ x: 0, y: 0 }}
          end={{ x: 0, y: 1 }}
        />
      )}

      {/* Children Content */}
      {children && !isLoading && !hasError && (
        <RTLView style={styles.childrenContainer}>
          {children}
        </RTLView>
      )}
    </RTLView>
  );
};

const createStyles = (colors: ThemeColors) => StyleSheet.create({
  container: {
    overflow: 'hidden',
    backgroundColor: colors.backgroundSecondary,
    position: 'relative',
  },
  image: {
    ...StyleSheet.absoluteFillObject,
  },
  imageContent: {
    width: '100%',
    height: '100%',
  },
  placeholder: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  shimmerContainer: {
    ...StyleSheet.absoluteFillObject,
  },
  placeholderContent: {
    justifyContent: 'center',
    alignItems: 'center',
    flex: 1,
  },
  errorContainer: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(239, 68, 68, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(239, 68, 68, 0.3)',
    borderStyle: 'dashed',
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
  },
  childrenContainer: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'flex-end',
    padding: SPACING.md,
  },
});
