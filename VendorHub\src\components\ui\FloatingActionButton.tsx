import React, { useRef, useEffect, useState } from 'react';
import {
  Animated,
  StyleSheet,
  Dimensions,
  PanResponder,
  Platform } from 'react-native';
import { RTLIcon, RTLTouchableOpacity, RTLView } from '../RTL';
import * as Haptics from 'expo-haptics';
import { useThemedStyles } from '../../hooks';
import { webCompatibleHaptics, isWeb } from '../../utils/webCompatibility';
import {
  SPACING,
  BORDER_RADIUS } from '../../constants/theme';
import type ThemeColors  from '../../contexts/ThemeContext';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

export interface FABAction {
  id: string;
  icon: string;
  label: string;
  onPress: () => void;
  color?: string;
  backgroundColor?: string;
}

export interface FloatingActionButtonProps {
  icon?: string;
  onPress?: () => void;
  actions?: FABAction[];
  position?: 'bottom-right' | 'bottom-left' | 'bottom-center';
  size?: 'small' | 'medium' | 'large';
  color?: string;
  backgroundColor?: string;
  draggable?: boolean;
  autoHide?: boolean;
  hideOnScroll?: boolean;
  style?: any;
}

export const FloatingActionButton: React.FC<FloatingActionButtonProps> = ({
  icon = 'add',
  onPress,
  actions = [],
  position = 'bottom-right',
  size = 'medium',
  color = '#FFFFFF',
  backgroundColor = '#667eea',
  draggable = false,
  autoHide = false,
  hideOnScroll = false,
  style,
}) => {
  const styles = useThemedStyles(createStyles);
  const [isExpanded, setIsExpanded] = useState(false);
  const [isVisible, setIsVisible] = useState(true);
  
  // Animation values
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const rotateAnim = useRef(new Animated.Value(0)).current;
  const opacityAnim = useRef(new Animated.Value(1)).current;
  const translateY = useRef(new Animated.Value(0)).current;
  const translateX = useRef(new Animated.Value(0)).current;
  
  // Action animations
  const actionAnims = useRef(
    actions.map(() => ({
      scale: new Animated.Value(0),
      opacity: new Animated.Value(0),
      translateY: new Animated.Value(50),
    }))
  ).current;

  // Dragging state
  const [dragPosition, setDragPosition] = useState({ x: 0, y: 0 });
  const lastPosition = useRef({ x: 0, y: 0 });

  const panResponder = useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => draggable && !isWeb,
      onMoveShouldSetPanResponder: () => draggable && !isWeb,

      onPanResponderGrant: () => {
        webCompatibleHaptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

        Animated.spring(scaleAnim, {
          toValue: 1.1,
          useNativeDriver: true,
          tension: 300,
          friction: 10,
        }).start();
      },
      
      onPanResponderMove: (evt, gestureState) => {
        const newX = lastPosition.current.x + gestureState.dx;
        const newY = lastPosition.current.y + gestureState.dy;
        
        // Keep within screen bounds
        const boundedX = Math.max(0, Math.min(screenWidth - 56, newX));
        const boundedY = Math.max(0, Math.min(screenHeight - 56, newY));
        
        setDragPosition({ x: boundedX, y: boundedY });
        translateX.setValue(boundedX);
        translateY.setValue(boundedY);
      },
      
      onPanResponderRelease: () => {
        lastPosition.current = dragPosition;
        
        Animated.spring(scaleAnim, {
          toValue: 1,
          useNativeDriver: true,
          tension: 300,
          friction: 10,
        }).start();
      },
    })
  ).current;

  const getFABSize = () => {
    switch (size) {
      case 'small':
        return 40;
      case 'large':
        return 64;
      default:
        return 56;
    }
  };

  const getIconSize = () => {
    switch (size) {
      case 'small':
        return 20;
      case 'large':
        return 32;
      default:
        return 24;
    }
  };

  const getPositionStyle = () => {
    if (draggable) {
      return {
        position: 'absolute' as const,
        transform: [
          { translateX },
          { translateY },
          { scale: scaleAnim },
          {
            rotate: rotateAnim.interpolate({
              inputRange: [0, 1],
              outputRange: ['0deg', '45deg'],
            }),
          },
        ],
      };
    }

    const fabSize = getFABSize();
    const baseStyle = {
      position: 'absolute' as const,
      transform: [
        { scale: scaleAnim },
        {
          rotate: rotateAnim.interpolate({
            inputRange: [0, 1],
            outputRange: ['0deg', '45deg'],
          }),
        },
      ],
    };

    switch (position) {
      case 'bottom-left':
        return {
          ...baseStyle,
          bottom: SPACING.xl,
          left: SPACING.lg,
        };
      case 'bottom-center':
        return {
          ...baseStyle,
          bottom: SPACING.xl,
          left: (screenWidth - fabSize) / 2,
        };
      default:
        return {
          ...baseStyle,
          bottom: SPACING.xl,
          right: SPACING.lg,
        };
    }
  };

  const handlePress = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    
    if (actions.length > 0) {
      toggleActions();
    } else {
      // Single action animation
      Animated.sequence([
        Animated.timing(scaleAnim, {
          toValue: 0.9,
          duration: 100,
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 1,
          duration: 100,
          useNativeDriver: true,
        }),
      ]).start();
      
      onPress?.();
    }
  };

  const toggleActions = () => {
    const newExpanded = !isExpanded;
    setIsExpanded(newExpanded);

    // Rotate main button
    Animated.timing(rotateAnim, {
      toValue: newExpanded ? 1 : 0,
      duration: 200,
      useNativeDriver: true,
    }).start();

    // Animate actions
    if (newExpanded) {
      // Show actions with stagger
      const animations = actionAnims.map((anim, index) =>
        Animated.parallel([
          Animated.timing(anim.scale, {
            toValue: 1,
            duration: 200,
            delay: index * 50,
            useNativeDriver: true,
          }),
          Animated.timing(anim.opacity, {
            toValue: 1,
            duration: 200,
            delay: index * 50,
            useNativeDriver: true,
          }),
          Animated.timing(anim.translateY, {
            toValue: 0,
            duration: 200,
            delay: index * 50,
            useNativeDriver: true,
          }),
        ])
      );
      
      Animated.stagger(50, animations).start();
    } else {
      // Hide actions
      const animations = actionAnims.map(anim =>
        Animated.parallel([
          Animated.timing(anim.scale, {
            toValue: 0,
            duration: 150,
            useNativeDriver: true,
          }),
          Animated.timing(anim.opacity, {
            toValue: 0,
            duration: 150,
            useNativeDriver: true,
          }),
          Animated.timing(anim.translateY, {
            toValue: 50,
            duration: 150,
            useNativeDriver: true,
          }),
        ])
      );
      
      Animated.parallel(animations).start();
    }
  };

  const handleActionPress = (action: FABAction) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    toggleActions(); // Close the menu
    action.onPress();
  };

  const hide = () => {
    setIsVisible(false);
    Animated.timing(opacityAnim, {
      toValue: 0,
      duration: 200,
      useNativeDriver: true,
    }).start();
  };

  const show = () => {
    setIsVisible(true);
    Animated.timing(opacityAnim, {
      toValue: 1,
      duration: 200,
      useNativeDriver: true,
    }).start();
  };

  // Auto-hide functionality
  useEffect(() => {
    if (autoHide) {
      const timer = setTimeout(() => {
        if (!isExpanded) {
          hide();
        }
      }, 3000);
      
      return () => clearTimeout(timer);
    }
  }, [autoHide, isExpanded]);

  if (!isVisible && !autoHide) return null;

  const fabSize = getFABSize();
  const iconSize = getIconSize();

  return (
    <RTLView style={styles.container}>
      {/* Action buttons */}
      {actions.map((action, index) => (
        <Animated.View
          key={action.id}
          style={[
            styles.actionButton,
            {
              bottom: SPACING.xl + (index + 1) * (fabSize + SPACING.sm),
              right: position === 'bottom-right' ? SPACING.lg : undefined,
              left: position === 'bottom-left' ? SPACING.lg : undefined,
              transform: [
                { scale: actionAnims[index].scale },
                { translateY: actionAnims[index].translateY },
              ],
              opacity: actionAnims[index].opacity,
            },
          ]}
        >
          <RTLTouchableOpacity
            style={[
              styles.actionTouchable,
              {
                backgroundColor: action.backgroundColor || '#FFFFFF',
                width: fabSize * 0.8,
                height: fabSize * 0.8,
              },
            ]}
            onPress={() => handleActionPress(action)}
            activeOpacity={0.8}
          >
            <RTLIcon
              name={action.icon as any}
              size={iconSize * 0.8}
              color={action.color || '#667eea'}
            />
          </RTLTouchableOpacity>
        </Animated.View>
      ))}

      {/* Main FAB */}
      <Animated.View
        style={[
          getPositionStyle(),
          {
            opacity: opacityAnim,
            width: fabSize,
            height: fabSize,
          },
          style,
        ]}
        {...(draggable ? panResponder.panHandlers : {})}
      >
        <RTLTouchableOpacity
          style={[
            styles.fab,
            {
              backgroundColor,
              width: fabSize,
              height: fabSize,
              borderRadius: fabSize / 2,
            },
          ]}
          onPress={handlePress}
          activeOpacity={0.8}
        >
          <RTLIcon name={icon as any} size={iconSize} color={color} />
        </RTLTouchableOpacity>
      </Animated.View>
    </RTLView>
  );
};

const createStyles = (colors: ThemeColors) => StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    left: 0,
    top: 0,
    pointerEvents: 'box-none',
  },
  fab: {
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  actionButton: {
    position: 'absolute',
  },
  actionTouchable: {
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
});
