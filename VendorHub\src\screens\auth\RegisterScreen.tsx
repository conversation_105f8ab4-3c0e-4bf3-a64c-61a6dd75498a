import React, { useState } from 'react';
import { StyleSheet, KeyboardAvoidingView, Platform, Alert } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useAuth, useThemedStyles, useI18n } from '../../hooks';
import { Button, Card, Input } from '../../components';
import { RTLView, RTLText, RTLScrollView, RTLSafeAreaView } from '../../components/RTL';
import {
  SPACING,
  FONT_SIZES,
  FONT_WEIGHTS,
  GRADIENTS } from '../../constants/theme';
import { APP_NAME, USER_ROLES, type UserRole  } from '../../constants';
import { validateEmail, validatePassword, validateBusinessName } from '../../utils';
import type ThemeColors  from '../../contexts/ThemeContext';
import type RegisterData  from '../../contexts/AuthContext';

interface RegisterScreenProps {
  navigation: any;
  route: {
    params?: {
      role?: UserRole;
    };
  };
}

export const RegisterScreen: React.FC<RegisterScreenProps> = ({ navigation, route }) => {
  const styles = useThemedStyles(createStyles);
  const { register, isLoading } = useAuth();
  const { t } = useI18n();
  
  const [formData, setFormData] = useState<RegisterData>({
    email: '',
    password: '',
    name: '',
    role: route.params?.role || USER_ROLES.CUSTOMER,
    businessName: '',
    businessDescription: '',
    phone: '',
  });
  
  const [confirmPassword, setConfirmPassword] = useState('');
  const [errors, setErrors] = useState<Record<string, string>>({});

  const isVendorRegistration = formData.role === USER_ROLES.VENDOR;

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    // Basic validation
    if (!formData.name.trim()) {
      newErrors.name = t('auth.nameRequired');
    }

    if (!formData.email.trim()) {
      newErrors.email = t('auth.emailRequired');
    } else if (!validateEmail(formData.email)) {
      newErrors.email = t('auth.emailInvalid');
    }

    if (!formData.password) {
      newErrors.password = t('auth.passwordRequired');
    } else if (!validatePassword(formData.password)) {
      newErrors.password = t('auth.passwordMinimumLength');
    }

    if (formData.password !== confirmPassword) {
      newErrors.confirmPassword = t('auth.passwordsDoNotMatch');
    }

    // Vendor-specific validation
    if (isVendorRegistration) {
      if (!formData.businessName?.trim()) {
        newErrors.businessName = t('auth.businessNameRequired');
      } else if (!validateBusinessName(formData.businessName)) {
        newErrors.businessName = t('auth.businessNameLength');
      }

      if (!formData.businessDescription?.trim()) {
        newErrors.businessDescription = t('auth.businessDescriptionRequired');
      }

      if (!formData.phone?.trim()) {
        newErrors.phone = t('auth.phoneRequired');
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleRegister = async () => {
    if (!validateForm()) return;

    try {
      const result = await register(formData);
      
      if (result.success) {
        if (isVendorRegistration) {
          Alert.alert(
            t('auth.registrationSuccessful'),
            t('auth.vendorApplicationSubmitted'),
            [{ text: t('common.ok'), onPress: () => navigation.navigate('Login') }]
          );
        } else {
          // Customer registration - auto login successful
          Alert.alert(
            t('auth.welcome'),
            t('auth.accountCreatedSuccessfully'),
            [{ text: t('common.ok') }]
          );
        }
      } else {
        Alert.alert(t('auth.registrationFailed'), result.message || t('common.tryAgain'));
      }
    } catch (error) {
      Alert.alert(t('common.error'), t('common.unexpectedError'));
    }
  };

  const handleLogin = () => {
    navigation.navigate('Login', { role: formData.role });
  };

  const handleBack = () => {
    navigation.goBack();
  };

  const updateFormData = (field: keyof RegisterData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  return (
    <LinearGradient colors={GRADIENTS.primary} style={styles.container}>
      <RTLSafeAreaView style={styles.safeArea}>
        <KeyboardAvoidingView
          style={styles.keyboardAvoid}
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        >
          <RTLScrollView
            contentContainerStyle={styles.scrollContent}
            showsVerticalScrollIndicator={false}
            keyboardShouldPersistTaps="handled"
          >
            {/* Header */}
            <RTLView style={styles.header}>
              <RTLText style={styles.logoText}>{APP_NAME}</RTLText>
              <RTLText style={styles.subtitle}>
                {isVendorRegistration ? t('auth.vendorRegistration') : t('auth.createAccount')}
              </RTLText>
              <RTLText style={styles.description}>
                {isVendorRegistration
                  ? t('auth.vendorRegistrationDescription')
                  : t('auth.createAccountDescription')}
              </RTLText>
            </RTLView>

            {/* Registration Form */}
            <Card variant="glass" style={styles.formCard}>
              <RTLView style={styles.form}>
                <Input
                  label={t('auth.name')}
                  value={formData.name}
                  onChangeText={(value) => updateFormData('name', value)}
                  placeholder={t('auth.namePlaceholder')}
                  leftIcon="person-outline"
                  error={errors.name}
                  required
                />

                <Input
                  label={t('auth.email')}
                  value={formData.email}
                  onChangeText={(value) => updateFormData('email', value)}
                  placeholder={t('auth.emailPlaceholder')}
                  keyboardType="email-address"
                  autoCapitalize="none"
                  autoCorrect={false}
                  leftIcon="mail-outline"
                  error={errors.email}
                  required
                />

                <Input
                  label={t('auth.password')}
                  value={formData.password}
                  onChangeText={(value) => updateFormData('password', value)}
                  placeholder={t('auth.passwordPlaceholder')}
                  secureTextEntry
                  leftIcon="lock-closed-outline"
                  error={errors.password}
                  helperText={t('auth.passwordMinimum')}
                  required
                />

                <Input
                  label={t('auth.confirmPassword')}
                  value={confirmPassword}
                  onChangeText={setConfirmPassword}
                  placeholder={t('auth.confirmPasswordPlaceholder')}
                  secureTextEntry
                  leftIcon="lock-closed-outline"
                  error={errors.confirmPassword}
                  required
                />

                {isVendorRegistration && (
                  <>
                    <Input
                      label="Business Name"
                      value={formData.businessName || ''}
                      onChangeText={(value) => updateFormData('businessName', value)}
                      placeholder={t('auth.businessNamePlaceholder')}
                      leftIcon="storefront-outline"
                      error={errors.businessName}
                      required
                    />

                    <Input
                      label="Business Description"
                      value={formData.businessDescription || ''}
                      onChangeText={(value) => updateFormData('businessDescription', value)}
                      placeholder={t('auth.businessDescriptionPlaceholder')}
                      multiline
                      numberOfLines={3}
                      leftIcon="document-text-outline"
                      error={errors.businessDescription}
                      required
                    />

                    <Input
                      label="Phone Number"
                      value={formData.phone || ''}
                      onChangeText={(value) => updateFormData('phone', value)}
                      placeholder={t('auth.phonePlaceholder')}
                      keyboardType="phone-pad"
                      leftIcon="call-outline"
                      error={errors.phone}
                      required
                    />
                  </>
                )}

                <Button
                  title={isLoading ? t('auth.creatingAccount') : t('auth.createAccount')}
                  onPress={handleRegister}
                  loading={isLoading}
                  disabled={isLoading}
                  style={styles.registerButton}
                />
              </RTLView>
            </Card>

            {/* Actions */}
            <RTLView style={styles.actionsContainer}>
              <Button
                title="Already have an account? Sign In"
                onPress={handleLogin}
                variant="ghost"
                style={styles.loginButton}
                textStyle={styles.loginButtonText}
              />

              <Button
                title="Back"
                onPress={handleBack}
                variant="ghost"
                style={styles.backButton}
                textStyle={styles.backButtonText}
              />
            </RTLView>
          </RTLScrollView>
        </KeyboardAvoidingView>
      </RTLSafeAreaView>
    </LinearGradient>
  );
};

const createStyles = (colors: ThemeColors) =>
  StyleSheet.create({
    container: {
      flex: 1,
    },
    safeArea: {
      flex: 1,
    },
    keyboardAvoid: {
      flex: 1,
    },
    scrollContent: {
      flexGrow: 1,
      paddingHorizontal: SPACING.lg,
      paddingBottom: SPACING.xl,
    },
    header: {
      alignItems: 'center',
      paddingTop: SPACING.xl,
      paddingBottom: SPACING.lg,
    },
    logoText: {
      fontSize: FONT_SIZES.xxxl,
      fontWeight: FONT_WEIGHTS.bold,
      color: colors.textOnPrimary,
      marginBottom: SPACING.sm,
    },
    subtitle: {
      fontSize: FONT_SIZES.lg,
      fontWeight: FONT_WEIGHTS.medium,
      color: colors.textOnPrimary,
      marginBottom: SPACING.xs,
      opacity: 0.9,
    },
    description: {
      fontSize: FONT_SIZES.sm,
      color: colors.textOnPrimary,
      textAlign: 'center',
      opacity: 0.8,
    },
    formCard: {
      marginBottom: SPACING.lg,
    },
    form: {
      paddingVertical: SPACING.md,
    },
    registerButton: {
      marginTop: SPACING.lg,
    },
    actionsContainer: {
      alignItems: 'center',
      paddingTop: SPACING.md,
    },
    loginButton: {
      backgroundColor: 'transparent',
      marginBottom: SPACING.sm,
    },
    loginButtonText: {
      color: colors.textOnPrimary,
    },
    backButton: {
      backgroundColor: 'transparent',
    },
    backButtonText: {
      color: colors.textOnPrimary,
      opacity: 0.7,
    },
  });
