import React, { useState } from 'react';
import { StyleSheet, ViewStyle, TextInputProps } from 'react-native';
import { RTLIcon, RTLInput, RTLText, RTLTouchableOpacity, RTLView } from './RTL';
import { useThemedStyles, useI18n } from '../hooks';
import {
  INPUT_HEIGHT,
  BORDER_RADIUS,
  SPACING,
  FONT_SIZES,
  FONT_WEIGHTS } from '../constants/theme';
import type ThemeColors  from '../contexts/ThemeContext';

interface InputProps extends TextInputProps {
  label?: string;
  error?: string;
  helperText?: string;
  leftIcon?: keyof typeof Ionicons.glyphMap;
  rightIcon?: keyof typeof Ionicons.glyphMap;
  onRightIconPress?: () => void;
  variant?: 'default' | 'outlined' | 'filled';
  size?: 'small' | 'medium' | 'large';
  required?: boolean;
  containerStyle?: ViewStyle;
}

export const Input: React.FC<InputProps> = ({
  label,
  error,
  helperText,
  leftIcon,
  rightIcon,
  onRightIconPress,
  variant = 'outlined',
  size = 'medium',
  required = false,
  containerStyle,
  style,
  secureTextEntry,
  ...props
}) => {
  const styles = useThemedStyles(createStyles);
  const { isRTL } = useI18n();
  const [isFocused, setIsFocused] = useState(false);
  const [isPasswordVisible, setIsPasswordVisible] = useState(!secureTextEntry);

  const handleFocus = () => {
    setIsFocused(true);
  };

  const handleBlur = () => {
    setIsFocused(false);
  };

  const togglePasswordVisibility = () => {
    setIsPasswordVisible(!isPasswordVisible);
  };

  const inputContainerStyle = [
    styles.inputContainer,
    styles[variant],
    styles[size],
    isFocused && styles.focused,
    error && styles.error,
  ].filter(Boolean);

  const inputStyle = [
    styles.input,
    styles[`${size}Input`],
    leftIcon && styles.inputWithLeftIcon,
    (rightIcon || secureTextEntry) && styles.inputWithRightIcon,
    style,
  ].filter(Boolean);

  const renderRightIcon = () => {
    if (secureTextEntry) {
      return (
        <RTLTouchableOpacity
          onPress={togglePasswordVisibility}
          style={styles.iconContainer}
        >
          <RTLIcon
            name={isPasswordVisible ? 'eye-off' : 'eye'}
            size={20}
            color={styles.icon.color}
          />
        </RTLTouchableOpacity>
      );
    }

    if (rightIcon) {
      return (
        <RTLTouchableOpacity
          onPress={onRightIconPress}
          style={styles.iconContainer}
        >
          <RTLIcon
            name={rightIcon}
            size={20}
            color={styles.icon.color}
          />
        </RTLTouchableOpacity>
      );
    }

    return null;
  };

  // Determine icon positions based on RTL
  const leftIconToShow = isRTL ? rightIcon : leftIcon;
  const rightIconToShow = isRTL ? leftIcon : rightIcon;
  const leftIconPress = isRTL ? onRightIconPress : undefined;
  const rightIconPress = isRTL ? undefined : onRightIconPress;

  return (
    <RTLView style={[styles.container, containerStyle].filter(Boolean) as any}>
      {label && (
        <RTLText style={styles.label}>
          {label}
          {required && <RTLText style={styles.required}> *</RTLText>}
        </RTLText>
      )}

      <RTLView style={inputContainerStyle as any}>
        {leftIconToShow && (
          <RTLView style={styles.iconContainer}>
            <RTLIcon
              name={leftIconToShow}
              size={20}
              color={styles.icon.color}
            />
          </RTLView>
        )}

        <RTLInput
          style={inputStyle as any}
          onFocus={handleFocus}
          onBlur={handleBlur}
          secureTextEntry={secureTextEntry && !isPasswordVisible}
          placeholderTextColor={styles.placeholder.color}
          {...props}
        />

        {(rightIconToShow || secureTextEntry) && (
          <RTLTouchableOpacity
            style={styles.iconContainer}
            onPress={secureTextEntry ? togglePasswordVisibility : rightIconPress}
          >
            <RTLIcon
              name={secureTextEntry ? (isPasswordVisible ? 'eye-off' : 'eye') : rightIconToShow!}
              size={20}
              color={styles.icon.color}
            />
          </RTLTouchableOpacity>
        )}
      </RTLView>

      {error && <RTLText style={styles.errorText}>{error}</RTLText>}
      {helperText && !error && <RTLText style={styles.helperText}>{helperText}</RTLText>}
    </RTLView>
  );
};

const createStyles = (colors: ThemeColors) =>
  StyleSheet.create({
    container: {
      marginBottom: SPACING.md,
    },
    label: {
      fontSize: FONT_SIZES.sm,
      fontWeight: FONT_WEIGHTS.medium,
      color: colors.textPrimary,
      marginBottom: SPACING.xs,
    },
    required: {
      color: colors.error,
    },
    inputContainer: {
      flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
      alignItems: 'center',
      borderRadius: BORDER_RADIUS.md,
      backgroundColor: colors.surface,
    },
    default: {
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
      backgroundColor: 'transparent',
      borderRadius: 0,
    },
    outlined: {
      borderWidth: 1,
      borderColor: colors.border,
    },
    filled: {
      backgroundColor: colors.backgroundSecondary,
      borderWidth: 0,
    },
    small: {
      height: INPUT_HEIGHT * 0.8,
    },
    medium: {
      height: INPUT_HEIGHT,
    },
    large: {
      height: INPUT_HEIGHT * 1.2,
    },
    focused: {
      borderColor: colors.primary,
      borderWidth: 2,
    },
    error: {
      borderColor: colors.error,
      borderWidth: 1,
    },
    input: {
      flex: 1,
      fontSize: FONT_SIZES.md,
      color: colors.textPrimary,
      paddingHorizontal: SPACING.md,
    },
    smallInput: {
      fontSize: FONT_SIZES.sm,
    },
    mediumInput: {
      fontSize: FONT_SIZES.md,
    },
    largeInput: {
      fontSize: FONT_SIZES.lg,
    },
    inputWithLeftIcon: {
      paddingLeft: SPACING.xs,
    },
    inputWithRightIcon: {
      paddingRight: SPACING.xs,
    },
    iconContainer: {
      padding: SPACING.sm,
      justifyContent: 'center',
      alignItems: 'center',
    },
    icon: {
      color: colors.textSecondary,
    },
    placeholder: {
      color: colors.textSecondary,
    },
    errorText: {
      fontSize: FONT_SIZES.xs,
      color: colors.error,
      marginTop: SPACING.xs,
    },
    helperText: {
      fontSize: FONT_SIZES.xs,
      color: colors.textSecondary,
      marginTop: SPACING.xs,
    },
  });
