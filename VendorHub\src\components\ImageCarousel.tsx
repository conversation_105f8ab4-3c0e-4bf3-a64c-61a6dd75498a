import React, { useState, useRef } from 'react';
import { StyleSheet, Dimensions, Modal, StatusBar, Platform } from 'react-native';
import { useThemedStyles } from '../hooks';
import { ZoomableImage } from './ui/ZoomableImage';
import { RTLScrollView, RTLView, RTLText, RTLIcon, RTLTouchableOpacity } from './RTL';
import {
  SPACING,
  FONT_SIZES,
  FONT_WEIGHTS,
  BORDER_RADIUS,
  ICON_SIZES } from '../constants/theme';
import type ThemeColors  from '../contexts/ThemeContext';

interface ImageCarouselProps {
  images: string[];
  style?: any;
  height?: number;
  showIndicators?: boolean;
  showFullscreenButton?: boolean;
  onImagePress?: (index: number) => void;
}

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

export const ImageCarousel: React.FC<ImageCarouselProps> = ({
  images,
  style,
  height = 300,
  showIndicators = true,
  showFullscreenButton = true,
  onImagePress,
}) => {
  const styles = useThemedStyles(createStyles);
  const scrollViewRef = useRef<RTLScrollView>(null);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [showFullscreen, setShowFullscreen] = useState(false);
  const [fullscreenIndex, setFullscreenIndex] = useState(0);

  const handleScroll = (event: any) => {
    const contentOffset = event.nativeEvent.contentOffset.x;
    const index = Math.round(contentOffset / screenWidth);
    setCurrentIndex(index);
  };

  const handleImagePress = (index: number) => {
    if (onImagePress) {
      onImagePress(index);
    } else if (showFullscreenButton) {
      setFullscreenIndex(index);
      setShowFullscreen(true);
    }
  };

  const handleIndicatorPress = (index: number) => {
    setCurrentIndex(index);
    scrollViewRef.current?.scrollTo({
      x: index * screenWidth,
      animated: true,
    });
  };

  const handleFullscreenClose = () => {
    setShowFullscreen(false);
  };

  const handleFullscreenScroll = (event: any) => {
    const contentOffset = event.nativeEvent.contentOffset.x;
    const index = Math.round(contentOffset / screenWidth);
    setFullscreenIndex(index);
  };

  const renderImage = (image: string, index: number, isFullscreen = false) => (
    <RTLTouchableOpacity
      key={index}
      style={[
        styles.imageContainer,
        { width: screenWidth, height: isFullscreen ? screenHeight : height },
      ]}
      onPress={() => !isFullscreen && handleImagePress(index)}
      activeOpacity={isFullscreen ? 1 : 0.8}
    >
      {image ? (
        isFullscreen ? (
          <ZoomableImage
            source={{ uri: image }}
            style={styles.fullscreenImage}
            resizeMode="contain"
          />
        ) : (
          <RTLView style={styles.imagePlaceholder}>
            <RTLIcon name="image-outline" size={60} color="#CCCCCC" />
            <RTLText style={styles.imageText}>Product Image</RTLText>
          </RTLView>
        )
      ) : (
        <RTLView style={styles.imagePlaceholder}>
          <RTLIcon name="image-outline" size={60} color="#CCCCCC" />
          <RTLText style={styles.imageText}>No Image</RTLText>
        </RTLView>
      )}
    </RTLTouchableOpacity>
  );

  const renderIndicators = () => (
    <RTLView style={styles.indicatorsContainer}>
      {images.map((_, index) => (
        <RTLTouchableOpacity
          key={index}
          style={[
            styles.indicator,
            currentIndex === index && styles.activeIndicator,
          ]}
          onPress={() => handleIndicatorPress(index)}
        />
      ))}
    </RTLView>
  );

  const renderFullscreenModal = () => (
    <Modal
      visible={showFullscreen}
      transparent={false}
      animationType="fade"
      onRequestClose={handleFullscreenClose}
    >
      <StatusBar hidden={Platform.OS === 'ios'} />
      <RTLView style={styles.fullscreenContainer}>
        {/* Header */}
        <RTLView style={styles.fullscreenHeader}>
          <RTLTouchableOpacity
            style={styles.closeButton}
            onPress={handleFullscreenClose}
          >
            <RTLIcon name="close" size={28} color="#FFFFFF" />
          </RTLTouchableOpacity>
          <RTLText style={styles.imageCounter}>
            {fullscreenIndex + 1} of {images.length}
          </RTLText>
          <RTLView style={styles.headerSpacer} />
        </RTLView>

        {/* Image Gallery */}
        <RTLScrollView
          ref={scrollViewRef}
          horizontal
          pagingEnabled
          showsHorizontalScrollIndicator={false}
          onMomentumScrollEnd={handleFullscreenScroll}
          style={styles.fullscreenScrollView}
          contentOffset={{ x: fullscreenIndex * screenWidth, y: 0 }}
        >
          {images.map((image, index) => renderImage(image, index, true))}
        </RTLScrollView>

        {/* Bottom Indicators */}
        {images.length > 1 && (
          <RTLView style={styles.fullscreenIndicators}>
            {images.map((_, index) => (
              <RTLView
                key={index}
                style={[
                  styles.fullscreenIndicator,
                  fullscreenIndex === index && styles.fullscreenActiveIndicator,
                ]}
              />
            ))}
          </RTLView>
        )}
      </RTLView>
    </Modal>
  );

  if (!images || images.length === 0) {
    return (
      <RTLView style={[styles.container, { height }, style]}>
        <RTLView style={styles.imagePlaceholder}>
          <RTLIcon name="image-outline" size={60} color="#CCCCCC" />
          <RTLText style={styles.imageText}>No Images Available</RTLText>
        </RTLView>
      </RTLView>
    );
  }

  return (
    <RTLView style={[styles.container, { height }, style]}>
      <RTLScrollView
        ref={scrollViewRef}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        onMomentumScrollEnd={handleScroll}
        style={styles.scrollView}
        enableRTLScrolling={true}
      >
        {images.map((image, index) => renderImage(image, index))}
      </RTLScrollView>

      {/* Indicators */}
      {showIndicators && images.length > 1 && renderIndicators()}

      {/* Fullscreen Button */}
      {showFullscreenButton && (
        <RTLTouchableOpacity
          style={styles.fullscreenButton}
          onPress={() => handleImagePress(currentIndex)}
        >
          <RTLIcon name="expand-outline" size={20} color="#FFFFFF" />
        </RTLTouchableOpacity>
      )}

      {/* Fullscreen Modal */}
      {renderFullscreenModal()}
    </RTLView>
  );
};

const createStyles = (colors: ThemeColors) => StyleSheet.create({
  container: {
    position: 'relative',
    backgroundColor: colors.surface,
    borderRadius: BORDER_RADIUS.lg,
    overflow: 'hidden',
  },
  scrollView: {
    flex: 1,
  },
  imageContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.surface,
  },
  imagePlaceholder: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.surface,
    gap: SPACING.sm,
  },
  imageText: {
    fontSize: FONT_SIZES.sm,
    color: colors.textSecondary,
    textAlign: 'center',
  },
  indicatorsContainer: {
    position: 'absolute',
    bottom: SPACING.md,
    left: 0,
    right: 0,
    flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
    justifyContent: 'center',
    gap: SPACING.xs,
  },
  indicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.5)',
  },
  activeIndicator: {
    backgroundColor: '#FFFFFF',
    width: 24,
  },
  fullscreenButton: {
    position: 'absolute',
    top: SPACING.md,
    right: SPACING.md,
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  fullscreenContainer: {
    flex: 1,
    backgroundColor: '#000000',
  },
  fullscreenHeader: {
    flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: Platform.OS === 'ios' ? 50 : 20,
    paddingHorizontal: SPACING.lg,
    paddingBottom: SPACING.md,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
  },
  closeButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  imageCounter: {
    fontSize: FONT_SIZES.md,
    color: '#FFFFFF',
    fontWeight: FONT_WEIGHTS.medium,
  },
  headerSpacer: {
    width: 40,
  },
  fullscreenScrollView: {
    flex: 1,
  },
  fullscreenImage: {
    width: screenWidth,
    height: screenHeight - 120,
  },
  fullscreenIndicators: {
    flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
    justifyContent: 'center',
    paddingVertical: SPACING.lg,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    gap: SPACING.xs,
  },
  fullscreenIndicator: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: 'rgba(255, 255, 255, 0.5)',
  },
  fullscreenActiveIndicator: {
    backgroundColor: '#FFFFFF',
    width: 20,
  },
});
