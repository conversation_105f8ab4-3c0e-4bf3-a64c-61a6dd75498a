import { EventEmitter } from '../utils/EventEmitter';
import { storage } from '../utils/storage';
import NotificationService from './NotificationService';
import I18nService from './I18nService';

export interface ChatMessage {
  id: string;
  chatId: string;
  senderId: string;
  senderName: string;
  senderAvatar?: string;
  receiverId: string;
  content: string;
  type: 'text' | 'image' | 'file' | 'product' | 'order';
  timestamp: string;
  read: boolean;
  edited?: boolean;
  editedAt?: string;
  replyTo?: string;
  metadata?: {
    productId?: string;
    orderId?: string;
    fileName?: string;
    fileSize?: number;
    imageUrl?: string;
  };
}

export interface ChatConversation {
  id: string;
  participants: string[];
  participantNames: { [userId: string]: string };
  participantAvatars: { [userId: string]: string };
  lastMessage?: ChatMessage;
  lastActivity: string;
  unreadCount: { [userId: string]: number };
  type: 'customer_vendor' | 'customer_support' | 'vendor_admin';
  title?: string;
  archived: boolean;
  muted: { [userId: string]: boolean };
}

export interface ChatTypingIndicator {
  chatId: string;
  userId: string;
  userName: string;
  timestamp: string;
}

class ChatService extends EventEmitter {
  private static instance: ChatService;
  private conversations: Map<string, ChatConversation> = new Map();
  private messages: Map<string, ChatMessage[]> = new Map();
  private typingIndicators: Map<string, ChatTypingIndicator[]> = new Map();
  private currentUserId: string | null = null;
  private isConnected: boolean = false;

  private constructor() {
    super();
    this.loadStoredData();
  }

  public static getInstance(): ChatService {
    if (!ChatService.instance) {
      ChatService.instance = new ChatService();
    }
    return ChatService.instance;
  }

  // Connection Management
  public async connect(userId: string) {
    this.currentUserId = userId;
    this.isConnected = true;
    
    // In a real implementation, this would connect to a WebSocket server
    // For now, we'll simulate real-time updates
    this.emit('connected');
    
    // Load user's conversations
    await this.loadUserConversations(userId);
  }

  public disconnect() {
    this.isConnected = false;
    this.currentUserId = null;
    this.emit('disconnected');
  }

  public getConnectionStatus(): boolean {
    return this.isConnected;
  }

  // Message Management
  public async sendMessage(
    chatId: string,
    content: string,
    type: ChatMessage['type'] = 'text',
    metadata?: ChatMessage['metadata'],
    replyTo?: string
  ): Promise<ChatMessage> {
    if (!this.currentUserId) {
      throw new Error('User not connected');
    }

    const conversation = this.conversations.get(chatId);
    if (!conversation) {
      throw new Error('Conversation not found');
    }

    const message: ChatMessage = {
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      chatId,
      senderId: this.currentUserId,
      senderName: conversation.participantNames[this.currentUserId] || 'Unknown',
      senderAvatar: conversation.participantAvatars[this.currentUserId],
      receiverId: conversation.participants.find(id => id !== this.currentUserId) || '',
      content,
      type,
      timestamp: new Date().toISOString(),
      read: false,
      metadata,
      replyTo,
    };

    // Add message to conversation
    const chatMessages = this.messages.get(chatId) || [];
    chatMessages.push(message);
    this.messages.set(chatId, chatMessages);

    // Update conversation
    conversation.lastMessage = message;
    conversation.lastActivity = message.timestamp;
    
    // Update unread count for receiver
    const receiverId = message.receiverId;
    if (receiverId && receiverId !== this.currentUserId) {
      conversation.unreadCount[receiverId] = (conversation.unreadCount[receiverId] || 0) + 1;
    }

    this.conversations.set(chatId, conversation);

    // Save to storage
    await this.saveData();

    // Emit events
    this.emit('messageReceived', message);
    this.emit('conversationUpdated', conversation);

    // Send notification to receiver
    if (receiverId && receiverId !== this.currentUserId) {
      await NotificationService?.scheduleLocalNotification(
        `New message from ${message.senderName}`,
        content,
        {
          type: 'chat',
          chatId,
          senderId: this.currentUserId,
          actionUrl: `/chat/${chatId}`,
        }
      );
    }

    return message;
  }

  public async markMessagesAsRead(chatId: string, userId: string): Promise<void> {
    const messages = this.messages.get(chatId) || [];
    let hasUpdates = false;

    messages.forEach(message => {
      if (message.receiverId === userId && !message.read) {
        message.read = true;
        hasUpdates = true;
      }
    });

    if (hasUpdates) {
      // Update conversation unread count
      const conversation = this.conversations.get(chatId);
      if (conversation) {
        conversation.unreadCount[userId] = 0;
        this.conversations.set(chatId, conversation);
        this.emit('conversationUpdated', conversation);
      }

      await this.saveData();
      this.emit('messagesRead', { chatId, userId });
    }
  }

  public async editMessage(messageId: string, newContent: string): Promise<void> {
    for (const [chatId, messages] of this.messages.entries()) {
      const messageIndex = messages.findIndex(m => m.id === messageId);
      if (messageIndex !== -1) {
        const message = messages[messageIndex];
        if (message.senderId === this.currentUserId) {
          message.content = newContent;
          message.edited = true;
          message.editedAt = new Date().toISOString();
          
          await this.saveData();
          this.emit('messageUpdated', message);
          return;
        }
      }
    }
  }

  public async deleteMessage(messageId: string): Promise<void> {
    for (const [chatId, messages] of this.messages.entries()) {
      const messageIndex = messages.findIndex(m => m.id === messageId);
      if (messageIndex !== -1) {
        const message = messages[messageIndex];
        if (message.senderId === this.currentUserId) {
          messages.splice(messageIndex, 1);
          
          await this.saveData();
          this.emit('messageDeleted', { messageId, chatId });
          return;
        }
      }
    }
  }

  // Conversation Management
  public async createConversation(
    participantIds: string[],
    participantNames: { [userId: string]: string },
    participantAvatars: { [userId: string]: string },
    type: ChatConversation['type'] = 'customer_vendor',
    title?: string
  ): Promise<ChatConversation> {
    const conversationId = Date.now().toString() + Math.random().toString(36).substr(2, 9);
    
    const conversation: ChatConversation = {
      id: conversationId,
      participants: participantIds,
      participantNames,
      participantAvatars,
      lastActivity: new Date().toISOString(),
      unreadCount: {},
      type,
      title,
      archived: false,
      muted: {},
    };

    // Initialize unread counts
    participantIds.forEach(id => {
      conversation.unreadCount[id] = 0;
      conversation.muted[id] = false;
    });

    this.conversations.set(conversationId, conversation);
    this.messages.set(conversationId, []);

    await this.saveData();
    this.emit('conversationCreated', conversation);

    return conversation;
  }

  public async getOrCreateConversation(
    otherUserId: string,
    otherUserName: string,
    otherUserAvatar?: string
  ): Promise<ChatConversation> {
    if (!this.currentUserId) {
      throw new Error('User not connected');
    }

    // Check if conversation already exists
    for (const conversation of this.conversations.values()) {
      if (conversation.participants.includes(this.currentUserId) && 
          conversation.participants.includes(otherUserId) &&
          conversation.participants.length === 2) {
        return conversation;
      }
    }

    // Create new conversation
    return await this.createConversation(
      [this.currentUserId, otherUserId],
      {
        [this.currentUserId]: I18nService.t('chat.you').replace(': ', ''), // Remove the colon and space for participant name
        [otherUserId]: otherUserName,
      },
      {
        [otherUserId]: otherUserAvatar || '',
      }
    );
  }

  // Typing Indicators
  public startTyping(chatId: string): void {
    if (!this.currentUserId) return;

    const conversation = this.conversations.get(chatId);
    if (!conversation) return;

    const indicator: ChatTypingIndicator = {
      chatId,
      userId: this.currentUserId,
      userName: conversation.participantNames[this.currentUserId] || 'Unknown',
      timestamp: new Date().toISOString(),
    };

    const indicators = this.typingIndicators.get(chatId) || [];
    const existingIndex = indicators.findIndex(i => i.userId === this.currentUserId);
    
    if (existingIndex !== -1) {
      indicators[existingIndex] = indicator;
    } else {
      indicators.push(indicator);
    }

    this.typingIndicators.set(chatId, indicators);
    this.emit('typingStarted', indicator);

    // Auto-stop typing after 3 seconds
    setTimeout(() => {
      this.stopTyping(chatId);
    }, 3000);
  }

  public stopTyping(chatId: string): void {
    if (!this.currentUserId) return;

    const indicators = this.typingIndicators.get(chatId) || [];
    const filteredIndicators = indicators.filter(i => i.userId !== this.currentUserId);
    
    this.typingIndicators.set(chatId, filteredIndicators);
    this.emit('typingStopped', { chatId, userId: this.currentUserId });
  }

  // Data Access
  public getConversations(userId: string): ChatConversation[] {
    return Array.from(this.conversations.values())
      .filter(conv => conv.participants.includes(userId) && !conv.archived)
      .sort((a, b) => new Date(b.lastActivity).getTime() - new Date(a.lastActivity).getTime());
  }

  public getMessages(chatId: string): ChatMessage[] {
    return this.messages.get(chatId) || [];
  }

  public getConversation(chatId: string): ChatConversation | undefined {
    return this.conversations.get(chatId);
  }

  public getTypingIndicators(chatId: string): ChatTypingIndicator[] {
    const indicators = this.typingIndicators.get(chatId) || [];
    const now = new Date().getTime();
    
    // Filter out old typing indicators (older than 5 seconds)
    return indicators.filter(indicator => {
      const indicatorTime = new Date(indicator.timestamp).getTime();
      return now - indicatorTime < 5000;
    });
  }

  public getTotalUnreadCount(userId: string): number {
    let total = 0;
    for (const conversation of this.conversations.values()) {
      if (conversation.participants.includes(userId)) {
        total += conversation.unreadCount[userId] || 0;
      }
    }
    return total;
  }

  // Storage
  private async loadStoredData(): Promise<void> {
    try {
      const [conversationsData, messagesData] = await Promise.all([
        storage.getItem('chatConversations'),
        storage.getItem('chatMessages'),
      ]);

      if (conversationsData) {
        const conversations = JSON.parse(conversationsData);
        this.conversations = new Map(Object.entries(conversations));
      }

      if (messagesData) {
        const messages = JSON.parse(messagesData);
        this.messages = new Map(Object.entries(messages));
      }
    } catch (error) {
      console.error('Error loading chat data:', error);
    }
  }

  private async saveData(): Promise<void> {
    try {
      const conversationsObj = Object.fromEntries(this.conversations);
      const messagesObj = Object.fromEntries(this.messages);

      await Promise.all([
        storage.setItem('chatConversations', JSON.stringify(conversationsObj)),
        storage.setItem('chatMessages', JSON.stringify(messagesObj)),
      ]);
    } catch (error) {
      console.error('Error saving chat data:', error);
    }
  }

  private async loadUserConversations(userId: string): Promise<void> {
    // In a real implementation, this would fetch conversations from the server
    // For now, we'll work with locally stored data
    this.emit('conversationsLoaded', this.getConversations(userId));
  }
}

export default ChatService.getInstance();
