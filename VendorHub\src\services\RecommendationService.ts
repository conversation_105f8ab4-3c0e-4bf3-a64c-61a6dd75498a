import type { Product, Order } from '../contexts/DataContext';
import type User  from '../contexts/AuthContext';
import { storage } from '../utils/storage';

export interface UserBehavior {
  userId: string;
  productViews: { [productId: string]: number };
  categoryViews: { [category: string]: number };
  vendorViews: { [vendorId: string]: number };
  searchQueries: string[];
  purchaseHistory: string[];
  cartAdditions: string[];
  wishlistItems: string[];
  lastActivity: string;
}

export interface ProductRecommendation {
  product: Product;
  score: number;
  reason: 'viewed_similar' | 'bought_together' | 'trending' | 'category_match' | 'vendor_match' | 'price_range' | 'collaborative';
  confidence: number;
}

export interface RecommendationContext {
  currentProduct?: Product;
  currentCategory?: string;
  currentVendor?: string;
  priceRange?: { min: number; max: number };
  excludeProductIds?: string[];
}

class RecommendationService {
  private static instance: RecommendationService;
  private userBehaviors: Map<string, UserBehavior> = new Map();
  private productSimilarities: Map<string, Map<string, number>> = new Map();
  private categoryTrends: Map<string, number> = new Map();

  private constructor() {
    this.loadStoredData();
  }

  public static getInstance(): RecommendationService {
    if (!RecommendationService.instance) {
      RecommendationService.instance = new RecommendationService();
    }
    return RecommendationService.instance;
  }

  // User Behavior Tracking
  public async trackProductView(userId: string, productId: string, category: string, vendorId: string) {
    const behavior = this.getUserBehavior(userId);
    
    behavior.productViews[productId] = (behavior.productViews[productId] || 0) + 1;
    behavior.categoryViews[category] = (behavior.categoryViews[category] || 0) + 1;
    behavior.vendorViews[vendorId] = (behavior.vendorViews[vendorId] || 0) + 1;
    behavior.lastActivity = new Date().toISOString();

    this.userBehaviors.set(userId, behavior);
    await this.saveData();
  }

  public async trackSearch(userId: string, query: string) {
    const behavior = this.getUserBehavior(userId);
    behavior.searchQueries.push(query.toLowerCase());
    
    // Keep only last 50 searches
    if (behavior.searchQueries.length > 50) {
      behavior.searchQueries = behavior.searchQueries.slice(-50);
    }

    behavior.lastActivity = new Date().toISOString();
    this.userBehaviors.set(userId, behavior);
    await this.saveData();
  }

  public async trackPurchase(userId: string, productIds: string[]) {
    const behavior = this.getUserBehavior(userId);
    behavior.purchaseHistory.push(...productIds);
    behavior.lastActivity = new Date().toISOString();

    this.userBehaviors.set(userId, behavior);
    await this.saveData();

    // Update product co-purchase relationships
    this.updateProductSimilarities(productIds);
  }

  public async trackCartAddition(userId: string, productId: string) {
    const behavior = this.getUserBehavior(userId);
    behavior.cartAdditions.push(productId);
    behavior.lastActivity = new Date().toISOString();

    this.userBehaviors.set(userId, behavior);
    await this.saveData();
  }

  public async trackWishlistAddition(userId: string, productId: string) {
    const behavior = this.getUserBehavior(userId);
    if (!behavior.wishlistItems.includes(productId)) {
      behavior.wishlistItems.push(productId);
      behavior.lastActivity = new Date().toISOString();

      this.userBehaviors.set(userId, behavior);
      await this.saveData();
    }
  }

  // Recommendation Generation
  public async getRecommendations(
    userId: string,
    products: Product[],
    orders: Order[],
    context?: RecommendationContext,
    limit: number = 10
  ): Promise<ProductRecommendation[]> {
    const behavior = this.getUserBehavior(userId);
    const recommendations: ProductRecommendation[] = [];

    // Get recommendations from different strategies
    const contentBasedRecs = this.getContentBasedRecommendations(behavior, products, context);
    const collaborativeRecs = this.getCollaborativeRecommendations(userId, products, orders);
    const trendingRecs = this.getTrendingRecommendations(products, context);
    const categoryRecs = this.getCategoryBasedRecommendations(behavior, products, context);

    // Combine and score recommendations
    const allRecs = [
      ...contentBasedRecs,
      ...collaborativeRecs,
      ...trendingRecs,
      ...categoryRecs,
    ];

    // Remove duplicates and excluded products
    const uniqueRecs = new Map<string, ProductRecommendation>();
    
    allRecs.forEach(rec => {
      if (context?.excludeProductIds?.includes(rec.product.id)) return;
      if (behavior.purchaseHistory.includes(rec.product.id)) return;

      const existing = uniqueRecs.get(rec.product.id);
      if (!existing || rec.score > existing.score) {
        uniqueRecs.set(rec.product.id, rec);
      }
    });

    // Sort by score and return top recommendations
    return Array.from(uniqueRecs.values())
      .sort((a, b) => b.score - a.score)
      .slice(0, limit);
  }

  private getContentBasedRecommendations(
    behavior: UserBehavior,
    products: Product[],
    context?: RecommendationContext
  ): ProductRecommendation[] {
    const recommendations: ProductRecommendation[] = [];

    // Recommend based on viewed categories
    const topCategories = Object.entries(behavior.categoryViews)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 3)
      .map(([category]) => category);

    products.forEach(product => {
      if (topCategories.includes(product.category)) {
        const categoryScore = behavior.categoryViews[product.category] || 0;
        const score = Math.min(categoryScore / 10, 1) * 0.7; // Max 0.7 for category match

        recommendations.push({
          product,
          score,
          reason: 'category_match',
          confidence: 0.7,
        });
      }
    });

    // Recommend based on viewed vendors
    const topVendors = Object.entries(behavior.vendorViews)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 3)
      .map(([vendorId]) => vendorId);

    products.forEach(product => {
      if (topVendors.includes(product.vendorId)) {
        const vendorScore = behavior.vendorViews[product.vendorId] || 0;
        const score = Math.min(vendorScore / 5, 1) * 0.6; // Max 0.6 for vendor match

        recommendations.push({
          product,
          score,
          reason: 'vendor_match',
          confidence: 0.6,
        });
      }
    });

    // Price range recommendations
    if (context?.priceRange) {
      products.forEach(product => {
        if (product.price >= context.priceRange!.min && product.price <= context.priceRange!.max) {
          recommendations.push({
            product,
            score: 0.5,
            reason: 'price_range',
            confidence: 0.5,
          });
        }
      });
    }

    return recommendations;
  }

  private getCollaborativeRecommendations(
    userId: string,
    products: Product[],
    orders: Order[]
  ): ProductRecommendation[] {
    const recommendations: ProductRecommendation[] = [];
    const userBehavior = this.getUserBehavior(userId);

    // Find similar users based on purchase history
    const similarUsers = this.findSimilarUsers(userId, orders);

    similarUsers.forEach(({ userId: similarUserId, similarity }) => {
      const similarUserBehavior = this.getUserBehavior(similarUserId);
      
      // Recommend products that similar users purchased but current user hasn't
      similarUserBehavior.purchaseHistory.forEach(productId => {
        if (!userBehavior.purchaseHistory.includes(productId)) {
          const product = products.find(p => p.id === productId);
          if (product) {
            recommendations.push({
              product,
              score: similarity * 0.8, // Max 0.8 for collaborative filtering
              reason: 'collaborative',
              confidence: similarity,
            });
          }
        }
      });
    });

    return recommendations;
  }

  private getTrendingRecommendations(
    products: Product[],
    context?: RecommendationContext
  ): ProductRecommendation[] {
    const recommendations: ProductRecommendation[] = [];

    // Simple trending based on rating and review count
    products.forEach(product => {
      const trendingScore = (product.rating * (product.reviewCount || 1)) / 100;
      const normalizedScore = Math.min(trendingScore, 1) * 0.6; // Max 0.6 for trending

      if (normalizedScore > 0.3) { // Only recommend if trending score is decent
        recommendations.push({
          product,
          score: normalizedScore,
          reason: 'trending',
          confidence: 0.6,
        });
      }
    });

    return recommendations;
  }

  private getCategoryBasedRecommendations(
    behavior: UserBehavior,
    products: Product[],
    context?: RecommendationContext
  ): ProductRecommendation[] {
    const recommendations: ProductRecommendation[] = [];

    if (context?.currentProduct) {
      // Find products in the same category
      const sameCategory = products.filter(p => 
        p.category === context.currentProduct!.category &&
        p.id !== context.currentProduct!.id
      );

      sameCategory.forEach(product => {
        recommendations.push({
          product,
          score: 0.7,
          reason: 'viewed_similar',
          confidence: 0.7,
        });
      });
    }

    return recommendations;
  }

  private findSimilarUsers(userId: string, orders: Order[]): Array<{ userId: string; similarity: number }> {
    const userBehavior = this.getUserBehavior(userId);
    const similarities: Array<{ userId: string; similarity: number }> = [];

    // Get all other users who have made purchases
    const otherUsers = new Set<string>();
    orders.forEach(order => {
      if (order.customerId !== userId) {
        otherUsers.add(order.customerId);
      }
    });

    otherUsers.forEach(otherUserId => {
      const otherBehavior = this.getUserBehavior(otherUserId);
      const similarity = this.calculateUserSimilarity(userBehavior, otherBehavior);
      
      if (similarity > 0.3) { // Only consider users with decent similarity
        similarities.push({ userId: otherUserId, similarity });
      }
    });

    return similarities.sort((a, b) => b.similarity - a.similarity).slice(0, 5);
  }

  private calculateUserSimilarity(behavior1: UserBehavior, behavior2: UserBehavior): number {
    // Calculate Jaccard similarity for purchase history
    const purchases1 = new Set(behavior1.purchaseHistory);
    const purchases2 = new Set(behavior2.purchaseHistory);
    
    const intersection = new Set([...purchases1].filter(x => purchases2.has(x)));
    const union = new Set([...purchases1, ...purchases2]);
    
    if (union.size === 0) return 0;
    
    const purchaseSimilarity = intersection.size / union.size;

    // Calculate category preference similarity
    const categories1 = Object.keys(behavior1.categoryViews);
    const categories2 = Object.keys(behavior2.categoryViews);
    const commonCategories = categories1.filter(cat => categories2.includes(cat));
    
    let categorySimilarity = 0;
    if (commonCategories.length > 0) {
      categorySimilarity = commonCategories.length / Math.max(categories1.length, categories2.length);
    }

    // Weighted average
    return (purchaseSimilarity * 0.7) + (categorySimilarity * 0.3);
  }

  private updateProductSimilarities(productIds: string[]) {
    // Update co-purchase relationships
    for (let i = 0; i < productIds.length; i++) {
      for (let j = i + 1; j < productIds.length; j++) {
        const product1 = productIds[i];
        const product2 = productIds[j];

        // Update similarity for product1 -> product2
        if (!this.productSimilarities.has(product1)) {
          this.productSimilarities.set(product1, new Map());
        }
        const similarities1 = this.productSimilarities.get(product1)!;
        similarities1.set(product2, (similarities1.get(product2) || 0) + 1);

        // Update similarity for product2 -> product1
        if (!this.productSimilarities.has(product2)) {
          this.productSimilarities.set(product2, new Map());
        }
        const similarities2 = this.productSimilarities.get(product2)!;
        similarities2.set(product1, (similarities2.get(product1) || 0) + 1);
      }
    }
  }

  private getUserBehavior(userId: string): UserBehavior {
    if (!this.userBehaviors.has(userId)) {
      this.userBehaviors.set(userId, {
        userId,
        productViews: {},
        categoryViews: {},
        vendorViews: {},
        searchQueries: [],
        purchaseHistory: [],
        cartAdditions: [],
        wishlistItems: [],
        lastActivity: new Date().toISOString(),
      });
    }
    return this.userBehaviors.get(userId)!;
  }

  // Storage
  private async loadStoredData() {
    try {
      const [behaviorsData, similaritiesData] = await Promise.all([
        storage.getItem('userBehaviors'),
        storage.getItem('productSimilarities'),
      ]);

      if (behaviorsData) {
        const behaviors = JSON.parse(behaviorsData);
        this.userBehaviors = new Map(Object.entries(behaviors));
      }

      if (similaritiesData) {
        const similarities = JSON.parse(similaritiesData);
        this.productSimilarities = new Map(
          Object.entries(similarities).map(([key, value]) => [
            key,
            new Map(Object.entries(value as any))
          ])
        );
      }
    } catch (error) {
      console.error('Error loading recommendation data:', error);
    }
  }

  private async saveData() {
    try {
      const behaviorsObj = Object.fromEntries(this.userBehaviors);
      const similaritiesObj = Object.fromEntries(
        Array.from(this.productSimilarities.entries()).map(([key, value]) => [
          key,
          Object.fromEntries(value)
        ])
      );

      await Promise.all([
        storage.setItem('userBehaviors', JSON.stringify(behaviorsObj)),
        storage.setItem('productSimilarities', JSON.stringify(similaritiesObj)),
      ]);
    } catch (error) {
      console.error('Error saving recommendation data:', error);
    }
  }

  // Public utility methods
  public async clearUserData(userId: string) {
    this.userBehaviors.delete(userId);
    await this.saveData();
  }

  public getUserInsights(userId: string) {
    const behavior = this.getUserBehavior(userId);
    
    const topCategories = Object.entries(behavior.categoryViews)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5);

    const topVendors = Object.entries(behavior.vendorViews)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5);

    return {
      totalProductViews: Object.values(behavior.productViews).reduce((sum, count) => sum + count, 0),
      totalPurchases: behavior.purchaseHistory.length,
      topCategories,
      topVendors,
      recentSearches: behavior.searchQueries.slice(-10),
      lastActivity: behavior.lastActivity,
    };
  }
}

export default RecommendationService.getInstance();
