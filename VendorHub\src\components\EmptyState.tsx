import React from 'react';
import { StyleSheet, ViewStyle } from 'react-native';
import { useThemedStyles } from '../hooks';
import { Button } from './Button';
import { RTLView, RTLText, RTLIcon } from './RTL';
import { SPACING, FONT_SIZES, FONT_WEIGHTS, ICON_SIZES } from '../constants/theme';
import type ThemeColors  from '../contexts/ThemeContext';

interface EmptyStateProps {
  icon?: keyof typeof Ionicons.glyphMap;
  title: string;
  description?: string;
  actionLabel?: string;
  onAction?: () => void;
  style?: ViewStyle;
  size?: 'small' | 'medium' | 'large';
}

export const EmptyState: React.FC<EmptyStateProps> = ({
  icon = 'folder-open-outline',
  title,
  description,
  actionLabel,
  onAction,
  style,
  size = 'medium',
}) => {
  const styles = useThemedStyles(createStyles);

  const containerStyle = [
    styles.container,
    styles[size],
    style,
  ];

  const getIconSize = () => {
    switch (size) {
      case 'small':
        return ICON_SIZES.lg;
      case 'large':
        return ICON_SIZES.xxl * 1.5;
      case 'medium':
      default:
        return ICON_SIZES.xxl;
    }
  };

  return (
    <RTLView style={containerStyle}>
      <RTLView style={styles.iconContainer}>
        <RTLIcon
          name={icon}
          size={getIconSize()}
          color={styles.icon.color}
        />
      </RTLView>

      <RTLText style={[styles.title, styles[`${size}Title`]]}>
        {title}
      </RTLText>

      {description && (
        <RTLText style={[styles.description, styles[`${size}Description`]]}>
          {description}
        </RTLText>
      )}

      {actionLabel && onAction && (
        <RTLView style={styles.actionContainer}>
          <Button
            title={actionLabel}
            onPress={onAction}
            variant="outline"
            size={size === 'large' ? 'medium' : 'small'}
          />
        </RTLView>
      )}
    </RTLView>
  );
};

const createStyles = (colors: ThemeColors) =>
  StyleSheet.create({
    container: {
      alignItems: 'center',
      justifyContent: 'center',
      paddingHorizontal: SPACING.lg,
    },
    small: {
      paddingVertical: SPACING.lg,
    },
    medium: {
      paddingVertical: SPACING.xl,
    },
    large: {
      paddingVertical: SPACING.xxl,
    },
    iconContainer: {
      marginBottom: SPACING.md,
    },
    icon: {
      color: colors.textSecondary,
      opacity: 0.6,
    },
    title: {
      fontWeight: FONT_WEIGHTS.semibold,
      color: colors.textPrimary,
      textAlign: 'center',
      marginBottom: SPACING.sm,
    },
    smallTitle: {
      fontSize: FONT_SIZES.md,
    },
    mediumTitle: {
      fontSize: FONT_SIZES.lg,
    },
    largeTitle: {
      fontSize: FONT_SIZES.xl,
    },
    description: {
      color: colors.textSecondary,
      textAlign: 'center',
      lineHeight: FONT_SIZES.md * 1.4,
    },
    smallDescription: {
      fontSize: FONT_SIZES.sm,
    },
    mediumDescription: {
      fontSize: FONT_SIZES.md,
    },
    largeDescription: {
      fontSize: FONT_SIZES.lg,
    },
    actionContainer: {
      marginTop: SPACING.lg,
    },
  });
