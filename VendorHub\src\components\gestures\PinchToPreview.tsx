import React, { useRef, useState } from 'react';
import { StyleSheet, Animated, PanResponder, Dimensions, Vibration } from 'react-native';
import { BlurView } from 'expo-blur';
import { LinearGradient } from 'expo-linear-gradient';
import { RTLView, RTLText, RTLIcon, RTLTouchableOpacity } from '../RTL';
import { useThemedStyles, useI18n } from '../../hooks';
import { SPACING, FONT_SIZES, FONT_WEIGHTS, BORDER_RADIUS } from '../../constants/theme';
import { PREMIUM_GRADIENTS } from '../../constants/advancedColors';
import { FadeInView, AnimatedPressable } from '../visual/MicroAnimations';
import type { ThemeColors } from '../../contexts/ThemeContext';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');
const PINCH_THRESHOLD = 0.15;
const PREVIEW_SCALE = 1.2;

interface PinchToPreviewProps {
  children: React.ReactNode;
  previewContent?: React.ReactNode;
  onPreviewOpen?: () => void;
  onPreviewClose?: () => void;
  onPreviewAction?: () => void;
  enablePinch?: boolean;
  pinchThreshold?: number;
  maxPreviewScale?: number;
  hapticFeedback?: boolean;
  style?: any;
}

export const PinchToPreview: React.FC<PinchToPreviewProps> = ({
  children,
  previewContent,
  onPreviewOpen,
  onPreviewClose,
  onPreviewAction,
  enablePinch = true,
  pinchThreshold = PINCH_THRESHOLD,
  maxPreviewScale = PREVIEW_SCALE,
  hapticFeedback = true,
  style,
}) => {
  const styles = useThemedStyles(createStyles);
  const { t } = useI18n();
  
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);
  const [initialDistance, setInitialDistance] = useState(0);
  const [currentScale, setCurrentScale] = useState(1);
  
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const overlayOpacity = useRef(new Animated.Value(0)).current;
  const previewTranslateY = useRef(new Animated.Value(screenHeight)).current;
  const previewScaleAnim = useRef(new Animated.Value(0.8)).current;

  const triggerHaptic = (type: 'light' | 'medium' | 'heavy' = 'medium') => {
    if (hapticFeedback) {
      switch (type) {
        case 'light':
          Vibration.vibrate(25);
          break;
        case 'medium':
          Vibration.vibrate(50);
          break;
        case 'heavy':
          Vibration.vibrate([0, 100, 50, 100]);
          break;
      }
    }
  };

  const getDistance = (touches: any[]) => {
    if (touches.length < 2) return 0;
    const [touch1, touch2] = touches;
    return Math.sqrt(
      Math.pow(touch2.pageX - touch1.pageX, 2) + 
      Math.pow(touch2.pageY - touch1.pageY, 2)
    );
  };

  const openPreview = () => {
    setIsPreviewOpen(true);
    triggerHaptic('heavy');
    onPreviewOpen?.();
    
    Animated.parallel([
      Animated.timing(overlayOpacity, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.spring(previewTranslateY, {
        toValue: 0,
        useNativeDriver: true,
        tension: 100,
        friction: 8,
      }),
      Animated.spring(previewScaleAnim, {
        toValue: 1,
        useNativeDriver: true,
        tension: 100,
        friction: 8,
      }),
    ]).start();
  };

  const closePreview = () => {
    onPreviewClose?.();
    
    Animated.parallel([
      Animated.timing(overlayOpacity, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.spring(previewTranslateY, {
        toValue: screenHeight,
        useNativeDriver: true,
        tension: 100,
        friction: 8,
      }),
      Animated.spring(previewScaleAnim, {
        toValue: 0.8,
        useNativeDriver: true,
        tension: 100,
        friction: 8,
      }),
    ]).start(() => {
      setIsPreviewOpen(false);
    });
  };

  const resetScale = () => {
    Animated.spring(scaleAnim, {
      toValue: 1,
      useNativeDriver: true,
      tension: 100,
      friction: 8,
    }).start();
    setCurrentScale(1);
  };

  const panResponder = PanResponder.create({
    onMoveShouldSetPanResponder: (evt, gestureState) => {
      return enablePinch && evt.nativeEvent.touches.length === 2;
    },

    onPanResponderGrant: (evt) => {
      if (evt.nativeEvent.touches.length === 2) {
        const distance = getDistance(evt.nativeEvent.touches);
        setInitialDistance(distance);
        scaleAnim.setOffset(currentScale);
        scaleAnim.setValue(0);
      }
    },

    onPanResponderMove: (evt, gestureState) => {
      if (evt.nativeEvent.touches.length === 2 && initialDistance > 0) {
        const currentDistance = getDistance(evt.nativeEvent.touches);
        const scale = currentDistance / initialDistance;
        const newScale = Math.max(0.5, Math.min(maxPreviewScale, scale));
        
        scaleAnim.setValue(newScale - currentScale);
        
        // Trigger preview when pinching out beyond threshold
        if (newScale > 1 + pinchThreshold && !isPreviewOpen) {
          openPreview();
        }
        
        // Provide haptic feedback at scale milestones
        if (hapticFeedback) {
          const scaleThresholds = [1.1, 1.3, 1.5];
          scaleThresholds.forEach(threshold => {
            if (newScale > threshold && currentScale <= threshold) {
              triggerHaptic('light');
            }
          });
        }
      }
    },

    onPanResponderRelease: () => {
      scaleAnim.flattenOffset();
      const finalScale = currentScale + scaleAnim._value;
      setCurrentScale(finalScale);
      
      if (finalScale < 1 - pinchThreshold) {
        resetScale();
      } else if (finalScale > 1 + pinchThreshold && !isPreviewOpen) {
        openPreview();
        resetScale();
      } else {
        resetScale();
      }
    },

    onPanResponderTerminate: () => {
      resetScale();
    },
  });

  const renderPreviewOverlay = () => {
    if (!isPreviewOpen) return null;

    return (
      <Animated.View
        style={[
          styles.previewOverlay,
          { opacity: overlayOpacity }
        ]}
      >
        <BlurView intensity={20} tint="dark" style={styles.blurBackground}>
          <RTLTouchableOpacity
            style={styles.overlayTouchable}
            onPress={closePreview}
            activeOpacity={1}
          />
          
          <Animated.View
            style={[
              styles.previewContainer,
              {
                transform: [
                  { translateY: previewTranslateY },
                  { scale: previewScaleAnim },
                ],
              },
            ]}
          >
            <LinearGradient
              colors={PREMIUM_GRADIENTS.elegantDepth}
              style={styles.previewGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
            >
              {/* Preview Header */}
              <RTLView style={styles.previewHeader}>
                <RTLView style={styles.previewHandle} />
                <RTLTouchableOpacity
                  style={styles.closeButton}
                  onPress={closePreview}
                >
                  <RTLIcon name="close" size={24} color="rgba(255, 255, 255, 0.8)" />
                </RTLTouchableOpacity>
              </RTLView>

              {/* Preview Content */}
              <RTLView style={styles.previewContent}>
                {previewContent || (
                  <FadeInView duration={400}>
                    <RTLView style={styles.defaultPreview}>
                      <RTLIcon name="eye-outline" size={48} color="#3B82F6" />
                      <RTLText style={styles.previewTitle}>
                        {t('gestures.quickPreview')}
                      </RTLText>
                      <RTLText style={styles.previewDescription}>
                        {t('gestures.pinchToPreviewDescription')}
                      </RTLText>
                    </RTLView>
                  </FadeInView>
                )}
              </RTLView>

              {/* Preview Actions */}
              <RTLView style={styles.previewActions}>
                <AnimatedPressable
                  style={styles.actionButton}
                  onPress={() => {
                    onPreviewAction?.();
                    closePreview();
                  }}
                >
                  <LinearGradient
                    colors={['#3B82F6', '#1D4ED8']}
                    style={styles.actionGradient}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 0 }}
                  >
                    <RTLText style={styles.actionText}>
                      {t('gestures.viewDetails')}
                    </RTLText>
                    <RTLIcon name="arrow-forward" size={16} color="#FFFFFF" />
                  </LinearGradient>
                </AnimatedPressable>
              </RTLView>
            </LinearGradient>
          </Animated.View>
        </BlurView>
      </Animated.View>
    );
  };

  return (
    <RTLView style={[styles.container, style]}>
      <Animated.View
        style={[
          styles.content,
          {
            transform: [{ scale: scaleAnim }],
          },
        ]}
        {...panResponder.panHandlers}
      >
        {children}
      </Animated.View>
      
      {renderPreviewOverlay()}
    </RTLView>
  );
};

const createStyles = (colors: ThemeColors) => StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
  previewOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1000,
  },
  blurBackground: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  overlayTouchable: {
    flex: 1,
  },
  previewContainer: {
    height: screenHeight * 0.7,
    borderTopLeftRadius: BORDER_RADIUS.xl,
    borderTopRightRadius: BORDER_RADIUS.xl,
    overflow: 'hidden',
  },
  previewGradient: {
    flex: 1,
    paddingTop: SPACING.md,
  },
  previewHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: SPACING.lg,
    marginBottom: SPACING.md,
  },
  previewHandle: {
    width: 40,
    height: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 2,
    alignSelf: 'center',
    flex: 1,
  },
  closeButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  previewContent: {
    flex: 1,
    paddingHorizontal: SPACING.lg,
  },
  defaultPreview: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  previewTitle: {
    fontSize: FONT_SIZES.xl,
    fontWeight: FONT_WEIGHTS.bold,
    color: '#FFFFFF',
    textAlign: 'center',
    marginTop: SPACING.lg,
    marginBottom: SPACING.md,
  },
  previewDescription: {
    fontSize: FONT_SIZES.md,
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
    lineHeight: 22,
  },
  previewActions: {
    paddingHorizontal: SPACING.lg,
    paddingBottom: SPACING.xl,
  },
  actionButton: {
    borderRadius: BORDER_RADIUS.lg,
    overflow: 'hidden',
  },
  actionGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.lg,
  },
  actionText: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.semiBold,
    color: '#FFFFFF',
    marginRight: SPACING.sm,
  },
});
