import React, { useEffect } from 'react';
import { StyleSheet, Dimensions } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withRepeat,
  withSequence,
  withSpring,
  interpolate,
  Extrapolation,
  withDelay,
} from 'react-native-reanimated';
import { RTLView } from '../RTL';
import { useThemedStyles } from '../../hooks';
import { SPACING } from '../../constants/theme';
import type ThemeColors from '../../contexts/ThemeContext';

const { width: screenWidth } = Dimensions.get('window');

export interface ReanimatedLoaderProps {
  type?: 'dots' | 'bars' | 'pulse' | 'wave' | 'spinner' | 'skeleton' | 'bounce' | 'elastic';
  size?: 'small' | 'medium' | 'large';
  color?: string;
  speed?: 'slow' | 'normal' | 'fast';
  style?: any;
}

export const ReanimatedLoader: React.FC<ReanimatedLoaderProps> = ({
  type = 'dots',
  size = 'medium',
  color = '#667eea',
  speed = 'normal',
  style,
}) => {
  const styles = useThemedStyles(createStyles);

  const getAnimationDuration = () => {
    switch (speed) {
      case 'slow':
        return 1500;
      case 'fast':
        return 800;
      default:
        return 1200;
    }
  };

  const getSize = () => {
    switch (size) {
      case 'small':
        return 20;
      case 'large':
        return 60;
      default:
        return 40;
    }
  };

  const renderLoader = () => {
    const duration = getAnimationDuration();
    const loaderSize = getSize();

    switch (type) {
      case 'dots':
        return <ReanimatedDotsLoader size={loaderSize} color={color} duration={duration} />;
      case 'bars':
        return <ReanimatedBarsLoader size={loaderSize} color={color} duration={duration} />;
      case 'pulse':
        return <ReanimatedPulseLoader size={loaderSize} color={color} duration={duration} />;
      case 'wave':
        return <ReanimatedWaveLoader size={loaderSize} color={color} duration={duration} />;
      case 'spinner':
        return <ReanimatedSpinnerLoader size={loaderSize} color={color} duration={duration} />;
      case 'skeleton':
        return <ReanimatedSkeletonLoader size={loaderSize} color={color} duration={duration} />;
      case 'bounce':
        return <ReanimatedBounceLoader size={loaderSize} color={color} duration={duration} />;
      case 'elastic':
        return <ReanimatedElasticLoader size={loaderSize} color={color} duration={duration} />;
      default:
        return <ReanimatedDotsLoader size={loaderSize} color={color} duration={duration} />;
    }
  };

  return (
    <RTLView style={[styles.container, style]}>
      {renderLoader()}
    </RTLView>
  );
};

// Reanimated Dots Loader
const ReanimatedDotsLoader: React.FC<{ size: number; color: string; duration: number }> = ({
  size,
  color,
  duration,
}) => {
  const styles = useThemedStyles(createStyles);
  const dot1 = useSharedValue(0);
  const dot2 = useSharedValue(0);
  const dot3 = useSharedValue(0);

  useEffect(() => {
    const animateDot = (sharedValue: any, delay: number) => {
      sharedValue.value = withDelay(
        delay,
        withRepeat(
          withSequence(
            withTiming(1, { duration: duration / 3 }),
            withTiming(0, { duration: duration / 3 })
          ),
          -1,
          false
        )
      );
    };

    animateDot(dot1, 0);
    animateDot(dot2, duration / 6);
    animateDot(dot3, duration / 3);
  }, [duration]);

  const dotSize = size / 4;

  const createDotStyle = (sharedValue: any) => useAnimatedStyle(() => ({
    opacity: interpolate(sharedValue.value, [0, 1], [0.3, 1], Extrapolation.CLAMP),
    transform: [
      {
        scale: interpolate(sharedValue.value, [0, 1], [0.8, 1.2], Extrapolation.CLAMP),
      },
    ],
  }));

  return (
    <RTLView style={styles.dotsContainer}>
      <Animated.View
        style={[
          styles.dot,
          {
            width: dotSize,
            height: dotSize,
            backgroundColor: color,
            borderRadius: dotSize / 2,
          },
          createDotStyle(dot1),
        ]}
      />
      <Animated.View
        style={[
          styles.dot,
          {
            width: dotSize,
            height: dotSize,
            backgroundColor: color,
            borderRadius: dotSize / 2,
          },
          createDotStyle(dot2),
        ]}
      />
      <Animated.View
        style={[
          styles.dot,
          {
            width: dotSize,
            height: dotSize,
            backgroundColor: color,
            borderRadius: dotSize / 2,
          },
          createDotStyle(dot3),
        ]}
      />
    </RTLView>
  );
};

// Reanimated Spinner Loader
const ReanimatedSpinnerLoader: React.FC<{ size: number; color: string; duration: number }> = ({
  size,
  color,
  duration,
}) => {
  const styles = useThemedStyles(createStyles);
  const rotation = useSharedValue(0);

  useEffect(() => {
    rotation.value = withRepeat(
      withTiming(360, { duration }),
      -1,
      false
    );
  }, [duration]);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ rotate: `${rotation.value}deg` }],
  }));

  return (
    <Animated.View
      style={[
        styles.spinner,
        {
          width: size,
          height: size,
          borderRadius: size / 2,
          borderWidth: size / 10,
          borderColor: `${color}20`,
          borderTopColor: color,
        },
        animatedStyle,
      ]}
    />
  );
};

// Reanimated Pulse Loader
const ReanimatedPulseLoader: React.FC<{ size: number; color: string; duration: number }> = ({
  size,
  color,
  duration,
}) => {
  const styles = useThemedStyles(createStyles);
  const scale = useSharedValue(1);
  const opacity = useSharedValue(1);

  useEffect(() => {
    scale.value = withRepeat(
      withSequence(
        withTiming(1.2, { duration: duration / 2 }),
        withTiming(1, { duration: duration / 2 })
      ),
      -1,
      false
    );

    opacity.value = withRepeat(
      withSequence(
        withTiming(0.3, { duration: duration / 2 }),
        withTiming(1, { duration: duration / 2 })
      ),
      -1,
      false
    );
  }, [duration]);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
    opacity: opacity.value,
  }));

  return (
    <Animated.View
      style={[
        styles.pulse,
        {
          width: size,
          height: size,
          backgroundColor: color,
          borderRadius: size / 2,
        },
        animatedStyle,
      ]}
    />
  );
};

// Reanimated Bounce Loader
const ReanimatedBounceLoader: React.FC<{ size: number; color: string; duration: number }> = ({
  size,
  color,
  duration,
}) => {
  const styles = useThemedStyles(createStyles);
  const translateY = useSharedValue(0);

  useEffect(() => {
    translateY.value = withRepeat(
      withSequence(
        withSpring(-size / 2, { damping: 10, stiffness: 200 }),
        withSpring(0, { damping: 10, stiffness: 200 })
      ),
      -1,
      false
    );
  }, [duration, size]);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ translateY: translateY.value }],
  }));

  return (
    <Animated.View
      style={[
        styles.bounce,
        {
          width: size,
          height: size,
          backgroundColor: color,
          borderRadius: size / 2,
        },
        animatedStyle,
      ]}
    />
  );
};

// Reanimated Bars Loader
const ReanimatedBarsLoader: React.FC<{ size: number; color: string; duration: number }> = ({
  size,
  color,
  duration,
}) => {
  const styles = useThemedStyles(createStyles);
  const bar1 = useSharedValue(0);
  const bar2 = useSharedValue(0);
  const bar3 = useSharedValue(0);
  const bar4 = useSharedValue(0);

  useEffect(() => {
    const animateBar = (sharedValue: any, delay: number) => {
      sharedValue.value = withDelay(
        delay,
        withRepeat(
          withSequence(
            withTiming(1, { duration: duration / 4 }),
            withTiming(0, { duration: duration / 4 })
          ),
          -1,
          false
        )
      );
    };

    animateBar(bar1, 0);
    animateBar(bar2, duration / 8);
    animateBar(bar3, duration / 4);
    animateBar(bar4, duration * 3 / 8);
  }, [duration]);

  const createBarStyle = (sharedValue: any) => useAnimatedStyle(() => ({
    height: interpolate(sharedValue.value, [0, 1], [size / 4, size], Extrapolation.CLAMP),
  }));

  const barWidth = size / 6;

  return (
    <RTLView style={styles.barsContainer}>
      {[bar1, bar2, bar3, bar4].map((bar, index) => (
        <Animated.View
          key={index}
          style={[
            styles.bar,
            {
              width: barWidth,
              backgroundColor: color,
              borderRadius: barWidth / 2,
            },
            createBarStyle(bar),
          ]}
        />
      ))}
    </RTLView>
  );
};

// Reanimated Wave Loader
const ReanimatedWaveLoader: React.FC<{ size: number; color: string; duration: number }> = ({
  size,
  color,
  duration,
}) => {
  const styles = useThemedStyles(createStyles);
  const waves = [useSharedValue(0), useSharedValue(0), useSharedValue(0), useSharedValue(0)];

  useEffect(() => {
    waves.forEach((wave, index) => {
      wave.value = withDelay(
        index * (duration / 8),
        withRepeat(
          withTiming(1, { duration }),
          -1,
          false
        )
      );
    });
  }, [duration]);

  const createWaveStyle = (sharedValue: any) => useAnimatedStyle(() => ({
    opacity: interpolate(sharedValue.value, [0, 0.5, 1], [0.3, 1, 0.3], Extrapolation.CLAMP),
    transform: [
      {
        scale: interpolate(sharedValue.value, [0, 1], [0.5, 1.5], Extrapolation.CLAMP),
      },
    ],
  }));

  return (
    <RTLView style={styles.waveContainer}>
      {waves.map((wave, index) => (
        <Animated.View
          key={index}
          style={[
            styles.wave,
            {
              width: size / 2,
              height: size / 2,
              borderRadius: size / 4,
              borderWidth: 2,
              borderColor: color,
              position: 'absolute',
            },
            createWaveStyle(wave),
          ]}
        />
      ))}
    </RTLView>
  );
};

// Reanimated Skeleton Loader
const ReanimatedSkeletonLoader: React.FC<{ size: number; color: string; duration: number }> = ({
  size,
  color,
  duration,
}) => {
  const styles = useThemedStyles(createStyles);
  const translateX = useSharedValue(-size * 2);

  useEffect(() => {
    translateX.value = withRepeat(
      withTiming(size * 2, { duration }),
      -1,
      false
    );
  }, [duration, size]);

  const shimmerStyle = useAnimatedStyle(() => ({
    transform: [{ translateX: translateX.value }],
  }));

  return (
    <RTLView style={[styles.skeleton, { width: size * 3, height: size / 2 }]}>
      <Animated.View
        style={[
          styles.skeletonShimmer,
          {
            backgroundColor: color,
            opacity: 0.3,
            width: size,
            height: size / 2,
          },
          shimmerStyle,
        ]}
      />
    </RTLView>
  );
};

// Reanimated Elastic Loader
const ReanimatedElasticLoader: React.FC<{ size: number; color: string; duration: number }> = ({
  size,
  color,
  duration,
}) => {
  const styles = useThemedStyles(createStyles);
  const scaleX = useSharedValue(1);
  const scaleY = useSharedValue(1);

  useEffect(() => {
    scaleX.value = withRepeat(
      withSequence(
        withSpring(1.5, { damping: 5, stiffness: 100 }),
        withSpring(1, { damping: 5, stiffness: 100 })
      ),
      -1,
      false
    );

    scaleY.value = withRepeat(
      withSequence(
        withSpring(0.5, { damping: 5, stiffness: 100 }),
        withSpring(1, { damping: 5, stiffness: 100 })
      ),
      -1,
      false
    );
  }, [duration]);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [
      { scaleX: scaleX.value },
      { scaleY: scaleY.value },
    ],
  }));

  return (
    <Animated.View
      style={[
        styles.elastic,
        {
          width: size,
          height: size,
          backgroundColor: color,
          borderRadius: size / 4,
        },
        animatedStyle,
      ]}
    />
  );
};

const createStyles = (theme: ThemeColors) => StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  dotsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  dot: {
    marginHorizontal: SPACING.xs / 2,
  },
  barsContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    justifyContent: 'center',
    height: 40,
  },
  bar: {
    marginHorizontal: 2,
  },
  waveContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    width: 60,
    height: 60,
  },
  wave: {
    // Wave styles handled inline
  },
  skeleton: {
    backgroundColor: `${theme.border}40`,
    borderRadius: 4,
    overflow: 'hidden',
  },
  skeletonShimmer: {
    borderRadius: 4,
  },
  spinner: {
    // Spinner styles handled inline
  },
  pulse: {
    // Pulse styles handled inline
  },
  bounce: {
    // Bounce styles handled inline
  },
  elastic: {
    // Elastic styles handled inline
  },
});
