import { useMemo } from 'react';
import { useData } from '../contexts/DataContext';
import { VENDOR_STATUS, type VendorStatus  } from '../constants';
import type Vendor  from '../contexts/DataContext';

export const useVendors = () => {
  const {
    vendors,
    approveVendor,
    rejectVendor,
    getVendorById,
    getVendorProducts,
    getVendorOrders,
    isLoading,
    error,
  } = useData();

  // Memoized vendor statistics
  const vendorStats = useMemo(() => {
    const total = vendors.length;
    const approved = vendors.filter(v => v.status === VENDOR_STATUS.APPROVED).length;
    const pending = vendors.filter(v => v.status === VENDOR_STATUS.PENDING).length;
    const rejected = vendors.filter(v => v.status === VENDOR_STATUS.REJECTED).length;
    const suspended = vendors.filter(v => v.status === VENDOR_STATUS.SUSPENDED).length;

    return {
      total,
      approved,
      pending,
      rejected,
      suspended,
      approvalRate: total > 0 ? (approved / total) * 100 : 0,
    };
  }, [vendors]);

  // Get vendors by status
  const getVendorsByStatus = (status: VendorStatus): Vendor[] => {
    return vendors.filter(vendor => vendor.status === status);
  };

  // Get approved vendors
  const getApprovedVendors = (): Vendor[] => {
    return getVendorsByStatus(VENDOR_STATUS.APPROVED);
  };

  // Get top vendors by revenue
  const getTopVendorsByRevenue = (limit: number = 10): Vendor[] => {
    return [...vendors]
      .sort((a, b) => b.revenue - a.revenue)
      .slice(0, limit);
  };

  // Get top vendors by rating
  const getTopVendorsByRating = (limit: number = 10): Vendor[] => {
    return [...vendors]
      .sort((a, b) => b.rating - a.rating)
      .slice(0, limit);
  };

  // Search vendors
  const searchVendors = (query: string): Vendor[] => {
    const lowercaseQuery = query.toLowerCase();
    return vendors.filter(vendor =>
      vendor.businessName.toLowerCase().includes(lowercaseQuery) ||
      vendor.businessDescription.toLowerCase().includes(lowercaseQuery) ||
      vendor.ownerName.toLowerCase().includes(lowercaseQuery) ||
      vendor.email.toLowerCase().includes(lowercaseQuery)
    );
  };

  // Get vendor performance metrics
  const getVendorPerformance = (vendorId: string) => {
    const vendor = getVendorById(vendorId);
    const products = getVendorProducts(vendorId);
    const orders = getVendorOrders(vendorId);

    if (!vendor) return null;

    const totalRevenue = orders.reduce((sum, order) => sum + order.totalAmount, 0);
    const averageOrderValue = orders.length > 0 ? totalRevenue / orders.length : 0;
    const activeProducts = products.filter(p => p.isActive).length;
    const totalProducts = products.length;

    return {
      vendor,
      totalRevenue,
      totalOrders: orders.length,
      averageOrderValue,
      activeProducts,
      totalProducts,
      rating: vendor.rating,
      productActivationRate: totalProducts > 0 ? (activeProducts / totalProducts) * 100 : 0,
    };
  };

  // Get recent vendors (last 30 days)
  const getRecentVendors = (): Vendor[] => {
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    return vendors.filter(vendor => {
      const createdAt = new Date(vendor.createdAt);
      return createdAt >= thirtyDaysAgo;
    });
  };

  // Bulk approve vendors
  const bulkApproveVendors = async (vendorIds: string[]): Promise<void> => {
    const promises = vendorIds.map(id => approveVendor(id));
    await Promise.all(promises);
  };

  // Bulk reject vendors
  const bulkRejectVendors = async (vendorIds: string[]): Promise<void> => {
    const promises = vendorIds.map(id => rejectVendor(id));
    await Promise.all(promises);
  };

  return {
    // Data
    vendors,
    vendorStats,
    isLoading,
    error,

    // Methods
    approveVendor,
    rejectVendor,
    bulkApproveVendors,
    bulkRejectVendors,
    getVendorById,
    getVendorsByStatus,
    getApprovedVendors,
    getTopVendorsByRevenue,
    getTopVendorsByRating,
    searchVendors,
    getVendorPerformance,
    getRecentVendors,
    getVendorProducts,
    getVendorOrders,
  };
};
