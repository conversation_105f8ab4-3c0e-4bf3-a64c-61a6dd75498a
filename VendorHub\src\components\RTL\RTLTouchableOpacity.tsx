import React, { useMemo } from 'react';
import { TouchableOpacity, TouchableOpacityProps, StyleSheet, ViewStyle } from 'react-native';
import { useI18n } from '../../hooks/useI18n';

interface RTLTouchableOpacityProps extends TouchableOpacityProps {
  style?: ViewStyle | ViewStyle[];
  children?: React.ReactNode;
  disableRTL?: boolean; // Option to disable RTL transformation for specific cases
}

export const RTLTouchableOpacity: React.FC<RTLTouchableOpacityProps> = ({
  style,
  children,
  disableRTL = false,
  ...props
}) => {
  const { isRTL } = useI18n();

  const rtlStyle = useMemo(() => {
    if (!style || !isRTL || disableRTL) return style;
    
    // Create RTL version of the style
    const flattenedStyle = StyleSheet.flatten(style);
    const rtlFlattenedStyle = { ...flattenedStyle };
    
    // Flip flexDirection
    if (flattenedStyle.flexDirection === 'row') {
      rtlFlattenedStyle.flexDirection = 'row-reverse';
    } else if (flattenedStyle.flexDirection === 'row-reverse') {
      rtlFlattenedStyle.flexDirection = 'row';
    }
    
    // Flip padding
    if (flattenedStyle.paddingLeft !== undefined) {
      rtlFlattenedStyle.paddingRight = flattenedStyle.paddingLeft;
      delete rtlFlattenedStyle.paddingLeft;
    }
    if (flattenedStyle.paddingRight !== undefined) {
      rtlFlattenedStyle.paddingLeft = flattenedStyle.paddingRight;
      delete rtlFlattenedStyle.paddingRight;
    }
    
    // Flip margin
    if (flattenedStyle.marginLeft !== undefined) {
      rtlFlattenedStyle.marginRight = flattenedStyle.marginLeft;
      delete rtlFlattenedStyle.marginLeft;
    }
    if (flattenedStyle.marginRight !== undefined) {
      rtlFlattenedStyle.marginLeft = flattenedStyle.marginRight;
      delete rtlFlattenedStyle.marginRight;
    }
    
    // Flip positioning
    if (flattenedStyle.left !== undefined) {
      rtlFlattenedStyle.right = flattenedStyle.left;
      delete rtlFlattenedStyle.left;
    }
    if (flattenedStyle.right !== undefined) {
      rtlFlattenedStyle.left = flattenedStyle.right;
      delete rtlFlattenedStyle.right;
    }
    
    return rtlFlattenedStyle;
  }, [style, isRTL, disableRTL]);

  return (
    <TouchableOpacity style={rtlStyle} {...props}>
      {children}
    </TouchableOpacity>
  );
};
