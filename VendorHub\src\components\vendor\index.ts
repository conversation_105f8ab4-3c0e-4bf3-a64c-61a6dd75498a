// Enhanced Vendor Discovery Components
export { HeroCarousel } from './HeroCarousel';
export { CategoryExploration } from './CategoryExploration';
export { TrendingShops } from './TrendingShops';
export { CuratedCollections } from './CuratedCollections';
export { EnhancedVendorCard } from './EnhancedVendorCard';

// Intelligent Shop Presentation Components
export { VendorPreviewModal } from './VendorPreviewModal';
export { VendorStoryPreview } from './VendorStoryPreview';
export { LiveActivityIndicators } from './LiveActivityIndicators';

// Re-export types
export type { ThemeColors } from '../../contexts/ThemeContext';
export type { Vendor } from '../../contexts/DataContext';
