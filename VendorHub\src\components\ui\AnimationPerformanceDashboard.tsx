import React, { useState, useEffect } from 'react';
import { StyleSheet, Platform } from 'react-native';
import { RTLView, RTLText, RTLScrollView } from '../RTL';
import { ReanimatedButton } from './ReanimatedButton';
import { useThemedStyles } from '../../hooks';
import { 
  animationMonitor, 
  animationPool,
  getRecommendedAnimationSettings,
  OptimizedEasing,
  PerformanceReport 
} from '../../utils/animationPerformance';
import { SPACING } from '../../constants/theme';
import type ThemeColors from '../../contexts/ThemeContext';

export interface AnimationPerformanceDashboardProps {
  onReportGenerated?: (report: PerformanceReport) => void;
}

export const AnimationPerformanceDashboard: React.FC<AnimationPerformanceDashboardProps> = ({
  onReportGenerated,
}) => {
  const styles = useThemedStyles(createStyles);
  const [isMonitoring, setIsMonitoring] = useState(false);
  const [report, setReport] = useState<PerformanceReport | null>(null);
  const [poolStats, setPoolStats] = useState<Record<string, number>>({});
  const [recommendedSettings] = useState(getRecommendedAnimationSettings());

  useEffect(() => {
    const interval = setInterval(() => {
      setPoolStats(animationPool.getPoolStats());
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  const startMonitoring = () => {
    animationMonitor.startMonitoring();
    setIsMonitoring(true);
    setReport(null);
  };

  const stopMonitoring = () => {
    const generatedReport = animationMonitor.stopMonitoring();
    setIsMonitoring(false);
    setReport(generatedReport);
    onReportGenerated?.(generatedReport);
  };

  const clearData = () => {
    animationMonitor.clearMetrics();
    animationPool.clearPool();
    setReport(null);
    setPoolStats({});
  };

  const getPerformanceColor = (value: number, threshold: number, inverse: boolean = false) => {
    const isGood = inverse ? value < threshold : value > threshold;
    return isGood ? '#4CAF50' : value > threshold * 0.7 ? '#FF9800' : '#F44336';
  };

  const renderMetricCard = (title: string, value: string | number, color?: string) => (
    <RTLView style={[styles.metricCard, color && { borderLeftColor: color }]}>
      <RTLText style={styles.metricTitle}>{title}</RTLText>
      <RTLText style={[styles.metricValue, color && { color }]}>
        {typeof value === 'number' ? value.toFixed(2) : value}
      </RTLText>
    </RTLView>
  );

  const renderRecommendations = () => {
    if (!report?.recommendations.length) return null;

    return (
      <RTLView style={styles.section}>
        <RTLText style={styles.sectionTitle}>🎯 Recommendations</RTLText>
        {report.recommendations.map((recommendation, index) => (
          <RTLView key={index} style={styles.recommendationItem}>
            <RTLText style={styles.recommendationText}>• {recommendation}</RTLText>
          </RTLView>
        ))}
      </RTLView>
    );
  };

  const renderPoolStats = () => {
    const poolEntries = Object.entries(poolStats);
    if (poolEntries.length === 0) return null;

    return (
      <RTLView style={styles.section}>
        <RTLText style={styles.sectionTitle}>🔄 Animation Pool</RTLText>
        <RTLView style={styles.poolGrid}>
          {poolEntries.map(([type, count]) => (
            <RTLView key={type} style={styles.poolItem}>
              <RTLText style={styles.poolType}>{type}</RTLText>
              <RTLText style={styles.poolCount}>{count}</RTLText>
            </RTLView>
          ))}
        </RTLView>
      </RTLView>
    );
  };

  const renderOptimizedEasing = () => (
    <RTLView style={styles.section}>
      <RTLText style={styles.sectionTitle}>⚡ Optimized Easing</RTLText>
      <RTLView style={styles.easingGrid}>
        {Object.entries(OptimizedEasing).map(([name, config]) => (
          <RTLView key={name} style={styles.easingItem}>
            <RTLText style={styles.easingName}>{name}</RTLText>
            <RTLText style={styles.easingDuration}>{config.duration}ms</RTLText>
          </RTLView>
        ))}
      </RTLView>
    </RTLView>
  );

  const renderTestButtons = () => (
    <RTLView style={styles.section}>
      <RTLText style={styles.sectionTitle}>🧪 Test Animations</RTLText>
      <RTLView style={styles.buttonGrid}>
        <ReanimatedButton
          title="Scale"
          animationType="scale"
          size="small"
          style={styles.testButton}
        />
        <ReanimatedButton
          title="Bounce"
          animationType="bounce"
          size="small"
          style={styles.testButton}
        />
        <ReanimatedButton
          title="Pulse"
          animationType="pulse"
          size="small"
          style={styles.testButton}
        />
        <ReanimatedButton
          title="Shake"
          animationType="shake"
          size="small"
          style={styles.testButton}
        />
        <ReanimatedButton
          title="Elastic"
          animationType="elastic"
          size="small"
          style={styles.testButton}
        />
        <ReanimatedButton
          title="Wobble"
          animationType="wobble"
          size="small"
          style={styles.testButton}
        />
      </RTLView>
    </RTLView>
  );

  return (
    <RTLScrollView style={styles.container}>
      <RTLView style={styles.header}>
        <RTLText style={styles.title}>Animation Performance Dashboard</RTLText>
        <RTLText style={styles.subtitle}>
          Platform: {Platform.OS} | Monitoring: {isMonitoring ? 'Active' : 'Inactive'}
        </RTLText>
      </RTLView>

      <RTLView style={styles.controls}>
        <ReanimatedButton
          title={isMonitoring ? 'Stop Monitoring' : 'Start Monitoring'}
          onPress={isMonitoring ? stopMonitoring : startMonitoring}
          variant={isMonitoring ? 'secondary' : 'primary'}
          animationType="pulse"
          style={styles.controlButton}
        />
        <ReanimatedButton
          title="Clear Data"
          onPress={clearData}
          variant="outline"
          animationType="shake"
          style={styles.controlButton}
        />
      </RTLView>

      {report && (
        <RTLView style={styles.section}>
          <RTLText style={styles.sectionTitle}>📊 Performance Report</RTLText>
          <RTLView style={styles.metricsGrid}>
            {renderMetricCard(
              'Total Animations',
              report.totalAnimations,
              getPerformanceColor(report.totalAnimations, 10)
            )}
            {renderMetricCard(
              'Avg Duration (ms)',
              report.averageDuration,
              getPerformanceColor(report.averageDuration, 300, true)
            )}
            {renderMetricCard(
              'Frame Drop Rate (%)',
              report.frameDropRate,
              getPerformanceColor(report.frameDropRate, 5, true)
            )}
            {renderMetricCard(
              'Native Driver Usage (%)',
              report.nativeDriverUsage,
              getPerformanceColor(report.nativeDriverUsage, 80)
            )}
            {renderMetricCard(
              'Memory Peak (MB)',
              report.memoryPeak,
              getPerformanceColor(report.memoryPeak, 50, true)
            )}
          </RTLView>
        </RTLView>
      )}

      {renderRecommendations()}
      {renderPoolStats()}

      <RTLView style={styles.section}>
        <RTLText style={styles.sectionTitle}>⚙️ Recommended Settings</RTLText>
        <RTLView style={styles.settingsGrid}>
          {renderMetricCard('Complex Animations', recommendedSettings.enableComplexAnimations ? 'Enabled' : 'Disabled')}
          {renderMetricCard('Max Concurrent', recommendedSettings.maxConcurrentAnimations)}
          {renderMetricCard('Default Duration (ms)', recommendedSettings.defaultDuration)}
          {renderMetricCard('Particle Effects', recommendedSettings.enableParticleEffects ? 'Enabled' : 'Disabled')}
          {renderMetricCard('Shadow Animations', recommendedSettings.enableShadowAnimations ? 'Enabled' : 'Disabled')}
        </RTLView>
      </RTLView>

      {renderOptimizedEasing()}
      {renderTestButtons()}
    </RTLScrollView>
  );
};

const createStyles = (theme: ThemeColors) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.background,
  },
  header: {
    padding: SPACING.lg,
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: theme.border,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.text,
    marginBottom: SPACING.xs,
  },
  subtitle: {
    fontSize: 14,
    color: theme.textSecondary,
  },
  controls: {
    flexDirection: 'row',
    padding: SPACING.lg,
    gap: SPACING.md,
  },
  controlButton: {
    flex: 1,
  },
  section: {
    padding: SPACING.lg,
    borderBottomWidth: 1,
    borderBottomColor: theme.border,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.text,
    marginBottom: SPACING.md,
  },
  metricsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: SPACING.sm,
  },
  metricCard: {
    flex: 1,
    minWidth: '45%',
    padding: SPACING.md,
    backgroundColor: theme.surface,
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: theme.primary,
  },
  metricTitle: {
    fontSize: 12,
    color: theme.textSecondary,
    marginBottom: SPACING.xs,
  },
  metricValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.text,
  },
  recommendationItem: {
    paddingVertical: SPACING.xs,
  },
  recommendationText: {
    fontSize: 14,
    color: theme.text,
    lineHeight: 20,
  },
  poolGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: SPACING.sm,
  },
  poolItem: {
    padding: SPACING.sm,
    backgroundColor: theme.surface,
    borderRadius: 6,
    alignItems: 'center',
  },
  poolType: {
    fontSize: 12,
    color: theme.textSecondary,
  },
  poolCount: {
    fontSize: 16,
    fontWeight: 'bold',
    color: theme.primary,
  },
  settingsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: SPACING.sm,
  },
  easingGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: SPACING.sm,
  },
  easingItem: {
    padding: SPACING.sm,
    backgroundColor: theme.surface,
    borderRadius: 6,
    alignItems: 'center',
  },
  easingName: {
    fontSize: 12,
    color: theme.textSecondary,
    textTransform: 'capitalize',
  },
  easingDuration: {
    fontSize: 14,
    fontWeight: 'bold',
    color: theme.primary,
  },
  buttonGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: SPACING.sm,
  },
  testButton: {
    flex: 1,
    minWidth: '30%',
  },
});
