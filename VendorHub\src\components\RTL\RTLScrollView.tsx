import React, { useMemo, useRef, useEffect, useState } from 'react';
import { ScrollView, ScrollViewProps, StyleSheet, ViewStyle, LayoutChangeEvent } from 'react-native';
import { useI18n } from '../../hooks/useI18n';

interface RTLScrollViewProps extends ScrollViewProps {
  style?: ViewStyle | ViewStyle[];
  contentContainerStyle?: ViewStyle | ViewStyle[];
  enableRTLScrolling?: boolean; // Enable automatic RTL scrolling behavior
}

export const RTLScrollView: React.FC<RTLScrollViewProps> = ({
  style,
  contentContainerStyle,
  horizontal,
  enableRTLScrolling = true,
  children,
  ...props
}) => {
  const { isRTL } = useI18n();
  const scrollViewRef = useRef<ScrollView>(null);
  const [scrollViewWidth, setScrollViewWidth] = useState(0);
  const [contentWidth, setContentWidth] = useState(0);
  const [hasInitializedScroll, setHasInitializedScroll] = useState(false);
  
  const rtlStyle = useMemo(() => {
    if (!style || !isRTL) return style;
    
    const flattenedStyle = StyleSheet.flatten(style);
    const rtlFlattenedStyle = { ...flattenedStyle };
    
    // Flip padding
    if (flattenedStyle.paddingLeft !== undefined) {
      rtlFlattenedStyle.paddingRight = flattenedStyle.paddingLeft;
      delete rtlFlattenedStyle.paddingLeft;
    }
    if (flattenedStyle.paddingRight !== undefined) {
      rtlFlattenedStyle.paddingLeft = flattenedStyle.paddingRight;
      delete rtlFlattenedStyle.paddingRight;
    }
    
    // Flip margin
    if (flattenedStyle.marginLeft !== undefined) {
      rtlFlattenedStyle.marginRight = flattenedStyle.marginLeft;
      delete rtlFlattenedStyle.marginLeft;
    }
    if (flattenedStyle.marginRight !== undefined) {
      rtlFlattenedStyle.marginLeft = flattenedStyle.marginRight;
      delete rtlFlattenedStyle.marginRight;
    }
    
    return rtlFlattenedStyle;
  }, [style, isRTL]);

  const rtlContentContainerStyle = useMemo(() => {
    if (!contentContainerStyle) {
      // Default styles for horizontal scrolling
      if (horizontal) {
        return {
          flexDirection: isRTL ? 'row-reverse' : 'row',
          justifyContent: 'flex-start',
        };
      }
      return contentContainerStyle;
    }

    const flattenedStyle = StyleSheet.flatten(contentContainerStyle);
    const rtlFlattenedStyle = { ...flattenedStyle };

    // For horizontal scrolling, set proper flex direction
    if (horizontal) {
      if (isRTL) {
        // In RTL mode, use row-reverse so first item appears on the right
        rtlFlattenedStyle.flexDirection = 'row-reverse';
      } else {
        // In LTR mode, use normal row direction
        rtlFlattenedStyle.flexDirection = 'row';
      }

      // Ensure proper justification
      if (!flattenedStyle.justifyContent) {
        rtlFlattenedStyle.justifyContent = 'flex-start';
      }
    }

    // Flip padding for RTL
    if (isRTL) {
      if (flattenedStyle.paddingLeft !== undefined) {
        rtlFlattenedStyle.paddingRight = flattenedStyle.paddingLeft;
        delete rtlFlattenedStyle.paddingLeft;
      }
      if (flattenedStyle.paddingRight !== undefined) {
        rtlFlattenedStyle.paddingLeft = flattenedStyle.paddingRight;
        delete rtlFlattenedStyle.paddingRight;
      }
    }

    return rtlFlattenedStyle;
  }, [contentContainerStyle, isRTL, horizontal]);

  // Handle initial scroll position for RTL horizontal scrolling
  useEffect(() => {
    if (
      isRTL &&
      horizontal &&
      enableRTLScrolling &&
      scrollViewWidth > 0 &&
      contentWidth > scrollViewWidth &&
      !hasInitializedScroll
    ) {
      // Calculate the initial scroll position to start at the rightmost position
      const initialScrollX = contentWidth - scrollViewWidth;

      // Use a small delay to ensure the layout is complete
      setTimeout(() => {
        scrollViewRef.current?.scrollTo({
          x: initialScrollX,
          y: 0,
          animated: false, // Don't animate the initial positioning
        });
        setHasInitializedScroll(true);
      }, 50);
    }
  }, [isRTL, horizontal, enableRTLScrolling, scrollViewWidth, contentWidth, hasInitializedScroll]);

  // Handle layout changes to get dimensions
  const handleScrollViewLayout = (event: LayoutChangeEvent) => {
    const { width } = event.nativeEvent.layout;
    setScrollViewWidth(width);

    // Call the original onLayout if provided
    if (props.onLayout) {
      props.onLayout(event);
    }
  };

  const handleContentSizeChange = (width: number, height: number) => {
    if (horizontal) {
      setContentWidth(width);
    }

    // Call the original onContentSizeChange if provided
    if (props.onContentSizeChange) {
      props.onContentSizeChange(width, height);
    }
  };

  // Reset initialization flag when RTL mode changes
  useEffect(() => {
    setHasInitializedScroll(false);
  }, [isRTL]);

  return (
    <ScrollView
      ref={scrollViewRef}
      style={rtlStyle}
      contentContainerStyle={rtlContentContainerStyle}
      horizontal={horizontal}
      onLayout={handleScrollViewLayout}
      onContentSizeChange={handleContentSizeChange}
      {...props}
    >
      {children}
    </ScrollView>
  );
};
