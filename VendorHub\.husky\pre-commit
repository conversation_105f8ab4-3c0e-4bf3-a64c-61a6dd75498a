#!/bin/sh
. "$(dirname "$0")/_/husky.sh"

# Run RTL validation tests
echo "🔍 Running RTL validation..."
node VendorHub/scripts/comprehensive-rtl-inspector.js --ci

# Check if RTL validation failed
if [ $? -ne 0 ]; then
  echo "❌ RTL validation failed. Please fix the issues before committing."
  exit 1
fi

# Run Arabic translation inspector
echo "🔍 Running Arabic translation validation..."
node VendorHub/scripts/arabic-translation-inspector.js --ci

# Check if Arabic translation validation failed
if [ $? -ne 0 ]; then
  echo "❌ Arabic translation validation failed. Please fix the issues before committing."
  exit 1
fi

# Run RTL functionality tests
echo "🔍 Running RTL functionality tests..."
node VendorHub/run-rtl-tests.js --ci

# Check if RTL functionality tests failed
if [ $? -ne 0 ]; then
  echo "❌ RTL functionality tests failed. Please fix the issues before committing."
  exit 1
fi

echo "✅ RTL validation passed. Proceeding with commit."
