import { VALIDATION, IMAGE_CONSTRAINTS, CURRENCY } from '../constants';

// Validation utilities
export const validateEmail = (email: string): boolean => {
  return VALIDATION.EMAIL_REGEX.test(email.trim());
};

export const validatePassword = (password: string): boolean => {
  return password.length >= VALIDATION.PASSWORD_MIN_LENGTH;
};

export const validatePhone = (phone: string): boolean => {
  return VALIDATION.PHONE_REGEX.test(phone.trim());
};

export const validateBusinessName = (name: string): boolean => {
  const trimmedName = name.trim();
  return trimmedName.length >= VALIDATION.BUSINESS_NAME_MIN_LENGTH && 
         trimmedName.length <= VALIDATION.BUSINESS_NAME_MAX_LENGTH;
};

export const validateProductName = (name: string): boolean => {
  const trimmedName = name.trim();
  return trimmedName.length >= VALIDATION.PRODUCT_NAME_MIN_LENGTH && 
         trimmedName.length <= VALIDATION.PRODUCT_NAME_MAX_LENGTH;
};

// Formatting utilities - Always use BHD currency with RTL support
export const formatCurrency = (amount: number, options?: {
  isRTL?: boolean;
  locale?: string;
  showSymbol?: boolean;
}): string => {
  const {
    isRTL = false,
    locale = isRTL ? 'ar-BH' : 'en-BH',
    showSymbol = true
  } = options || {};

  try {
    // Use appropriate locale for RTL/LTR formatting
    const formatter = new Intl.NumberFormat(locale, {
      style: showSymbol ? 'currency' : 'decimal',
      currency: CURRENCY.CODE,
      minimumFractionDigits: CURRENCY.DECIMAL_PLACES,
      maximumFractionDigits: CURRENCY.DECIMAL_PLACES,
      currencyDisplay: 'symbol', // Use symbol instead of code
    });

    const formatted = formatter.format(amount);

    // For RTL, ensure proper text direction
    if (isRTL && showSymbol) {
      // Arabic formatting: ensure currency symbol appears correctly
      // The Intl.NumberFormat should handle this, but we can add fallback
      return formatted;
    }

    return formatted;
  } catch (error) {
    // Enhanced fallback formatting with RTL support
    const formattedNumber = amount.toFixed(CURRENCY.DECIMAL_PLACES);

    if (!showSymbol) {
      return formattedNumber;
    }

    if (isRTL) {
      // For RTL: number + space + currency symbol
      return `${formattedNumber} ${CURRENCY.SYMBOL}`;
    } else {
      // For LTR: currency symbol + space + number
      return `${CURRENCY.SYMBOL} ${formattedNumber}`;
    }
  }
};

export const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M';
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K';
  }
  return num.toString();
};

export const formatDate = (date: Date | string): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return dateObj.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  });
};

export const formatDateTime = (date: Date | string): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return dateObj.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
};

export const formatRelativeTime = (date: Date | string): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000);

  if (diffInSeconds < 60) {
    return 'Just now';
  }

  const diffInMinutes = Math.floor(diffInSeconds / 60);
  if (diffInMinutes < 60) {
    return `${diffInMinutes}m ago`;
  }

  const diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 24) {
    return `${diffInHours}h ago`;
  }

  const diffInDays = Math.floor(diffInHours / 24);
  if (diffInDays < 7) {
    return `${diffInDays}d ago`;
  }

  return formatDate(dateObj);
};

// String utilities
export const capitalize = (str: string): string => {
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
};

export const truncateText = (text: string, maxLength: number): string => {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength - 3) + '...';
};

export const generateId = (): string => {
  return Math.random().toString(36).substring(2) + Date.now().toString(36);
};

export const slugify = (text: string): string => {
  return text
    .toLowerCase()
    .replace(/[^\w\s-]/g, '')
    .replace(/[\s_-]+/g, '-')
    .replace(/^-+|-+$/g, '');
};

// Array utilities
export const groupBy = <T>(array: T[], key: keyof T): Record<string, T[]> => {
  return array.reduce((groups, item) => {
    const group = String(item[key]);
    groups[group] = groups[group] || [];
    groups[group].push(item);
    return groups;
  }, {} as Record<string, T[]>);
};

export const sortBy = <T>(array: T[], key: keyof T, direction: 'asc' | 'desc' = 'asc'): T[] => {
  return [...array].sort((a, b) => {
    const aVal = a[key];
    const bVal = b[key];
    
    if (aVal < bVal) return direction === 'asc' ? -1 : 1;
    if (aVal > bVal) return direction === 'asc' ? 1 : -1;
    return 0;
  });
};

export const uniqueBy = <T>(array: T[], key: keyof T): T[] => {
  const seen = new Set();
  return array.filter(item => {
    const value = item[key];
    if (seen.has(value)) {
      return false;
    }
    seen.add(value);
    return true;
  });
};

// Image utilities
export const validateImageSize = (sizeInBytes: number): boolean => {
  const maxSizeInBytes = IMAGE_CONSTRAINTS.MAX_SIZE_MB * 1024 * 1024;
  return sizeInBytes <= maxSizeInBytes;
};

export const validateImageType = (mimeType: string): boolean => {
  return IMAGE_CONSTRAINTS.ALLOWED_TYPES.includes(mimeType as any);
};

// Async utilities
export const delay = (ms: number): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};

export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  limit: number
): ((...args: Parameters<T>) => void) => {
  let inThrottle: boolean;
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
};

// Error handling utilities
export const handleError = (error: any): string => {
  if (typeof error === 'string') {
    return error;
  }
  
  if (error?.message) {
    return error.message;
  }
  
  if (error?.response?.data?.message) {
    return error.response.data.message;
  }
  
  return 'An unexpected error occurred';
};

// Platform utilities
export const isIOS = (): boolean => {
  return require('react-native').Platform.OS === 'ios';
};

// Export web compatibility utilities
export * from './webCompatibility';
export * from './gestureHandlerWeb';

export const isAndroid = (): boolean => {
  return require('react-native').Platform.OS === 'android';
};

// Color utilities
export const hexToRgba = (hex: string, alpha: number = 1): string => {
  const r = parseInt(hex.slice(1, 3), 16);
  const g = parseInt(hex.slice(3, 5), 16);
  const b = parseInt(hex.slice(5, 7), 16);
  return `rgba(${r}, ${g}, ${b}, ${alpha})`;
};

export const getContrastColor = (hexColor: string): string => {
  const r = parseInt(hexColor.slice(1, 3), 16);
  const g = parseInt(hexColor.slice(3, 5), 16);
  const b = parseInt(hexColor.slice(5, 7), 16);
  const brightness = (r * 299 + g * 587 + b * 114) / 1000;
  return brightness > 128 ? '#000000' : '#FFFFFF';
};
