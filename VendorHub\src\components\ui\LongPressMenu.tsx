import React, { useState, useRef } from 'react';
import { StyleSheet, Modal, Animated, Dimensions, PanResponder, Platform } from 'react-native';
import { RTLIcon, RTLText, RTLTouchableOpacity, RTLView } from '../RTL';
import * as Haptics from 'expo-haptics';
import { useThemedStyles } from '../../hooks';
import { webCompatibleHaptics, isWeb } from '../../utils/webCompatibility';
import {
  SPACING,
  FONT_SIZES,
  FONT_WEIGHTS,
  BORDER_RADIUS } from '../../constants/theme';
import type ThemeColors  from '../../contexts/ThemeContext';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

export interface MenuAction {
  id: string;
  title: string;
  icon: string;
  color?: string;
  onPress: () => void;
  destructive?: boolean;
}

export interface LongPressMenuProps {
  children: React.ReactNode;
  actions: MenuAction[];
  disabled?: boolean;
  longPressDuration?: number;
  style?: any;
}

export const LongPressMenu: React.FC<LongPressMenuProps> = ({
  children,
  actions,
  disabled = false,
  longPressDuration = 500,
  style,
}) => {
  const styles = useThemedStyles(createStyles);
  const [isMenuVisible, setIsMenuVisible] = useState(false);
  const [menuPosition, setMenuPosition] = useState({ x: 0, y: 0 });
  const [pressPosition, setPressPosition] = useState({ x: 0, y: 0 });
  
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const menuOpacity = useRef(new Animated.Value(0)).current;
  const menuScale = useRef(new Animated.Value(0.8)).current;
  
  const longPressTimer = useRef<NodeJS.Timeout | null>(null);
  const isLongPressing = useRef(false);

  const panResponder = useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => !disabled && !isWeb,
      onMoveShouldSetPanResponder: () => false,

      onPanResponderGrant: (evt) => {
        if (disabled) return;

        isLongPressing.current = false;
        setPressPosition({ x: evt.nativeEvent.pageX, y: evt.nativeEvent.pageY });

        // Start scale animation
        Animated.spring(scaleAnim, {
          toValue: 0.95,
          useNativeDriver: true,
          tension: 300,
          friction: 10,
        }).start();

        // Start long press timer
        longPressTimer.current = setTimeout(() => {
          isLongPressing.current = true;
          handleLongPress(evt.nativeEvent.pageX, evt.nativeEvent.pageY);
        }, longPressDuration);
      },
      
      onPanResponderMove: (evt, gestureState) => {
        // Cancel long press if user moves too much
        const distance = Math.sqrt(
          Math.pow(gestureState.dx, 2) + Math.pow(gestureState.dy, 2)
        );
        
        if (distance > 10 && longPressTimer.current) {
          clearTimeout(longPressTimer.current);
          longPressTimer.current = null;
        }
      },
      
      onPanResponderRelease: () => {
        // Reset scale animation
        Animated.spring(scaleAnim, {
          toValue: 1,
          useNativeDriver: true,
          tension: 300,
          friction: 10,
        }).start();
        
        // Clear timer if still active
        if (longPressTimer.current) {
          clearTimeout(longPressTimer.current);
          longPressTimer.current = null;
        }
        
        // If it wasn't a long press, treat as normal tap
        if (!isLongPressing.current) {
          // Handle normal tap if needed
        }
      },
    })
  ).current;

  const handleLongPress = (x: number, y: number) => {
    if (disabled || actions.length === 0) return;
    
    // Provide haptic feedback
    webCompatibleHaptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    
    // Calculate menu position to keep it on screen
    const menuWidth = 200;
    const menuHeight = actions.length * 50 + SPACING.md * 2;
    
    let menuX = x - menuWidth / 2;
    let menuY = y - menuHeight - 20; // Position above touch point
    
    // Adjust if menu would go off screen
    if (menuX < SPACING.md) {
      menuX = SPACING.md;
    } else if (menuX + menuWidth > screenWidth - SPACING.md) {
      menuX = screenWidth - menuWidth - SPACING.md;
    }
    
    if (menuY < SPACING.xl) {
      menuY = y + 20; // Position below touch point
    }
    
    setMenuPosition({ x: menuX, y: menuY });
    setIsMenuVisible(true);
    
    // Animate menu appearance
    Animated.parallel([
      Animated.timing(menuOpacity, {
        toValue: 1,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.spring(menuScale, {
        toValue: 1,
        useNativeDriver: true,
        tension: 300,
        friction: 10,
      }),
    ]).start();
  };

  const handleMenuClose = () => {
    Animated.parallel([
      Animated.timing(menuOpacity, {
        toValue: 0,
        duration: 150,
        useNativeDriver: true,
      }),
      Animated.timing(menuScale, {
        toValue: 0.8,
        duration: 150,
        useNativeDriver: true,
      }),
    ]).start(() => {
      setIsMenuVisible(false);
      menuScale.setValue(0.8);
    });
  };

  const handleActionPress = (action: MenuAction) => {
    handleMenuClose();

    // Provide haptic feedback
    webCompatibleHaptics.impactAsync(
      action.destructive
        ? Haptics.ImpactFeedbackStyle.Heavy
        : Haptics.ImpactFeedbackStyle.Light
    );

    // Execute action after a short delay to allow menu to close
    setTimeout(() => {
      action.onPress();
    }, 100);
  };

  // Web-compatible event handlers
  const webEventHandlers = isWeb ? {
    onMouseDown: (evt: any) => {
      if (disabled) return;

      isLongPressing.current = false;
      setPressPosition({ x: evt.clientX, y: evt.clientY });

      // Start scale animation
      Animated.spring(scaleAnim, {
        toValue: 0.95,
        useNativeDriver: true,
        tension: 300,
        friction: 10,
      }).start();

      // Start long press timer
      longPressTimer.current = setTimeout(() => {
        isLongPressing.current = true;
        handleLongPress(evt.clientX, evt.clientY);
      }, longPressDuration);
    },

    onMouseUp: () => {
      // Reset scale animation
      Animated.spring(scaleAnim, {
        toValue: 1,
        useNativeDriver: true,
        tension: 300,
        friction: 10,
      }).start();

      // Clear timer if still active
      if (longPressTimer.current) {
        clearTimeout(longPressTimer.current);
        longPressTimer.current = null;
      }
    },

    onMouseLeave: () => {
      // Reset scale animation
      Animated.spring(scaleAnim, {
        toValue: 1,
        useNativeDriver: true,
        tension: 300,
        friction: 10,
      }).start();

      // Clear timer if still active
      if (longPressTimer.current) {
        clearTimeout(longPressTimer.current);
        longPressTimer.current = null;
      }
    },
  } : {};

  const renderMenu = () => (
    <Modal
      visible={isMenuVisible}
      transparent
      animationType="none"
      onRequestClose={handleMenuClose}
    >
      <RTLTouchableOpacity
        style={styles.modalOverlay}
        activeOpacity={1}
        onPress={handleMenuClose}
      >
        <Animated.View
          style={[
            styles.menuContainer,
            {
              left: menuPosition.x,
              top: menuPosition.y,
              opacity: menuOpacity,
              transform: [{ scale: menuScale }],
            },
          ]}
        >
          {actions.map((action, index) => (
            <RTLTouchableOpacity
              key={action.id}
              style={[
                styles.menuItem,
                index === 0 && styles.menuItemFirst,
                index === actions.length - 1 && styles.menuItemLast,
                action.destructive && styles.menuItemDestructive,
              ]}
              onPress={() => handleActionPress(action)}
              activeOpacity={0.7}
            >
              <RTLIcon
                name={action.icon as any}
                size={20}
                color={action.destructive ? '#FF6B6B' : action.color || '#667eea'}
              />
              <RTLText
                style={[
                  styles.menuItemText,
                  action.destructive && styles.menuItemTextDestructive,
                ]}
              >
                {action.title}
              </RTLText>
            </RTLTouchableOpacity>
          ))}
        </Animated.View>
      </RTLTouchableOpacity>
    </Modal>
  );

  return (
    <RTLView style={style}>
      <Animated.View
        style={[{ transform: [{ scale: scaleAnim }] }]}
        {...(isWeb ? webEventHandlers : panResponder.panHandlers)}
      >
        {children}
      </Animated.View>
      {renderMenu()}
    </RTLView>
  );
};

const createStyles = (colors: ThemeColors) => StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
  },
  menuContainer: {
    position: 'absolute',
    backgroundColor: colors.surface,
    borderRadius: BORDER_RADIUS.lg,
    paddingVertical: SPACING.sm,
    minWidth: 200,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.25,
    shadowRadius: 10,
    elevation: 10,
  },
  menuItem: {
    flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
    alignItems: 'center',
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    minHeight: 44,
  },
  menuItemFirst: {
    borderTopLeftRadius: BORDER_RADIUS.lg,
    borderTopRightRadius: BORDER_RADIUS.lg,
  },
  menuItemLast: {
    borderBottomLeftRadius: BORDER_RADIUS.lg,
    borderBottomRightRadius: BORDER_RADIUS.lg,
  },
  menuItemDestructive: {
    backgroundColor: 'rgba(255, 107, 107, 0.1)',
  },
  menuItemText: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.medium,
    color: colors.text,
    marginLeft: SPACING.sm,
  },
  menuItemTextDestructive: {
    color: '#FF6B6B',
  },
});

// Predefined menu actions for common use cases
export const MenuActions = {
  edit: (onPress: () => void): MenuAction => ({
    id: 'edit',
    title: 'Edit',
    icon: 'pencil',
    onPress,
  }),
  
  delete: (onPress: () => void): MenuAction => ({
    id: 'delete',
    title: 'Delete',
    icon: 'trash',
    onPress,
    destructive: true,
  }),
  
  duplicate: (onPress: () => void): MenuAction => ({
    id: 'duplicate',
    title: 'Duplicate',
    icon: 'copy',
    onPress,
  }),
  
  share: (onPress: () => void): MenuAction => ({
    id: 'share',
    title: 'Share',
    icon: 'share',
    onPress,
  }),
  
  archive: (onPress: () => void): MenuAction => ({
    id: 'archive',
    title: 'Archive',
    icon: 'archive',
    onPress,
  }),
  
  favorite: (onPress: () => void): MenuAction => ({
    id: 'favorite',
    title: 'Add to Favorites',
    icon: 'heart',
    onPress,
  }),
  
  view: (onPress: () => void): MenuAction => ({
    id: 'view',
    title: 'View Details',
    icon: 'eye',
    onPress,
  }),
};
