import React, { createContext, useContext, useRef, useCallback, useEffect } from 'react';
import { Platform } from 'react-native';
import { useIntelligentCache } from './IntelligentCache';

// Conditionally import NetInfo for native platforms
let NetInfo: any = null;
try {
  if (Platform.OS !== 'web') {
    const netInfoModule = require('@react-native-community/netinfo');
    NetInfo = netInfoModule.default || netInfoModule;
  }
} catch (error) {
  console.warn('NetInfo not available:', error);
}

interface NetworkRequest {
  id: string;
  url: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  headers?: Record<string, string>;
  body?: any;
  priority: 'low' | 'medium' | 'high' | 'critical';
  retryCount: number;
  maxRetries: number;
  timeout: number;
  cacheKey?: string;
  cacheTTL?: number;
  timestamp: number;
}

interface NetworkConfig {
  maxConcurrentRequests: number;
  requestTimeout: number;
  retryDelay: number;
  maxRetries: number;
  enableBatching: boolean;
  batchDelay: number;
  enableCompression: boolean;
  enableCaching: boolean;
  enableOfflineQueue: boolean;
  enableRequestDeduplication: boolean;
}

interface NetworkMetrics {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageResponseTime: number;
  cacheHitRate: number;
  networkErrors: number;
  timeouts: number;
  retries: number;
  bytesTransferred: number;
  requestsPerSecond: number;
}

interface NetworkOptimizerContextType {
  request: <T>(config: RequestConfig) => Promise<T>;
  batchRequest: <T>(configs: RequestConfig[]) => Promise<T[]>;
  prefetchData: (configs: RequestConfig[]) => Promise<void>;
  getMetrics: () => NetworkMetrics;
  clearQueue: () => void;
  setNetworkConfig: (config: Partial<NetworkConfig>) => void;
}

interface RequestConfig {
  url: string;
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  headers?: Record<string, string>;
  body?: any;
  priority?: 'low' | 'medium' | 'high' | 'critical';
  timeout?: number;
  maxRetries?: number;
  cacheKey?: string;
  cacheTTL?: number;
  enableCache?: boolean;
  enableRetry?: boolean;
  enableCompression?: boolean;
}

const DEFAULT_CONFIG: NetworkConfig = {
  maxConcurrentRequests: 6,
  requestTimeout: 30000,
  retryDelay: 1000,
  maxRetries: 3,
  enableBatching: true,
  batchDelay: 100,
  enableCompression: true,
  enableCaching: true,
  enableOfflineQueue: true,
  enableRequestDeduplication: true,
};

const NetworkOptimizerContext = createContext<NetworkOptimizerContextType | undefined>(undefined);

export const useNetworkOptimizer = (): NetworkOptimizerContextType => {
  const context = useContext(NetworkOptimizerContext);
  if (!context) {
    throw new Error('useNetworkOptimizer must be used within a NetworkOptimizerProvider');
  }
  return context;
};

interface NetworkOptimizerProviderProps {
  children: React.ReactNode;
  config?: Partial<NetworkConfig>;
}

export const NetworkOptimizerProvider: React.FC<NetworkOptimizerProviderProps> = ({
  children,
  config = {},
}) => {
  const cache = useIntelligentCache();
  const finalConfig = useRef({ ...DEFAULT_CONFIG, ...config });
  
  // Request queue and management
  const requestQueue = useRef<NetworkRequest[]>([]);
  const activeRequests = useRef<Map<string, Promise<any>>>(new Map());
  const pendingRequests = useRef<Set<string>>(new Set());
  const offlineQueue = useRef<NetworkRequest[]>([]);
  
  // Network state
  const isOnline = useRef(true);
  const connectionType = useRef<string>('wifi');
  
  // Metrics
  const metrics = useRef<NetworkMetrics>({
    totalRequests: 0,
    successfulRequests: 0,
    failedRequests: 0,
    averageResponseTime: 0,
    cacheHitRate: 0,
    networkErrors: 0,
    timeouts: 0,
    retries: 0,
    bytesTransferred: 0,
    requestsPerSecond: 0,
  });

  // Timers
  const batchTimer = useRef<NodeJS.Timeout>();
  const queueProcessor = useRef<NodeJS.Timeout>();

  useEffect(() => {
    const networkUnsubscribe = setupNetworkListener();
    startQueueProcessor();

    return () => {
      if (batchTimer.current) clearTimeout(batchTimer.current);
      if (queueProcessor.current) clearInterval(queueProcessor.current);
      if (networkUnsubscribe) {
        networkUnsubscribe();
      }
    };
  }, []);

  const setupNetworkListener = () => {
    if (!NetInfo) {
      // For web or when NetInfo is not available, assume online
      isOnline.current = true;
      connectionType.current = 'unknown';
      return () => {}; // Return empty cleanup function
    }

    try {
      const unsubscribe = NetInfo.addEventListener(state => {
        const wasOnline = isOnline.current;
        isOnline.current = state.isConnected ?? true;
        connectionType.current = state.type || 'unknown';

        // Process offline queue when coming back online
        if (!wasOnline && isOnline.current && offlineQueue.current.length > 0) {
          processOfflineQueue();
        }
      });

      return unsubscribe;
    } catch (error) {
      console.warn('Failed to setup network listener:', error);
      isOnline.current = true;
      connectionType.current = 'unknown';
      return () => {}; // Return empty cleanup function
    }
  };

  const startQueueProcessor = () => {
    queueProcessor.current = setInterval(() => {
      processRequestQueue();
    }, 50); // Process queue every 50ms
  };

  const generateRequestId = (config: RequestConfig): string => {
    const key = `${config.method || 'GET'}-${config.url}-${JSON.stringify(config.body || {})}`;
    return btoa(key).replace(/[^a-zA-Z0-9]/g, '').substring(0, 16);
  };

  const calculatePriority = (priority: string): number => {
    const priorityMap = { critical: 4, high: 3, medium: 2, low: 1 };
    return priorityMap[priority as keyof typeof priorityMap] || 2;
  };

  const adaptRequestForConnection = (config: RequestConfig): RequestConfig => {
    const adaptedConfig = { ...config };

    // Adjust timeout based on connection type
    if (connectionType.current === 'cellular') {
      adaptedConfig.timeout = (adaptedConfig.timeout || finalConfig.current.requestTimeout) * 1.5;
    } else if (connectionType.current === '2g') {
      adaptedConfig.timeout = (adaptedConfig.timeout || finalConfig.current.requestTimeout) * 3;
    }

    // Enable compression for slower connections
    if (['2g', '3g', 'cellular'].includes(connectionType.current)) {
      adaptedConfig.enableCompression = true;
    }

    return adaptedConfig;
  };

  const executeRequest = async <T,>(request: NetworkRequest): Promise<T> => {
    const startTime = Date.now();
    
    try {
      // Check cache first
      if (finalConfig.current.enableCaching && request.cacheKey) {
        const cachedData = await cache.get<T>(request.cacheKey);
        if (cachedData !== null) {
          metrics.current.cacheHitRate = 
            (metrics.current.cacheHitRate * metrics.current.totalRequests + 1) / 
            (metrics.current.totalRequests + 1);
          return cachedData;
        }
      }

      // Prepare request options
      const requestOptions: RequestInit = {
        method: request.method,
        headers: {
          'Content-Type': 'application/json',
          ...request.headers,
        },
        signal: AbortSignal.timeout(request.timeout),
      };

      if (request.body && request.method !== 'GET') {
        requestOptions.body = JSON.stringify(request.body);
      }

      // Add compression headers if enabled
      if (finalConfig.current.enableCompression) {
        requestOptions.headers = {
          ...requestOptions.headers,
          'Accept-Encoding': 'gzip, deflate, br',
        };
      }

      // Execute request
      const response = await fetch(request.url, requestOptions);
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      const responseTime = Date.now() - startTime;

      // Update metrics
      metrics.current.totalRequests++;
      metrics.current.successfulRequests++;
      metrics.current.averageResponseTime = 
        (metrics.current.averageResponseTime * (metrics.current.totalRequests - 1) + responseTime) / 
        metrics.current.totalRequests;

      // Estimate bytes transferred
      const responseSize = JSON.stringify(data).length * 2;
      metrics.current.bytesTransferred += responseSize;

      // Cache response if enabled
      if (finalConfig.current.enableCaching && request.cacheKey && request.cacheTTL) {
        await cache.set(request.cacheKey, data, {
          ttl: request.cacheTTL,
          tags: ['network-response'],
        });
      }

      return data as T;

    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      // Update error metrics
      metrics.current.totalRequests++;
      metrics.current.failedRequests++;
      
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          metrics.current.timeouts++;
        } else {
          metrics.current.networkErrors++;
        }
      }

      // Retry logic
      if (request.retryCount < request.maxRetries && isOnline.current) {
        request.retryCount++;
        metrics.current.retries++;
        
        // Exponential backoff
        const delay = finalConfig.current.retryDelay * Math.pow(2, request.retryCount - 1);
        await new Promise(resolve => setTimeout(resolve, delay));
        
        return executeRequest<T>(request);
      }

      throw error;
    }
  };

  const processRequestQueue = () => {
    if (!isOnline.current || requestQueue.current.length === 0) {
      return;
    }

    const activeCount = activeRequests.current.size;
    const maxConcurrent = finalConfig.current.maxConcurrentRequests;
    
    if (activeCount >= maxConcurrent) {
      return;
    }

    // Sort queue by priority and timestamp
    requestQueue.current.sort((a, b) => {
      const priorityDiff = calculatePriority(b.priority) - calculatePriority(a.priority);
      if (priorityDiff !== 0) return priorityDiff;
      return a.timestamp - b.timestamp;
    });

    // Process requests up to the concurrent limit
    const toProcess = requestQueue.current.splice(0, maxConcurrent - activeCount);
    
    toProcess.forEach(request => {
      const promise = executeRequest(request);
      activeRequests.current.set(request.id, promise);
      
      promise.finally(() => {
        activeRequests.current.delete(request.id);
        pendingRequests.current.delete(request.id);
      });
    });
  };

  const processOfflineQueue = () => {
    if (!isOnline.current) return;
    
    const queuedRequests = [...offlineQueue.current];
    offlineQueue.current = [];
    
    queuedRequests.forEach(request => {
      requestQueue.current.push(request);
    });
  };

  const request = useCallback(async <T,>(config: RequestConfig): Promise<T> => {
    const adaptedConfig = adaptRequestForConnection(config);
    const requestId = generateRequestId(adaptedConfig);
    
    // Request deduplication
    if (finalConfig.current.enableRequestDeduplication && pendingRequests.current.has(requestId)) {
      const existingPromise = activeRequests.current.get(requestId);
      if (existingPromise) {
        return existingPromise as Promise<T>;
      }
    }

    const networkRequest: NetworkRequest = {
      id: requestId,
      url: adaptedConfig.url,
      method: adaptedConfig.method || 'GET',
      headers: adaptedConfig.headers,
      body: adaptedConfig.body,
      priority: adaptedConfig.priority || 'medium',
      retryCount: 0,
      maxRetries: adaptedConfig.maxRetries || finalConfig.current.maxRetries,
      timeout: adaptedConfig.timeout || finalConfig.current.requestTimeout,
      cacheKey: adaptedConfig.cacheKey,
      cacheTTL: adaptedConfig.cacheTTL,
      timestamp: Date.now(),
    };

    pendingRequests.current.add(requestId);

    // Handle offline requests
    if (!isOnline.current && finalConfig.current.enableOfflineQueue) {
      offlineQueue.current.push(networkRequest);
      throw new Error('Request queued for when network is available');
    }

    // Add to queue
    requestQueue.current.push(networkRequest);
    
    // Return promise that resolves when request completes
    return new Promise((resolve, reject) => {
      const checkCompletion = () => {
        const activePromise = activeRequests.current.get(requestId);
        if (activePromise) {
          activePromise.then(resolve).catch(reject);
        } else if (!pendingRequests.current.has(requestId)) {
          reject(new Error('Request was cancelled or failed'));
        } else {
          setTimeout(checkCompletion, 10);
        }
      };
      
      checkCompletion();
    });
  }, []);

  const batchRequest = useCallback(async <T,>(configs: RequestConfig[]): Promise<T[]> => {
    if (!finalConfig.current.enableBatching) {
      return Promise.all(configs.map(config => request<T>(config)));
    }

    // Group requests by priority
    const groupedConfigs = configs.reduce((groups, config) => {
      const priority = config.priority || 'medium';
      if (!groups[priority]) groups[priority] = [];
      groups[priority].push(config);
      return groups;
    }, {} as Record<string, RequestConfig[]>);

    // Execute groups in priority order
    const results: T[] = [];
    const priorities = ['critical', 'high', 'medium', 'low'];
    
    for (const priority of priorities) {
      if (groupedConfigs[priority]) {
        const groupResults = await Promise.all(
          groupedConfigs[priority].map(config => request<T>(config))
        );
        results.push(...groupResults);
      }
    }

    return results;
  }, [request]);

  const prefetchData = useCallback(async (configs: RequestConfig[]): Promise<void> => {
    // Execute prefetch requests with low priority
    const prefetchConfigs = configs.map(config => ({
      ...config,
      priority: 'low' as const,
    }));

    try {
      await batchRequest(prefetchConfigs);
    } catch (error) {
      // Prefetch failures are non-critical
      console.warn('Prefetch failed:', error);
    }
  }, [batchRequest]);

  const getMetrics = useCallback((): NetworkMetrics => {
    // Calculate requests per second
    const now = Date.now();
    const timeWindow = 60000; // 1 minute
    const recentRequests = metrics.current.totalRequests; // Simplified
    metrics.current.requestsPerSecond = recentRequests / 60;

    return { ...metrics.current };
  }, []);

  const clearQueue = useCallback(() => {
    requestQueue.current = [];
    offlineQueue.current = [];
    activeRequests.current.clear();
    pendingRequests.current.clear();
  }, []);

  const setNetworkConfig = useCallback((config: Partial<NetworkConfig>) => {
    finalConfig.current = { ...finalConfig.current, ...config };
  }, []);

  const contextValue: NetworkOptimizerContextType = {
    request,
    batchRequest,
    prefetchData,
    getMetrics,
    clearQueue,
    setNetworkConfig,
  };

  return (
    <NetworkOptimizerContext.Provider value={contextValue}>
      {children}
    </NetworkOptimizerContext.Provider>
  );
};
