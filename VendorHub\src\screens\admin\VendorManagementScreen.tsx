import React, { useState, useMemo } from 'react';
import { StyleSheet, RefreshControl, Alert } from 'react-native';
import { useThemedStyles, useVendors, useDebounce, useI18n } from '../../hooks';
import { RTLView, RTLText, RTLFlatList, RTLIcon, RTLSafeAreaView, RTLTouchableOpacity } from '../../components/RTL';
import { Card, StatusBadge, Button, Input, EmptyState, LoadingSpinner, SwipeableRow, SwipeActions, useEnhancedRefresh, OptimizedFlatList, useListPerformance } from '../../components';
import {
  SPACING,
  FONT_SIZES,
  FONT_WEIGHTS,
  BORDER_RADIUS } from '../../constants/theme';
import { VENDOR_STATUS, type VendorStatus } from '../../constants';
import { formatDate } from '../../utils';
import type { ThemeColors } from '../../contexts/ThemeContext';
import type Vendor  from '../../contexts/DataContext';

interface VendorManagementScreenProps {
  navigation: any;
}

export const VendorManagementScreen: React.FC<VendorManagementScreenProps> = ({ navigation }) => {
  const styles = useThemedStyles(createStyles);
  const { t } = useI18n();
  const {
    vendors,
    vendorStats,
    approveVendor,
    rejectVendor,
    searchVendors,
    getVendorsByStatus,
    isLoading,
  } = useVendors();

  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilter, setSelectedFilter] = useState<VendorStatus | 'all'>('all');

  const debouncedSearchQuery = useDebounce(searchQuery, 300);

  const filteredVendors = useMemo(() => {
    let result = vendors;

    // Apply status filter
    if (selectedFilter !== 'all') {
      result = getVendorsByStatus(selectedFilter);
    }

    // Apply search filter
    if (debouncedSearchQuery.trim()) {
      result = searchVendors(debouncedSearchQuery);
      if (selectedFilter !== 'all') {
        result = result.filter(vendor => vendor.status === selectedFilter);
      }
    }

    return result;
  }, [vendors, selectedFilter, debouncedSearchQuery, getVendorsByStatus, searchVendors]);

  // Enhanced refresh with haptic feedback
  const { refreshing, onRefresh } = useEnhancedRefresh(async () => {
    // Simulate data refresh
    await new Promise(resolve => setTimeout(resolve, 1000));
    // In a real app, this would refetch vendor data
  }, []);

  // Performance monitoring
  const { logScrollPerformance } = useListPerformance('VendorManagement');

  const handleApproveVendor = async (vendorId: string, vendorName: string) => {
    Alert.alert(
      t('admin.approveVendor'),
      t('admin.approveVendorConfirmation', { vendorName }),
      [
        { text: t('common.cancel'), style: 'cancel' },
        {
          text: t('admin.approve'),
          style: 'default',
          onPress: async () => {
            try {
              await approveVendor(vendorId);
              Alert.alert(t('common.success'), t('admin.vendorApprovedSuccess'));
            } catch (error) {
              Alert.alert(t('common.error'), t('admin.failedToApproveVendor'));
            }
          },
        },
      ]
    );
  };

  const handleRejectVendor = async (vendorId: string, vendorName: string) => {
    Alert.alert(
      t('admin.rejectVendor'),
      t('admin.rejectVendorConfirmation', { vendorName }),
      [
        { text: t('common.cancel'), style: 'cancel' },
        {
          text: t('admin.reject'),
          style: 'destructive',
          onPress: async () => {
            try {
              await rejectVendor(vendorId);
              Alert.alert(t('common.success'), t('admin.vendorRejectedSuccess'));
            } catch (error) {
              Alert.alert(t('common.error'), t('admin.failedToRejectVendor'));
            }
          },
        },
      ]
    );
  };

  const renderVendorItem = ({ item }: { item: Vendor }) => {
    // Define swipe actions based on vendor status
    const getSwipeActions = () => {
      if (item.status === VENDOR_STATUS.PENDING) {
        return {
          leftActions: [
            SwipeActions.approve(() => handleApproveVendor(item.id, item.businessName)),
          ],
          rightActions: [
            SwipeActions.reject(() => handleRejectVendor(item.id, item.businessName)),
          ],
        };
      }

      if (item.status === VENDOR_STATUS.APPROVED) {
        return {
          rightActions: [
            SwipeActions.view(() => {
              // Navigate to vendor details or shop
              console.log('View vendor:', item.businessName);
            }),
          ],
        };
      }

      return { leftActions: [], rightActions: [] };
    };

    const { leftActions, rightActions } = getSwipeActions();

    return (
      <SwipeableRow
        leftActions={leftActions}
        rightActions={rightActions}
        style={styles.swipeableContainer}
      >
        <Card style={styles.vendorCard} variant="elevated">
          <RTLView style={styles.vendorHeader}>
            <RTLView style={styles.vendorInfo}>
              <RTLText style={styles.businessName}>{item.businessName}</RTLText>
              <RTLText style={styles.ownerName}>{t('admin.by')} {item.ownerName}</RTLText>
              <RTLText style={styles.email}>{item.email}</RTLText>
            </RTLView>
            <StatusBadge status={item.status} type="vendor" />
          </RTLView>

          <RTLText style={styles.description} numberOfLines={2}>
            {item.businessDescription}
          </RTLText>

          <RTLView style={styles.vendorStats}>
            <RTLView style={styles.statItem}>
              <RTLText style={styles.statValue}>{item.totalProducts}</RTLText>
              <RTLText style={styles.statLabel}>{t('admin.products')}</RTLText>
            </RTLView>
            <RTLView style={styles.statItem}>
              <RTLText style={styles.statValue}>{item.totalOrders}</RTLText>
              <RTLText style={styles.statLabel}>{t('admin.orders')}</RTLText>
            </RTLView>
            <RTLView style={styles.statItem}>
              <RTLText style={styles.statValue}>{item.rating.toFixed(1)}</RTLText>
              <RTLText style={styles.statLabel}>{t('admin.rating')}</RTLText>
            </RTLView>
          </RTLView>

          <RTLView style={styles.vendorFooter}>
            <RTLText style={styles.dateText}>
              {t('admin.applied')}: {formatDate(item.createdAt)}
            </RTLText>

            {/* Show hint for swipe actions on pending vendors */}
            {item.status === VENDOR_STATUS.PENDING && (
              <RTLText style={styles.swipeHint}>
                {t('admin.swipeToApproveReject')}
              </RTLText>
            )}

            {/* Keep buttons as fallback for accessibility */}
            {item.status === VENDOR_STATUS.PENDING && (
              <RTLView style={styles.actionButtons}>
                <Button
                  title={t('admin.reject')}
                  onPress={() => handleRejectVendor(item.id, item.businessName)}
                  variant="outline"
                  size="small"
                  style={styles.rejectButton}
                />
                <Button
                  title={t('admin.approve')}
                  onPress={() => handleApproveVendor(item.id, item.businessName)}
                  size="small"
                  style={styles.approveButton}
                />
              </RTLView>
            )}
          </RTLView>
        </Card>
      </SwipeableRow>
    );
  };

  const filterOptions = [
    { label: t('common.all'), value: 'all' as const },
    { label: t('orders.pending'), value: VENDOR_STATUS.PENDING },
    { label: t('admin.approved'), value: VENDOR_STATUS.APPROVED },
    { label: t('admin.rejected'), value: VENDOR_STATUS.REJECTED },
  ];

  if (isLoading) {
    return (
      <RTLView style={styles.loadingContainer}>
        <LoadingSpinner size="large" variant="gradient" />
      </RTLView>
    );
  }

  return (
    <RTLSafeAreaView style={styles.container}>
      {/* Header Stats */}
      <RTLView style={styles.statsHeader}>
        <RTLView style={styles.statCard}>
          <RTLText style={styles.statNumber}>{vendorStats.total}</RTLText>
          <RTLText style={styles.statTitle}>{t('admin.total')}</RTLText>
        </RTLView>
        <RTLView style={styles.statCard}>
          <RTLText style={styles.statNumber}>{vendorStats.pending}</RTLText>
          <RTLText style={styles.statTitle}>{t('admin.pending')}</RTLText>
        </RTLView>
        <RTLView style={styles.statCard}>
          <RTLText style={styles.statNumber}>{vendorStats.approved}</RTLText>
          <RTLText style={styles.statTitle}>{t('admin.approved')}</RTLText>
        </RTLView>
        <RTLView style={styles.statCard}>
          <RTLText style={styles.statNumber}>{vendorStats.rejected}</RTLText>
          <RTLText style={styles.statTitle}>{t('admin.rejected')}</RTLText>
        </RTLView>
      </RTLView>

      {/* Search and Filters */}
      <RTLView style={styles.searchContainer}>
        <Input
          placeholder={t('admin.searchVendors')}
          value={searchQuery}
          onChangeText={setSearchQuery}
          leftIcon="search-outline"
          style={styles.searchInput}
          containerStyle={styles.searchInputContainer}
        />

        <RTLView style={styles.filterContainer}>
          {filterOptions.map((option) => (
            <RTLTouchableOpacity
              key={option.value}
              style={[
                styles.filterChip,
                selectedFilter === option.value && styles.filterChipActive,
              ]}
              onPress={() => setSelectedFilter(option.value)}
            >
              <RTLText
                style={[
                  styles.filterChipText,
                  selectedFilter === option.value && styles.filterChipTextActive,
                ].filter(Boolean) as any}
              >
                {option.label}
              </RTLText>
            </RTLTouchableOpacity>
          ))}
        </RTLView>
      </RTLView>

      {/* Vendor List */}
      <OptimizedFlatList
        data={filteredVendors}
        renderItem={renderVendorItem}
        keyExtractor={(item) => item.id}
        estimatedItemSize={200}
        enableVirtualization={true}
        enableMemoryOptimization={true}
        enableScrollOptimization={true}
        contentContainerStyle={styles.listContainer}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
        onScroll={logScrollPerformance}
        ListEmptyComponent={
          <EmptyState
            icon="storefront-outline"
            title={t('admin.noVendorsFound')}
            description={
              searchQuery
                ? t('admin.noVendorsMatchSearch')
                : t('admin.noVendorsRegistered')
            }
          />
        }
      />
    </RTLSafeAreaView>
  );
};

const createStyles = (colors: ThemeColors) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: colors.background,
    },
    statsHeader: {
      flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
      paddingHorizontal: SPACING.lg,
      paddingVertical: SPACING.md,
      backgroundColor: colors.surface,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    statCard: {
      flex: 1,
      alignItems: 'center',
    },
    statNumber: {
      fontSize: FONT_SIZES.lg,
      fontWeight: FONT_WEIGHTS.bold,
      color: colors.textPrimary,
    },
    statTitle: {
      fontSize: FONT_SIZES.xs,
      color: colors.textSecondary,
      marginTop: SPACING.xs,
    },
    searchContainer: {
      paddingHorizontal: SPACING.lg,
      paddingVertical: SPACING.md,
      backgroundColor: colors.surface,
    },
    searchInputContainer: {
      marginBottom: SPACING.md,
    },
    searchInput: {
      marginBottom: 0,
    },
    filterContainer: {
      flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
      gap: SPACING.sm,
    },
    filterChip: {
      paddingHorizontal: SPACING.md,
      paddingVertical: SPACING.sm,
      borderRadius: BORDER_RADIUS.round,
      backgroundColor: colors.backgroundSecondary,
      borderWidth: 1,
      borderColor: colors.border,
    },
    filterChipActive: {
      backgroundColor: colors.primary,
      borderColor: colors.primary,
    },
    filterChipText: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
    },
    filterChipTextActive: {
      color: colors.textOnPrimary,
      fontWeight: FONT_WEIGHTS.medium,
    },
    listContainer: {
      padding: SPACING.lg,
    },
    vendorCard: {
      marginBottom: SPACING.md,
    },
    vendorHeader: {
      flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
      justifyContent: 'space-between',
      alignItems: 'flex-start',
      marginBottom: SPACING.sm,
    },
    vendorInfo: {
      flex: 1,
      marginRight: SPACING.md,
    },
    businessName: {
      fontSize: FONT_SIZES.lg,
      fontWeight: FONT_WEIGHTS.semibold,
      color: colors.textPrimary,
      marginBottom: SPACING.xs,
    },
    ownerName: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
      marginBottom: SPACING.xs,
    },
    email: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
    },
    description: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
      lineHeight: FONT_SIZES.sm * 1.4,
      marginBottom: SPACING.md,
    },
    vendorStats: {
      flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
      justifyContent: 'space-around',
      paddingVertical: SPACING.md,
      borderTopWidth: 1,
      borderBottomWidth: 1,
      borderColor: colors.borderLight,
      marginBottom: SPACING.md,
    },
    statItem: {
      alignItems: 'center',
    },
    statValue: {
      fontSize: FONT_SIZES.md,
      fontWeight: FONT_WEIGHTS.semibold,
      color: colors.textPrimary,
    },
    statLabel: {
      fontSize: FONT_SIZES.xs,
      color: colors.textSecondary,
      marginTop: SPACING.xs,
    },
    vendorFooter: {
      flexDirection: 'column',
      gap: SPACING.sm,
    },
    dateText: {
      fontSize: FONT_SIZES.xs,
      color: colors.textSecondary,
    },
    swipeHint: {
      fontSize: FONT_SIZES.xs,
      color: colors.primary,
      textAlign: 'center',
      fontStyle: 'italic',
      opacity: 0.8,
    },
    actionButtons: {
      flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
      gap: SPACING.sm,
      justifyContent: 'flex-end',
    },
    rejectButton: {
      borderColor: colors.error,
    },
    approveButton: {
      minWidth: 80,
    },
    swipeableContainer: {
      marginBottom: SPACING.md,
    },
  });
