import React, { useState } from 'react';
import { StyleSheet, Dimensions } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { RTLView, RTLText, RTLTouchableOpacity, RTLIcon, RTLFlatList } from '../RTL';
import { useThemedStyles, useVendors, useProducts, useI18n } from '../../hooks';
import { SPACING, FONT_SIZES, FONT_WEIGHTS, BORDER_RADIUS } from '../../constants/theme';
import { PREMIUM_GRADIENTS } from '../../constants/advancedColors';
import { SubtleGlow } from '../MoonlightEffects';
import type { ThemeColors } from '../../contexts/ThemeContext';

const { width: screenWidth } = Dimensions.get('window');
const TILE_WIDTH = (screenWidth - SPACING.md * 3) / 2;

interface CategoryExplorationProps {
  onCategoryPress: (category: string | null) => void;
  selectedCategory?: string | null;
}

interface CategoryData {
  key: string | null;
  name: string;
  icon: string;
  vendorCount: number;
  gradient: readonly string[];
  isPopular?: boolean;
}

const CATEGORY_ICONS: Record<string, string> = {
  'Electronics': 'phone-portrait-outline',
  'Fashion': 'shirt-outline',
  'Home & Garden': 'home-outline',
  'Sports': 'football-outline',
  'Books': 'book-outline',
  'Health': 'medical-outline',
  'Food': 'restaurant-outline',
  'Automotive': 'car-outline',
  'Beauty': 'flower-outline',
  'Toys': 'game-controller-outline',
  'Music': 'musical-notes-outline',
  'Art': 'brush-outline',
};

export const CategoryExploration: React.FC<CategoryExplorationProps> = ({
  onCategoryPress,
  selectedCategory,
}) => {
  const styles = useThemedStyles(createStyles);
  const { getApprovedVendors } = useVendors();
  const { getAllProducts } = useProducts();
  const { t } = useI18n();

  const allVendors = getApprovedVendors();
  const allProducts = getAllProducts();

  // Get unique categories with vendor counts
  const categories = React.useMemo(() => {
    const categoryMap = new Map<string, number>();
    
    allProducts.forEach(product => {
      if (product.isActive && product.category) {
        const count = categoryMap.get(product.category) || 0;
        const vendorIds = new Set();
        allProducts
          .filter(p => p.category === product.category && p.isActive)
          .forEach(p => vendorIds.add(p.vendorId));
        categoryMap.set(product.category, vendorIds.size);
      }
    });

    const categoryData: CategoryData[] = [
      {
        key: null,
        name: t('shops.allCategories'),
        icon: 'grid-outline',
        vendorCount: allVendors.length,
        gradient: PREMIUM_GRADIENTS.elegantDepth,
        isPopular: true,
      },
    ];

    Array.from(categoryMap.entries())
      .sort((a, b) => b[1] - a[1]) // Sort by vendor count
      .forEach(([category, vendorCount], index) => {
        categoryData.push({
          key: category,
          name: category,
          icon: CATEGORY_ICONS[category] || 'cube-outline',
          vendorCount,
          gradient: index % 2 === 0 ? PREMIUM_GRADIENTS.oceanDepth : PREMIUM_GRADIENTS.royalElegance,
          isPopular: vendorCount >= 3,
        });
      });

    return categoryData;
  }, [allVendors, allProducts, t]);

  const renderCategoryTile = ({ item }: { item: CategoryData }) => {
    const isSelected = selectedCategory === item.key;
    
    return (
      <RTLView style={styles.tileContainer}>
        <RTLTouchableOpacity
          style={[styles.tile, isSelected && styles.tileSelected]}
          onPress={() => onCategoryPress(item.key)}
          activeOpacity={0.8}
        >
          <SubtleGlow intensity={isSelected ? 0.8 : 0.4}>
            <LinearGradient
              colors={isSelected ? PREMIUM_GRADIENTS.royalSpotlight : item.gradient}
              style={styles.tileGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
            >
              {/* Popular Badge */}
              {item.isPopular && (
                <RTLView style={styles.popularBadge}>
                  <RTLIcon name="trending-up" size={12} color="#10B981" />
                </RTLView>
              )}

              {/* Icon */}
              <RTLView style={styles.iconContainer}>
                <RTLIcon 
                  name={item.icon} 
                  size={32} 
                  color={isSelected ? "#FFD700" : "#3B82F6"} 
                />
              </RTLView>

              {/* Content */}
              <RTLView style={styles.tileContent}>
                <RTLText style={styles.categoryName} numberOfLines={2}>
                  {item.name}
                </RTLText>
                <RTLText style={styles.vendorCount}>
                  {t('shops.vendorCount', { count: item.vendorCount })}
                </RTLText>
              </RTLView>

              {/* Selection Indicator */}
              {isSelected && (
                <RTLView style={styles.selectionIndicator}>
                  <RTLIcon name="checkmark-circle" size={20} color="#10B981" />
                </RTLView>
              )}
            </LinearGradient>
          </SubtleGlow>
        </RTLTouchableOpacity>
      </RTLView>
    );
  };

  return (
    <RTLView style={styles.container}>
      {/* Header */}
      <RTLView style={styles.header}>
        <RTLText style={styles.title}>{t('shops.exploreCategories')}</RTLText>
        <RTLText style={styles.subtitle}>{t('shops.findVendorsByCategory')}</RTLText>
      </RTLView>

      {/* Category Grid */}
      <RTLFlatList
        data={categories}
        renderItem={renderCategoryTile}
        keyExtractor={(item) => item.key || 'all'}
        numColumns={2}
        scrollEnabled={false}
        contentContainerStyle={styles.gridContent}
        columnWrapperStyle={styles.row}
      />
    </RTLView>
  );
};

const createStyles = (colors: ThemeColors) => StyleSheet.create({
  container: {
    marginBottom: SPACING.lg,
  },
  header: {
    paddingHorizontal: SPACING.md,
    marginBottom: SPACING.md,
  },
  title: {
    fontSize: FONT_SIZES.xl,
    fontWeight: FONT_WEIGHTS.bold,
    color: colors.textPrimary,
    marginBottom: SPACING.xs,
  },
  subtitle: {
    fontSize: FONT_SIZES.sm,
    color: colors.textSecondary,
  },
  gridContent: {
    paddingHorizontal: SPACING.md,
  },
  row: {
    justifyContent: 'space-between',
  },
  tileContainer: {
    width: TILE_WIDTH,
    marginBottom: SPACING.md,
  },
  tile: {
    height: 120,
    borderRadius: BORDER_RADIUS.xl,
    overflow: 'hidden',
  },
  tileSelected: {
    transform: [{ scale: 1.02 }],
  },
  tileGradient: {
    flex: 1,
    padding: SPACING.md,
    justifyContent: 'space-between',
  },
  popularBadge: {
    position: 'absolute',
    top: SPACING.sm,
    right: SPACING.sm,
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: 'rgba(16, 185, 129, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: BORDER_RADIUS.lg,
    backgroundColor: 'rgba(59, 130, 246, 0.1)',
    alignItems: 'center',
    justifyContent: 'center',
    alignSelf: 'flex-start',
  },
  tileContent: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  categoryName: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.semiBold,
    color: '#FFFFFF',
    marginBottom: SPACING.xs,
  },
  vendorCount: {
    fontSize: FONT_SIZES.xs,
    color: 'rgba(255, 255, 255, 0.8)',
    fontWeight: FONT_WEIGHTS.medium,
  },
  selectionIndicator: {
    position: 'absolute',
    bottom: SPACING.sm,
    right: SPACING.sm,
  },
});
