import React from 'react';
import { StyleSheet, ViewStyle } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { RTLView, RTLText } from './RTL';
import { useThemedStyles } from '../hooks';
import { PREMIUM_GRADIENTS, PREMIUM_SHADOWS, BRAND_COLORS } from '../constants/advancedColors';
import { DarkGlassBackground } from './DarkGradientBackground';
import { SubtleGlow } from './MoonlightEffects';
import { SPACING, BORDER_RADIUS, FONT_SIZES, FONT_WEIGHTS } from '../constants/theme';
import type ThemeColors  from '../contexts/ThemeContext';

export interface PremiumCardProps {
  title?: string;
  subtitle?: string;
  children?: React.ReactNode;
  variant?: 'elevated' | 'glass' | 'gradient' | 'aurora' | 'darkGlass' | 'moonlight';
  style?: ViewStyle;
  onPress?: () => void;
}

export const PremiumCard: React.FC<PremiumCardProps> = ({
  title,
  subtitle,
  children,
  variant = 'elevated',
  style,
  onPress,
}) => {
  const styles = useThemedStyles(createStyles);

  const renderContent = () => (
    <RTLView style={styles.content}>
      {title && (
        <RTLView style={styles.header}>
          <RTLText style={styles.title}>{title}</RTLText>
          {subtitle && <RTLText style={styles.subtitle}>{subtitle}</RTLText>}
        </RTLView>
      )}
      {children && <RTLView style={styles.body}>{children}</RTLView>}
    </RTLView>
  );

  const getGradientColors = () => {
    switch (variant) {
      case 'glass':
        return PREMIUM_GRADIENTS.glassLight;
      case 'gradient':
        return PREMIUM_GRADIENTS.cardElevated;
      case 'aurora':
        return PREMIUM_GRADIENTS.aurora;
      default:
        return PREMIUM_GRADIENTS.cardElevated;
    }
  };

  if (variant === 'darkGlass') {
    return (
      <DarkGlassBackground
        variant="elegant"
        style={[styles.base, styles[variant], style]}
      >
        {renderContent()}
      </DarkGlassBackground>
    );
  }

  if (variant === 'moonlight') {
    return (
      <SubtleGlow style={[styles.base, styles[variant], style]}>
        {renderContent()}
      </SubtleGlow>
    );
  }

  if (variant === 'glass' || variant === 'gradient' || variant === 'aurora') {
    return (
      <LinearGradient
        colors={getGradientColors()}
        style={[styles.base, styles[variant], style]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        {renderContent()}
      </LinearGradient>
    );
  }

  return (
    <RTLView style={[styles.base, styles[variant], style]}>
      {renderContent()}
    </RTLView>
  );
};

const createStyles = (colors: ThemeColors) =>
  StyleSheet.create({
    base: {
      borderRadius: BORDER_RADIUS.lg,
      padding: SPACING.lg,
      marginVertical: SPACING.sm,
    },
    elevated: {
      backgroundColor: colors.surface,
      ...PREMIUM_SHADOWS.lightMedium,
      borderWidth: 1,
      borderColor: colors.borderLight,
    },
    glass: {
      backgroundColor: 'transparent',
      borderWidth: 1,
      borderColor: 'rgba(59, 130, 246, 0.2)',
      ...PREMIUM_SHADOWS.lightSoft,
    },
    gradient: {
      backgroundColor: 'transparent',
      ...PREMIUM_SHADOWS.lightMedium,
    },
    aurora: {
      backgroundColor: 'transparent',
      ...PREMIUM_SHADOWS.blueShadow,
      borderWidth: 1,
      borderColor: 'rgba(59, 130, 246, 0.3)',
    },
    darkGlass: {
      backgroundColor: 'transparent',
      ...PREMIUM_SHADOWS.darkMedium,
      borderWidth: 1,
      borderColor: 'rgba(51, 65, 85, 0.4)',
    },
    moonlight: {
      backgroundColor: 'rgba(248, 250, 252, 0.05)',
      ...PREMIUM_SHADOWS.lightSoft,
      borderWidth: 1,
      borderColor: 'rgba(248, 250, 252, 0.1)',
      borderRadius: 16,
    },
    content: {
      flex: 1,
    },
    header: {
      marginBottom: SPACING.md,
    },
    title: {
      fontSize: FONT_SIZES.lg,
      fontWeight: FONT_WEIGHTS.bold,
      color: colors.textPrimary,
      marginBottom: SPACING.xs,
    },
    subtitle: {
      fontSize: FONT_SIZES.md,
      fontWeight: FONT_WEIGHTS.medium,
      color: colors.textSecondary,
    },
    body: {
      flex: 1,
    },
  });

// Premium Button Component with new colors
export interface PremiumButtonProps {
  title: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary' | 'aurora' | 'ocean';
  size?: 'small' | 'medium' | 'large';
  disabled?: boolean;
  style?: ViewStyle;
}

export const PremiumButton: React.FC<PremiumButtonProps> = ({
  title,
  onPress,
  variant = 'primary',
  size = 'medium',
  disabled = false,
  style,
}) => {
  const styles = useThemedStyles(createButtonStyles);

  const getGradientColors = () => {
    switch (variant) {
      case 'primary':
        return PREMIUM_GRADIENTS.buttonPrimary;
      case 'secondary':
        return PREMIUM_GRADIENTS.buttonSecondary;
      case 'aurora':
        return PREMIUM_GRADIENTS.aurora;
      case 'ocean':
        return PREMIUM_GRADIENTS.ocean;
      default:
        return PREMIUM_GRADIENTS.buttonPrimary;
    }
  };

  return (
    <LinearGradient
      colors={getGradientColors()}
      style={[styles.base, styles[size], disabled && styles.disabled, style]}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 0 }}
    >
      <RTLText style={[styles.text, styles[`${size}Text`], disabled && styles.disabledText]}>
        {title}
      </RTLText>
    </LinearGradient>
  );
};

const createButtonStyles = (colors: ThemeColors) =>
  StyleSheet.create({
    base: {
      borderRadius: BORDER_RADIUS.md,
      alignItems: 'center',
      justifyContent: 'center',
      ...PREMIUM_SHADOWS.lightMedium,
    },
    small: {
      height: 36,
      paddingHorizontal: SPACING.md,
    },
    medium: {
      height: 48,
      paddingHorizontal: SPACING.lg,
    },
    large: {
      height: 56,
      paddingHorizontal: SPACING.xl,
    },
    disabled: {
      opacity: 0.5,
    },
    text: {
      color: '#FFFFFF',
      fontWeight: FONT_WEIGHTS.semiBold,
      textAlign: 'center',
    },
    smallText: {
      fontSize: FONT_SIZES.sm,
    },
    mediumText: {
      fontSize: FONT_SIZES.md,
    },
    largeText: {
      fontSize: FONT_SIZES.lg,
    },
    disabledText: {
      color: 'rgba(255, 255, 255, 0.6)',
    },
  });

export default PremiumCard;
