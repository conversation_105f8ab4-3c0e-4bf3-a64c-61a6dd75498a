import React from 'react';
import { StyleSheet, Animated } from 'react-native';
import { RTLIcon, RTLInput, RTLSafeAreaView, RTLText, RTLTouchableOpacity, RTLView } from '../../components/RTL';
import { useThemedStyles, useVendors, useProducts, useI18n } from '../../hooks';
import {
  EmptyState,
  HeroCarousel,
  CategoryExploration,
  TrendingShops,
  CuratedCollections,
  EnhancedVendorCard,
  VendorPreviewModal,
  VendorStoryPreview,
  GlassmorphismCard,
  FadeInView,
  AnimatedPressable,
  DynamicThemeProvider,
  useDynamicTheme,
  GestureHandler,
  PullToDiscover,
  PinchToPreview,
  ShakeToShuffle,
  UltraSmoothScrollView,
  usePerformanceMonitor,
  useNetworkOptimizer,
  PerformanceMonitorProvider,
  IntelligentCacheProvider,
  NetworkOptimizerProvider,
  PersonalizationEngineProvider,
  usePersonalizationEngine,
  BehavioralAnalyticsProvider,
  useBehavioralAnalytics,
  SmartRecommendationDisplay
} from '../../components';
import { DarkGradientBackground } from '../../components/DarkGradientBackground';
import {
  SPACING,
  FONT_SIZES,
  FONT_WEIGHTS,
  BORDER_RADIUS } from '../../constants/theme';

import type { ThemeColors } from '../../contexts/ThemeContext';
import type { Vendor } from '../../contexts/DataContext';

interface ShopsScreenProps {
  navigation: any;
}

// Inner component that uses the dynamic theme
const ShopsScreenContent: React.FC<ShopsScreenProps> = ({ navigation }) => {
  const styles = useThemedStyles(createStyles);
  const { getApprovedVendors } = useVendors();
  const { getProductsByVendor } = useProducts();
  const { t } = useI18n();
  const { setThemeMode } = useDynamicTheme();
  const performanceMonitor = usePerformanceMonitor();
  const networkOptimizer = useNetworkOptimizer();
  const personalizationEngine = usePersonalizationEngine();
  const behavioralAnalytics = useBehavioralAnalytics();
  const [refreshing, setRefreshing] = React.useState(false);
  const [searchQuery, setSearchQuery] = React.useState('');
  const [selectedCategory, setSelectedCategory] = React.useState<string | null>(null);
  const [viewMode, setViewMode] = React.useState<'enhanced' | 'compact' | 'detailed'>('enhanced');
  const [previewVendor, setPreviewVendor] = React.useState<Vendor | null>(null);
  const [showPreviewModal, setShowPreviewModal] = React.useState(false);
  const [isDiscovering, setIsDiscovering] = React.useState(false);
  const [shuffleCount, setShuffleCount] = React.useState(0);
  const scrollY = React.useRef(new Animated.Value(0)).current;

  // Track screen view for behavioral analytics
  React.useEffect(() => {
    const endScreenView = behavioralAnalytics.trackScreenView('ShopsScreen');
    return endScreenView;
  }, [behavioralAnalytics]);

  const approvedVendors = getApprovedVendors();

  const onRefresh = React.useCallback(async () => {
    setRefreshing(true);
    // Simulate refresh delay with haptic feedback
    setTimeout(() => setRefreshing(false), 1000);
  }, []);

  const handleVendorPress = React.useCallback(async (vendorId: string) => {
    const endMeasurement = performanceMonitor.measureScreenTransition('VendorShop');

    // Track vendor interaction for personalization
    await personalizationEngine.trackBehavior({
      action: 'view',
      entityType: 'vendor',
      entityId: vendorId,
      metadata: {
        category: selectedCategory || undefined,
      },
    });

    // Track interaction for behavioral analytics
    await behavioralAnalytics.trackInteraction({
      type: 'tap',
      target: `vendor_${vendorId}`,
      metadata: {
        screen: 'ShopsScreen',
        category: selectedCategory,
      },
    });

    navigation.navigate('VendorShop', { vendorId });
    // End measurement after navigation completes
    setTimeout(endMeasurement, 100);
  }, [navigation, performanceMonitor, personalizationEngine, behavioralAnalytics, selectedCategory]);

  const handleCategoryPress = React.useCallback((category: string | null) => {
    setSelectedCategory(category);
  }, []);

  const handleVendorPreview = React.useCallback(async (vendorId: string) => {
    const vendor = approvedVendors.find(v => v.id === vendorId);
    if (vendor) {
      // Track preview interaction
      await personalizationEngine.trackBehavior({
        action: 'view',
        entityType: 'vendor',
        entityId: vendorId,
        metadata: {
          category: vendor.businessType,
        },
      });

      await behavioralAnalytics.trackInteraction({
        type: 'long_press',
        target: `vendor_preview_${vendorId}`,
        metadata: {
          screen: 'ShopsScreen',
          vendorType: vendor.businessType,
        },
      });

      setPreviewVendor(vendor);
      setShowPreviewModal(true);
    }
  }, [approvedVendors, personalizationEngine, behavioralAnalytics]);

  const handleClosePreview = React.useCallback(() => {
    setShowPreviewModal(false);
    setTimeout(() => setPreviewVendor(null), 300);
  }, []);

  const handleRefresh = React.useCallback(async () => {
    const endMeasurement = performanceMonitor.startInteractionMeasurement('refresh-shops');
    setRefreshing(true);

    try {
      // Use network optimizer for efficient data fetching
      await networkOptimizer.request({
        url: '/api/vendors/refresh',
        method: 'GET',
        priority: 'high',
        cacheKey: 'vendors-refresh',
        cacheTTL: 5 * 60 * 1000, // 5 minutes
      });

      // Simulate refresh with new content discovery
      await new Promise(resolve => setTimeout(resolve, 1500));
    } catch (error) {
      performanceMonitor.reportError(error as Error, 'network');
    } finally {
      setRefreshing(false);
      endMeasurement();
    }
  }, [performanceMonitor, networkOptimizer]);

  const handleDiscover = React.useCallback(async () => {
    setIsDiscovering(true);
    // Simulate discovering new vendors or content
    await new Promise(resolve => setTimeout(resolve, 1000));
    setIsDiscovering(false);
    // Could trigger theme change or show new vendors
    setThemeMode('vibrant');
  }, [setThemeMode]);

  const handleShuffle = React.useCallback(() => {
    setShuffleCount(prev => prev + 1);
    // Shuffle vendors or change sorting
    // Could randomize the vendor order or apply random filters
    console.log('Shuffling vendors...', shuffleCount);
  }, [shuffleCount]);

  const handleSwipeLeft = React.useCallback(() => {
    // Navigate to next category or filter
    const categories = ['Electronics', 'Fashion', 'Home & Garden', 'Sports'];
    const currentIndex = categories.indexOf(selectedCategory || '');
    const nextIndex = (currentIndex + 1) % categories.length;
    setSelectedCategory(categories[nextIndex]);
  }, [selectedCategory]);

  const handleSwipeRight = React.useCallback(() => {
    // Navigate to previous category or clear filter
    if (selectedCategory) {
      setSelectedCategory(null);
    }
  }, [selectedCategory]);

  const handleLongPress = React.useCallback(() => {
    // Show quick actions or context menu
    console.log('Long press detected - showing quick actions');
  }, []);

  const handleDoubleTap = React.useCallback(() => {
    // Quick action like favorite or bookmark
    console.log('Double tap detected - quick action');
  }, []);

  // Filter vendors based on search and category
  const filteredVendors = React.useMemo(() => {
    return approvedVendors.filter(vendor => {
      const matchesSearch = vendor.businessName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           vendor.businessDescription.toLowerCase().includes(searchQuery.toLowerCase());
      
      if (!selectedCategory) return matchesSearch;
      
      // Check if vendor has products in the selected category
      const vendorProducts = getProductsByVendor(vendor.id);
      const hasCategory = vendorProducts.some(product => product.category === selectedCategory);
      
      return matchesSearch && hasCategory;
    });
  }, [approvedVendors, searchQuery, selectedCategory, getProductsByVendor]);

  // Get all categories from all products
  const categories = React.useMemo(() => {
    const allCategories = new Set<string>();
    approvedVendors.forEach(vendor => {
      const vendorProducts = getProductsByVendor(vendor.id);
      vendorProducts.forEach(product => {
        allCategories.add(product.category);
      });
    });
    return Array.from(allCategories).sort();
  }, [approvedVendors, getProductsByVendor]);





  const renderEnhancedHeader = () => (
    <RTLView>
      {/* Hero Carousel - Featured Vendors */}
      {!searchQuery && !selectedCategory && (
        <HeroCarousel onVendorPress={handleVendorPress} />
      )}

      {/* Category Exploration */}
      {!searchQuery && (
        <CategoryExploration
          onCategoryPress={handleCategoryPress}
          selectedCategory={selectedCategory}
        />
      )}

      {/* Trending Shops */}
      {!searchQuery && !selectedCategory && (
        <TrendingShops onVendorPress={handleVendorPress} limit={8} />
      )}

      {/* Smart Recommendations */}
      {!searchQuery && (
        <SmartRecommendationDisplay
          type="mixed"
          title={t('ai.personalizedRecommendations')}
          subtitle={t('ai.basedOnYourActivity')}
          limit={8}
          layout="horizontal"
          showReasons={true}
          showConfidence={false}
          onRecommendationPress={async (recommendation) => {
            if (recommendation.type === 'vendor') {
              await handleVendorPress(recommendation.id);
            } else {
              // Handle product recommendation
              await behavioralAnalytics.trackInteraction({
                type: 'tap',
                target: `recommended_product_${recommendation.id}`,
                metadata: {
                  algorithm: recommendation.metadata.algorithm,
                  confidence: recommendation.confidence,
                },
              });
            }
          }}
          style={styles.recommendationsSection}
        />
      )}

      {/* Curated Collections */}
      {!searchQuery && !selectedCategory && (
        <CuratedCollections
          onVendorPress={handleVendorPress}
          onCollectionPress={(collectionType, vendors) => {
            // Handle collection press - could navigate to a dedicated collection screen
            console.log('Collection pressed:', collectionType, vendors.length);
          }}
        />
      )}

      {/* Vendor Stories */}
      {!searchQuery && !selectedCategory && (
        <VendorStoryPreview
          vendors={approvedVendors.slice(0, 10)}
          onVendorPress={handleVendorPress}
          onStoryPress={handleVendorPreview}
        />
      )}

      {/* Search and Filters */}
      <RTLView style={styles.searchSection}>
        <RTLView style={styles.searchContainer}>
          <RTLIcon name="search" size={20} color="#64748B" style={styles.searchIcon} />
          <RTLInput
            style={styles.searchInput}
            placeholder={t('shops.searchShops')}
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholderTextColor="#94A3B8"
          />
          {searchQuery.length > 0 && (
            <RTLTouchableOpacity
              style={styles.clearButton}
              onPress={() => setSearchQuery('')}
            >
              <RTLIcon name="close-circle" size={20} color="#94A3B8" />
            </RTLTouchableOpacity>
          )}
        </RTLView>

        {/* View Mode Toggle */}
        <RTLView style={styles.viewModeContainer}>
          <RTLTouchableOpacity
            style={[styles.viewModeButton, ...(viewMode === 'enhanced' ? [styles.viewModeActive] : [])]}
            onPress={() => setViewMode('enhanced')}
          >
            <RTLIcon name="grid" size={16} color={viewMode === 'enhanced' ? '#3B82F6' : '#64748B'} />
          </RTLTouchableOpacity>
          <RTLTouchableOpacity
            style={[styles.viewModeButton, ...(viewMode === 'detailed' ? [styles.viewModeActive] : [])]}
            onPress={() => setViewMode('detailed')}
          >
            <RTLIcon name="list" size={16} color={viewMode === 'detailed' ? '#3B82F6' : '#64748B'} />
          </RTLTouchableOpacity>
          <RTLTouchableOpacity
            style={[styles.viewModeButton, ...(viewMode === 'compact' ? [styles.viewModeActive] : [])]}
            onPress={() => setViewMode('compact')}
          >
            <RTLIcon name="menu" size={16} color={viewMode === 'compact' ? '#3B82F6' : '#64748B'} />
          </RTLTouchableOpacity>
        </RTLView>
      </RTLView>

      {/* Active Filters */}
      {(searchQuery || selectedCategory) && (
        <RTLView style={styles.activeFilters}>
          <RTLText style={styles.activeFiltersTitle}>{t('shops.activeFilters')}</RTLText>
          <RTLView style={styles.filterChips}>
            {searchQuery && (
              <RTLView style={styles.filterChip}>
                <RTLText style={styles.filterChipText}>"{searchQuery}"</RTLText>
                <RTLTouchableOpacity onPress={() => setSearchQuery('')}>
                  <RTLIcon name="close" size={14} color="#64748B" />
                </RTLTouchableOpacity>
              </RTLView>
            )}
            {selectedCategory && (
              <RTLView style={styles.filterChip}>
                <RTLText style={styles.filterChipText}>{selectedCategory}</RTLText>
                <RTLTouchableOpacity onPress={() => setSelectedCategory(null)}>
                  <RTLIcon name="close" size={14} color="#64748B" />
                </RTLTouchableOpacity>
              </RTLView>
            )}
          </RTLView>
        </RTLView>
      )}

      {/* Results Header */}
      {(searchQuery || selectedCategory) && (
        <RTLView style={styles.resultsHeader}>
          <RTLText style={styles.resultsTitle}>
            {t('shops.searchResults', { count: filteredVendors.length })}
          </RTLText>
        </RTLView>
      )}
    </RTLView>
  );

  const renderEnhancedVendorCard = ({ item }: { item: Vendor }) => (
    <EnhancedVendorCard
      vendor={item}
      onPress={handleVendorPress}
      onPreview={handleVendorPreview}
      layout={viewMode === 'enhanced' ? 'detailed' : viewMode}
      showLiveActivity={true}
    />
  );



  return (
    <DarkGradientBackground variant="elegant">
      <RTLSafeAreaView style={styles.container}>
          {/* Shake to Shuffle Indicator */}
          <ShakeToShuffle
            onShuffle={handleShuffle}
            enabled={!refreshing && !isDiscovering}
            indicatorPosition="top-right"
          />

          {/* Main Content with Gesture Support */}
          <GestureHandler
            onSwipeLeft={handleSwipeLeft}
            onSwipeRight={handleSwipeRight}
            onLongPress={handleLongPress}
            onDoubleTap={handleDoubleTap}
            enableSwipe={true}
            enableLongPress={true}
            enableDoubleTap={true}
            hapticFeedback={true}
          >
            <PullToDiscover
              onRefresh={handleRefresh}
              onDiscover={handleDiscover}
              refreshing={refreshing}
              discovering={isDiscovering}
              enableDiscover={true}
            >
              <UltraSmoothScrollView
                enableVirtualization={approvedVendors.length > 20}
                itemHeight={200}
                windowSize={10}
                maxToRenderPerBatch={5}
                removeClippedSubviews={true}
                scrollEventThrottle={16}
                enableMomentumScrolling={true}
                onScroll={Animated.event(
                  [{ nativeEvent: { contentOffset: { y: scrollY } } }],
                  { useNativeDriver: false }
                )}
              >
            <FadeInView duration={800} delay={200}>
              {renderEnhancedHeader()}
            </FadeInView>

            {/* Vendor List with Glassmorphism Cards */}
            <RTLView style={styles.vendorListContainer}>
              {filteredVendors.length > 0 ? (
                filteredVendors.map((vendor, index) => (
                  <FadeInView
                    key={vendor.id}
                    duration={600}
                    delay={index * 100}
                    direction="up"
                  >
                    <PinchToPreview
                      onPreviewOpen={() => handleVendorPreview(vendor.id)}
                      onPreviewAction={() => handleVendorPress(vendor.id)}
                      enablePinch={true}
                      hapticFeedback={true}
                    >
                      <GlassmorphismCard
                        intensity="medium"
                        style={styles.vendorCardGlass}
                        borderRadius={20}
                      >
                        <AnimatedPressable
                          onPress={() => handleVendorPress(vendor.id)}
                          onLongPress={() => handleVendorPreview(vendor.id)}
                        >
                          {renderEnhancedVendorCard({ item: vendor })}
                        </AnimatedPressable>
                      </GlassmorphismCard>
                    </PinchToPreview>
                  </FadeInView>
                ))
              ) : (
                <FadeInView duration={800}>
                  <GlassmorphismCard intensity="light" style={styles.emptyStateCard}>
                    <EmptyState
                      icon="storefront-outline"
                      title={t('shops.noShopsFound')}
                      description={
                        searchQuery || selectedCategory
                          ? t('shops.adjustSearchFilter')
                          : t('shops.noApprovedShops')
                      }
                    />
                  </GlassmorphismCard>
                </FadeInView>
              )}
            </RTLView>
              </UltraSmoothScrollView>
            </PullToDiscover>
          </GestureHandler>

          {/* Vendor Preview Modal */}
          <VendorPreviewModal
            vendor={previewVendor}
            visible={showPreviewModal}
            onClose={handleClosePreview}
            onVisitShop={handleVendorPress}
          />
        </RTLSafeAreaView>
      </DarkGradientBackground>
  );
};

// Main component that wraps the content with all necessary providers
export const ShopsScreen: React.FC<ShopsScreenProps> = (props) => {
  return (
    <DynamicThemeProvider initialTheme="elegant">
      <PerformanceMonitorProvider>
        <IntelligentCacheProvider>
          <NetworkOptimizerProvider>
            <PersonalizationEngineProvider>
              <BehavioralAnalyticsProvider>
                <ShopsScreenContent {...props} />
              </BehavioralAnalyticsProvider>
            </PersonalizationEngineProvider>
          </NetworkOptimizerProvider>
        </IntelligentCacheProvider>
      </PerformanceMonitorProvider>
    </DynamicThemeProvider>
  );
};

const createStyles = (colors: ThemeColors) =>
  StyleSheet.create({
    container: {
      flex: 1,
    },
    scrollView: {
      flex: 1,
    },
    scrollContent: {
      paddingBottom: SPACING.xl,
    },

    // Search Section
    searchSection: {
      paddingHorizontal: SPACING.md,
      marginBottom: SPACING.lg,
    },
    searchContainer: {
      flexDirection: 'row', // RTL handled by RTLView
      alignItems: 'center',
      backgroundColor: 'rgba(255, 255, 255, 0.1)',
      borderRadius: BORDER_RADIUS.lg,
      paddingHorizontal: SPACING.md,
      paddingVertical: SPACING.md,
      marginBottom: SPACING.md,
      borderWidth: 1,
      borderColor: 'rgba(59, 130, 246, 0.3)',
    },
    searchIcon: {
      marginEnd: SPACING.sm, // RTL-aware
    },
    searchInput: {
      flex: 1,
      fontSize: FONT_SIZES.md,
      color: '#FFFFFF',
      fontWeight: FONT_WEIGHTS.medium,
    },
    clearButton: {
      marginStart: SPACING.sm, // RTL-aware
    },

    // View Mode Toggle
    viewModeContainer: {
      flexDirection: 'row', // RTL handled by RTLView
      backgroundColor: 'rgba(255, 255, 255, 0.1)',
      borderRadius: BORDER_RADIUS.md,
      padding: SPACING.xs,
    },
    viewModeButton: {
      flex: 1,
      alignItems: 'center',
      paddingVertical: SPACING.sm,
      borderRadius: BORDER_RADIUS.sm,
    },
    viewModeActive: {
      backgroundColor: 'rgba(59, 130, 246, 0.3)',
    },

    // Active Filters
    activeFilters: {
      paddingHorizontal: SPACING.md,
      marginBottom: SPACING.md,
    },
    activeFiltersTitle: {
      fontSize: FONT_SIZES.sm,
      fontWeight: FONT_WEIGHTS.semiBold,
      color: 'rgba(255, 255, 255, 0.8)',
      marginBottom: SPACING.sm,
    },
    filterChips: {
      flexDirection: 'row', // RTL handled by RTLView
      flexWrap: 'wrap',
    },
    filterChip: {
      flexDirection: 'row', // RTL handled by RTLView
      alignItems: 'center',
      backgroundColor: 'rgba(59, 130, 246, 0.2)',
      paddingHorizontal: SPACING.md,
      paddingVertical: SPACING.sm,
      borderRadius: BORDER_RADIUS.md,
      marginEnd: SPACING.sm, // RTL-aware
      marginBottom: SPACING.sm,
      borderWidth: 1,
      borderColor: '#3B82F6',
    },
    filterChipText: {
      fontSize: FONT_SIZES.sm,
      color: '#3B82F6',
      marginEnd: SPACING.sm, // RTL-aware
      fontWeight: FONT_WEIGHTS.medium,
    },

    // Results Header
    resultsHeader: {
      paddingHorizontal: SPACING.md,
      marginBottom: SPACING.md,
    },
    resultsTitle: {
      fontSize: FONT_SIZES.lg,
      fontWeight: FONT_WEIGHTS.bold,
      color: '#FFFFFF',
    },

    // Vendor List
    vendorListContainer: {
      paddingHorizontal: SPACING.md,
    },

    // Parallax Header
    parallaxHeaderContent: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: SPACING.lg,
    },
    headerTextContainer: {
      alignItems: 'center',
      marginBottom: SPACING.xl,
    },
    headerTitle: {
      fontSize: FONT_SIZES.xxl,
      fontWeight: FONT_WEIGHTS.bold,
      color: '#FFFFFF',
      textAlign: 'center',
      marginBottom: SPACING.sm,
      textShadowColor: 'rgba(0, 0, 0, 0.3)',
      textShadowOffset: { width: 0, height: 2 },
      textShadowRadius: 4,
    },
    headerSubtitle: {
      fontSize: FONT_SIZES.md,
      color: 'rgba(255, 255, 255, 0.9)',
      textAlign: 'center',
      lineHeight: 22,
      textShadowColor: 'rgba(0, 0, 0, 0.2)',
      textShadowOffset: { width: 0, height: 1 },
      textShadowRadius: 2,
    },
    floatingElements: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
    },
    floatingIcon: {
      position: 'absolute',
      width: 48,
      height: 48,
      borderRadius: 24,
      backgroundColor: 'rgba(255, 255, 255, 0.1)',
      alignItems: 'center',
      justifyContent: 'center',
      borderWidth: 1,
      borderColor: 'rgba(255, 255, 255, 0.2)',
    },
    floatingIcon1: {
      top: '20%',
      right: '15%',
    },
    floatingIcon2: {
      bottom: '25%',
      left: '10%',
    },

    // Glassmorphism Cards
    vendorCardGlass: {
      marginBottom: SPACING.md,
    },
    emptyStateCard: {
      marginTop: SPACING.xl,
      padding: SPACING.xl,
    },
    recommendationsSection: {
      marginVertical: SPACING.lg,
    },
  });
