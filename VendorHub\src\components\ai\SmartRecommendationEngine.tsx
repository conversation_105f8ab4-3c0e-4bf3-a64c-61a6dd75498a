import React, { createContext, useContext, useRef, useCallback, useEffect } from 'react';
import { usePersonalizationEngine } from './PersonalizationEngine';
import { useVendors, useProducts } from '../../hooks';
import { useIntelligentCache } from '../performance/IntelligentCache';
import type { Vendor, Product } from '../../contexts/DataContext';

interface RecommendationAlgorithm {
  name: string;
  weight: number;
  enabled: boolean;
  compute: (context: RecommendationContext) => Promise<ScoredItem[]>;
}

interface ScoredItem {
  id: string;
  type: 'vendor' | 'product';
  score: number;
  reasons: string[];
  data: Vendor | Product;
  algorithm: string;
}

interface RecommendationContext {
  userId?: string;
  currentVendor?: Vendor;
  currentProduct?: Product;
  currentCategory?: string;
  searchQuery?: string;
  timeContext?: 'morning' | 'afternoon' | 'evening' | 'night';
  locationContext?: string;
  priceRange?: [number, number];
  excludeIds?: string[];
  limit?: number;
}

interface SmartRecommendation {
  id: string;
  type: 'vendor' | 'product';
  title: string;
  subtitle: string;
  score: number;
  confidence: number;
  reasons: string[];
  data: Vendor | Product;
  metadata: {
    algorithm: string;
    category?: string;
    priceRange?: string;
    trending?: boolean;
    personalized?: boolean;
    cultural?: boolean;
  };
}

interface RecommendationEngineConfig {
  enablePersonalization: boolean;
  enableTrendingAnalysis: boolean;
  enableSeasonalRecommendations: boolean;
  enableCulturalAdaptation: boolean;
  enableCollaborativeFiltering: boolean;
  enableContentBasedFiltering: boolean;
  enableHybridApproach: boolean;
  maxRecommendations: number;
  minConfidenceThreshold: number;
  diversityFactor: number;
  freshnessWeight: number;
}

interface SmartRecommendationEngineContextType {
  getRecommendations: (context: RecommendationContext) => Promise<SmartRecommendation[]>;
  getPersonalizedVendors: (limit?: number) => Promise<SmartRecommendation[]>;
  getPersonalizedProducts: (limit?: number) => Promise<SmartRecommendation[]>;
  getTrendingRecommendations: (type: 'vendor' | 'product', limit?: number) => Promise<SmartRecommendation[]>;
  getSimilarItems: (itemId: string, type: 'vendor' | 'product', limit?: number) => Promise<SmartRecommendation[]>;
  getSeasonalRecommendations: (limit?: number) => Promise<SmartRecommendation[]>;
  getCulturalRecommendations: (culture: string, limit?: number) => Promise<SmartRecommendation[]>;
  refreshRecommendations: () => Promise<void>;
  getRecommendationInsights: () => Promise<any>;
}

const DEFAULT_CONFIG: RecommendationEngineConfig = {
  enablePersonalization: true,
  enableTrendingAnalysis: true,
  enableSeasonalRecommendations: true,
  enableCulturalAdaptation: true,
  enableCollaborativeFiltering: false, // Requires backend
  enableContentBasedFiltering: true,
  enableHybridApproach: true,
  maxRecommendations: 20,
  minConfidenceThreshold: 0.3,
  diversityFactor: 0.3,
  freshnessWeight: 0.2,
};

const SmartRecommendationEngineContext = createContext<SmartRecommendationEngineContextType | undefined>(undefined);

export const useSmartRecommendationEngine = (): SmartRecommendationEngineContextType => {
  const context = useContext(SmartRecommendationEngineContext);
  if (!context) {
    throw new Error('useSmartRecommendationEngine must be used within a SmartRecommendationEngineProvider');
  }
  return context;
};

interface SmartRecommendationEngineProviderProps {
  children: React.ReactNode;
  config?: Partial<RecommendationEngineConfig>;
}

export const SmartRecommendationEngineProvider: React.FC<SmartRecommendationEngineProviderProps> = ({
  children,
  config = {},
}) => {
  const finalConfig = useRef({ ...DEFAULT_CONFIG, ...config });
  const personalizationEngine = usePersonalizationEngine();
  const { getApprovedVendors } = useVendors();
  const { getAllProducts } = useProducts();
  const cache = useIntelligentCache();

  // Algorithm registry
  const algorithms = useRef<RecommendationAlgorithm[]>([]);
  
  // Trending data
  const trendingData = useRef<{
    vendors: Map<string, number>;
    products: Map<string, number>;
    categories: Map<string, number>;
    lastUpdate: number;
  }>({
    vendors: new Map(),
    products: new Map(),
    categories: new Map(),
    lastUpdate: 0,
  });

  useEffect(() => {
    initializeAlgorithms();
    updateTrendingData();
    
    // Update trending data every hour
    const trendingInterval = setInterval(updateTrendingData, 60 * 60 * 1000);
    
    return () => clearInterval(trendingInterval);
  }, []);

  const initializeAlgorithms = () => {
    algorithms.current = [
      {
        name: 'personalized',
        weight: 0.4,
        enabled: finalConfig.current.enablePersonalization,
        compute: computePersonalizedRecommendations,
      },
      {
        name: 'trending',
        weight: 0.2,
        enabled: finalConfig.current.enableTrendingAnalysis,
        compute: computeTrendingRecommendations,
      },
      {
        name: 'content-based',
        weight: 0.2,
        enabled: finalConfig.current.enableContentBasedFiltering,
        compute: computeContentBasedRecommendations,
      },
      {
        name: 'seasonal',
        weight: 0.1,
        enabled: finalConfig.current.enableSeasonalRecommendations,
        compute: computeSeasonalRecommendations,
      },
      {
        name: 'cultural',
        weight: 0.1,
        enabled: finalConfig.current.enableCulturalAdaptation,
        compute: computeCulturalRecommendations,
      },
    ];
  };

  const updateTrendingData = async () => {
    try {
      // Simulate trending analysis (in production, this would come from analytics)
      const vendors = getApprovedVendors();
      const products = getAllProducts();
      
      // Simple trending simulation based on rating and activity
      vendors.forEach(vendor => {
        const trendingScore = vendor.rating * Math.random() * 10;
        trendingData.current.vendors.set(vendor.id, trendingScore);
      });
      
      products.forEach(product => {
        const trendingScore = (product.isActive ? 1 : 0.5) * Math.random() * 10;
        trendingData.current.products.set(product.id, trendingScore);
      });
      
      trendingData.current.lastUpdate = Date.now();
      
      // Cache trending data
      await cache.set('trending-data', trendingData.current, {
        ttl: 60 * 60 * 1000, // 1 hour
        tags: ['trending', 'recommendations'],
      });
    } catch (error) {
      console.warn('Failed to update trending data:', error);
    }
  };

  const computePersonalizedRecommendations = async (
    context: RecommendationContext
  ): Promise<ScoredItem[]> => {
    try {
      const preferences = await personalizationEngine.getUserPreferences();
      const vendors = getApprovedVendors();
      const products = getAllProducts();
      const results: ScoredItem[] = [];

      // Score vendors based on user preferences
      vendors.forEach(vendor => {
        let score = 0;
        const reasons: string[] = [];

        // Category preference
        if (vendor.businessType && preferences.categories[vendor.businessType]) {
          score += preferences.categories[vendor.businessType] * 0.4;
          reasons.push(`Matches your interest in ${vendor.businessType}`);
        }

        // Vendor preference
        if (preferences.vendors[vendor.id]) {
          score += preferences.vendors[vendor.id] * 0.6;
          reasons.push('Based on your previous interactions');
        }

        // Rating boost
        score += vendor.rating * 0.1;

        if (score > 0 && !context.excludeIds?.includes(vendor.id)) {
          results.push({
            id: vendor.id,
            type: 'vendor',
            score,
            reasons,
            data: vendor,
            algorithm: 'personalized',
          });
        }
      });

      // Score products based on user preferences
      products.forEach(product => {
        let score = 0;
        const reasons: string[] = [];

        // Category preference
        if (product.category && preferences.categories[product.category]) {
          score += preferences.categories[product.category] * 0.4;
          reasons.push(`Matches your interest in ${product.category}`);
        }

        // Price range preference
        const priceRange = getPriceRange(product.price);
        if (preferences.priceRanges[priceRange]) {
          score += preferences.priceRanges[priceRange] * 0.3;
          reasons.push(`Fits your preferred price range`);
        }

        // Vendor preference
        if (preferences.vendors[product.vendorId]) {
          score += preferences.vendors[product.vendorId] * 0.3;
          reasons.push('From a vendor you like');
        }

        if (score > 0 && !context.excludeIds?.includes(product.id)) {
          results.push({
            id: product.id,
            type: 'product',
            score,
            reasons,
            data: product,
            algorithm: 'personalized',
          });
        }
      });

      return results.sort((a, b) => b.score - a.score);
    } catch (error) {
      console.warn('Personalized recommendations failed:', error);
      return [];
    }
  };

  const computeTrendingRecommendations = async (
    context: RecommendationContext
  ): Promise<ScoredItem[]> => {
    const results: ScoredItem[] = [];
    const vendors = getApprovedVendors();
    const products = getAllProducts();

    // Get trending vendors
    vendors.forEach(vendor => {
      const trendingScore = trendingData.current.vendors.get(vendor.id) || 0;
      if (trendingScore > 5 && !context.excludeIds?.includes(vendor.id)) {
        results.push({
          id: vendor.id,
          type: 'vendor',
          score: trendingScore,
          reasons: ['Currently trending'],
          data: vendor,
          algorithm: 'trending',
        });
      }
    });

    // Get trending products
    products.forEach(product => {
      const trendingScore = trendingData.current.products.get(product.id) || 0;
      if (trendingScore > 5 && !context.excludeIds?.includes(product.id)) {
        results.push({
          id: product.id,
          type: 'product',
          score: trendingScore,
          reasons: ['Popular right now'],
          data: product,
          algorithm: 'trending',
        });
      }
    });

    return results.sort((a, b) => b.score - a.score);
  };

  const computeContentBasedRecommendations = async (
    context: RecommendationContext
  ): Promise<ScoredItem[]> => {
    const results: ScoredItem[] = [];
    
    if (context.currentVendor) {
      // Find similar vendors
      const vendors = getApprovedVendors();
      vendors.forEach(vendor => {
        if (vendor.id !== context.currentVendor!.id) {
          const similarity = calculateVendorSimilarity(context.currentVendor!, vendor);
          if (similarity > 0.3) {
            results.push({
              id: vendor.id,
              type: 'vendor',
              score: similarity * 10,
              reasons: [`Similar to ${context.currentVendor!.businessName}`],
              data: vendor,
              algorithm: 'content-based',
            });
          }
        }
      });
    }

    if (context.currentProduct) {
      // Find similar products
      const products = getAllProducts();
      products.forEach(product => {
        if (product.id !== context.currentProduct!.id) {
          const similarity = calculateProductSimilarity(context.currentProduct!, product);
          if (similarity > 0.3) {
            results.push({
              id: product.id,
              type: 'product',
              score: similarity * 10,
              reasons: [`Similar to ${context.currentProduct!.name}`],
              data: product,
              algorithm: 'content-based',
            });
          }
        }
      });
    }

    return results.sort((a, b) => b.score - a.score);
  };

  const computeSeasonalRecommendations = async (
    context: RecommendationContext
  ): Promise<ScoredItem[]> => {
    const results: ScoredItem[] = [];
    const currentMonth = new Date().getMonth();
    const season = getSeason(currentMonth);
    
    const vendors = getApprovedVendors();
    const products = getAllProducts();

    // Seasonal vendor recommendations
    vendors.forEach(vendor => {
      const seasonalScore = getSeasonalScore(vendor, season);
      if (seasonalScore > 0) {
        results.push({
          id: vendor.id,
          type: 'vendor',
          score: seasonalScore,
          reasons: [`Perfect for ${season}`],
          data: vendor,
          algorithm: 'seasonal',
        });
      }
    });

    // Seasonal product recommendations
    products.forEach(product => {
      const seasonalScore = getSeasonalScore(product, season);
      if (seasonalScore > 0) {
        results.push({
          id: product.id,
          type: 'product',
          score: seasonalScore,
          reasons: [`Great for ${season}`],
          data: product,
          algorithm: 'seasonal',
        });
      }
    });

    return results.sort((a, b) => b.score - a.score);
  };

  const computeCulturalRecommendations = async (
    context: RecommendationContext
  ): Promise<ScoredItem[]> => {
    const results: ScoredItem[] = [];
    const preferences = await personalizationEngine.getUserPreferences();
    const culturalPrefs = preferences.culturalPreferences;
    
    const vendors = getApprovedVendors();
    const products = getAllProducts();

    // Cultural vendor recommendations
    vendors.forEach(vendor => {
      const culturalScore = getCulturalScore(vendor, culturalPrefs);
      if (culturalScore > 0) {
        results.push({
          id: vendor.id,
          type: 'vendor',
          score: culturalScore,
          reasons: ['Matches your cultural preferences'],
          data: vendor,
          algorithm: 'cultural',
        });
      }
    });

    return results.sort((a, b) => b.score - a.score);
  };

  // Helper functions
  const getPriceRange = (price: number): string => {
    if (price < 10) return '0-10';
    if (price < 25) return '10-25';
    if (price < 50) return '25-50';
    if (price < 100) return '50-100';
    return '100+';
  };

  const calculateVendorSimilarity = (vendor1: Vendor, vendor2: Vendor): number => {
    let similarity = 0;
    
    // Business type similarity
    if (vendor1.businessType === vendor2.businessType) {
      similarity += 0.4;
    }
    
    // Rating similarity
    const ratingDiff = Math.abs(vendor1.rating - vendor2.rating);
    similarity += (1 - ratingDiff / 5) * 0.3;
    
    // Location similarity (simplified)
    if (vendor1.location === vendor2.location) {
      similarity += 0.3;
    }
    
    return similarity;
  };

  const calculateProductSimilarity = (product1: Product, product2: Product): number => {
    let similarity = 0;
    
    // Category similarity
    if (product1.category === product2.category) {
      similarity += 0.5;
    }
    
    // Price similarity
    const priceDiff = Math.abs(product1.price - product2.price);
    const maxPrice = Math.max(product1.price, product2.price);
    similarity += (1 - priceDiff / maxPrice) * 0.3;
    
    // Vendor similarity
    if (product1.vendorId === product2.vendorId) {
      similarity += 0.2;
    }
    
    return similarity;
  };

  const getSeason = (month: number): string => {
    if (month >= 2 && month <= 4) return 'spring';
    if (month >= 5 && month <= 7) return 'summer';
    if (month >= 8 && month <= 10) return 'autumn';
    return 'winter';
  };

  const getSeasonalScore = (item: any, season: string): number => {
    // Simplified seasonal scoring
    const seasonalKeywords = {
      spring: ['fresh', 'new', 'garden', 'outdoor'],
      summer: ['cool', 'beach', 'vacation', 'outdoor'],
      autumn: ['warm', 'cozy', 'indoor', 'comfort'],
      winter: ['warm', 'indoor', 'holiday', 'gift'],
    };
    
    const keywords = seasonalKeywords[season as keyof typeof seasonalKeywords] || [];
    const description = (item.businessDescription || item.description || '').toLowerCase();
    
    let score = 0;
    keywords.forEach(keyword => {
      if (description.includes(keyword)) {
        score += 2;
      }
    });
    
    return Math.min(score, 10);
  };

  const getCulturalScore = (vendor: Vendor, culturalPrefs: any): number => {
    let score = 0;
    
    // Local brand preference
    if (vendor.isLocal && culturalPrefs.localBrands > 0.5) {
      score += culturalPrefs.localBrands * 5;
    }
    
    // Traditional vs modern preference
    const isTraditional = vendor.businessType?.includes('traditional') || 
                         vendor.businessDescription?.includes('traditional');
    
    if (isTraditional && culturalPrefs.traditionalProducts > 0.5) {
      score += culturalPrefs.traditionalProducts * 5;
    } else if (!isTraditional && culturalPrefs.modernProducts > 0.5) {
      score += culturalPrefs.modernProducts * 5;
    }
    
    return score;
  };

  const combineRecommendations = (algorithmResults: ScoredItem[][]): SmartRecommendation[] => {
    const combinedScores = new Map<string, {
      item: ScoredItem;
      totalScore: number;
      algorithms: string[];
      reasons: Set<string>;
    }>();

    // Combine scores from all algorithms
    algorithmResults.forEach((results, algorithmIndex) => {
      const algorithm = algorithms.current[algorithmIndex];
      if (!algorithm.enabled) return;

      results.forEach(item => {
        const key = `${item.type}-${item.id}`;
        const weightedScore = item.score * algorithm.weight;
        
        if (combinedScores.has(key)) {
          const existing = combinedScores.get(key)!;
          existing.totalScore += weightedScore;
          existing.algorithms.push(algorithm.name);
          item.reasons.forEach(reason => existing.reasons.add(reason));
        } else {
          combinedScores.set(key, {
            item,
            totalScore: weightedScore,
            algorithms: [algorithm.name],
            reasons: new Set(item.reasons),
          });
        }
      });
    });

    // Convert to SmartRecommendation format
    const recommendations: SmartRecommendation[] = Array.from(combinedScores.values())
      .filter(({ totalScore }) => totalScore >= finalConfig.current.minConfidenceThreshold)
      .sort((a, b) => b.totalScore - a.totalScore)
      .slice(0, finalConfig.current.maxRecommendations)
      .map(({ item, totalScore, algorithms, reasons }) => ({
        id: item.id,
        type: item.type,
        title: item.type === 'vendor' ? 
          (item.data as Vendor).businessName : 
          (item.data as Product).name,
        subtitle: item.type === 'vendor' ? 
          (item.data as Vendor).businessDescription || 'Vendor' :
          `BHD ${(item.data as Product).price.toFixed(2)}`,
        score: totalScore,
        confidence: Math.min(totalScore / 10, 1),
        reasons: Array.from(reasons),
        data: item.data,
        metadata: {
          algorithm: algorithms.join(', '),
          category: item.type === 'vendor' ? 
            (item.data as Vendor).businessType : 
            (item.data as Product).category,
          trending: algorithms.includes('trending'),
          personalized: algorithms.includes('personalized'),
          cultural: algorithms.includes('cultural'),
        },
      }));

    return recommendations;
  };

  const getRecommendations = useCallback(async (
    context: RecommendationContext
  ): Promise<SmartRecommendation[]> => {
    try {
      const enabledAlgorithms = algorithms.current.filter(alg => alg.enabled);
      const algorithmResults = await Promise.all(
        enabledAlgorithms.map(algorithm => algorithm.compute(context))
      );

      return combineRecommendations(algorithmResults);
    } catch (error) {
      console.warn('Failed to get recommendations:', error);
      return [];
    }
  }, []);

  const getPersonalizedVendors = useCallback(async (limit = 10): Promise<SmartRecommendation[]> => {
    const recommendations = await getRecommendations({ limit });
    return recommendations.filter(rec => rec.type === 'vendor').slice(0, limit);
  }, [getRecommendations]);

  const getPersonalizedProducts = useCallback(async (limit = 10): Promise<SmartRecommendation[]> => {
    const recommendations = await getRecommendations({ limit });
    return recommendations.filter(rec => rec.type === 'product').slice(0, limit);
  }, [getRecommendations]);

  const getTrendingRecommendations = useCallback(async (
    type: 'vendor' | 'product',
    limit = 10
  ): Promise<SmartRecommendation[]> => {
    const trendingResults = await computeTrendingRecommendations({});
    const filtered = trendingResults.filter(item => item.type === type).slice(0, limit);
    
    return filtered.map(item => ({
      id: item.id,
      type: item.type,
      title: item.type === 'vendor' ? 
        (item.data as Vendor).businessName : 
        (item.data as Product).name,
      subtitle: item.type === 'vendor' ? 
        (item.data as Vendor).businessDescription || 'Vendor' :
        `BHD ${(item.data as Product).price.toFixed(2)}`,
      score: item.score,
      confidence: Math.min(item.score / 10, 1),
      reasons: item.reasons,
      data: item.data,
      metadata: {
        algorithm: 'trending',
        trending: true,
        personalized: false,
        cultural: false,
      },
    }));
  }, []);

  const getSimilarItems = useCallback(async (
    itemId: string,
    type: 'vendor' | 'product',
    limit = 10
  ): Promise<SmartRecommendation[]> => {
    const vendors = getApprovedVendors();
    const products = getAllProducts();
    
    let currentItem: Vendor | Product | undefined;
    
    if (type === 'vendor') {
      currentItem = vendors.find(v => v.id === itemId);
    } else {
      currentItem = products.find(p => p.id === itemId);
    }
    
    if (!currentItem) return [];
    
    const context: RecommendationContext = {
      excludeIds: [itemId],
      limit,
    };
    
    if (type === 'vendor') {
      context.currentVendor = currentItem as Vendor;
    } else {
      context.currentProduct = currentItem as Product;
    }
    
    const contentBasedResults = await computeContentBasedRecommendations(context);
    
    return contentBasedResults.slice(0, limit).map(item => ({
      id: item.id,
      type: item.type,
      title: item.type === 'vendor' ? 
        (item.data as Vendor).businessName : 
        (item.data as Product).name,
      subtitle: item.type === 'vendor' ? 
        (item.data as Vendor).businessDescription || 'Vendor' :
        `BHD ${(item.data as Product).price.toFixed(2)}`,
      score: item.score,
      confidence: Math.min(item.score / 10, 1),
      reasons: item.reasons,
      data: item.data,
      metadata: {
        algorithm: 'content-based',
        trending: false,
        personalized: false,
        cultural: false,
      },
    }));
  }, []);

  const getSeasonalRecommendations = useCallback(async (limit = 10): Promise<SmartRecommendation[]> => {
    const seasonalResults = await computeSeasonalRecommendations({});
    
    return seasonalResults.slice(0, limit).map(item => ({
      id: item.id,
      type: item.type,
      title: item.type === 'vendor' ? 
        (item.data as Vendor).businessName : 
        (item.data as Product).name,
      subtitle: item.type === 'vendor' ? 
        (item.data as Vendor).businessDescription || 'Vendor' :
        `BHD ${(item.data as Product).price.toFixed(2)}`,
      score: item.score,
      confidence: Math.min(item.score / 10, 1),
      reasons: item.reasons,
      data: item.data,
      metadata: {
        algorithm: 'seasonal',
        trending: false,
        personalized: false,
        cultural: false,
      },
    }));
  }, []);

  const getCulturalRecommendations = useCallback(async (
    culture: string,
    limit = 10
  ): Promise<SmartRecommendation[]> => {
    const culturalResults = await computeCulturalRecommendations({});
    
    return culturalResults.slice(0, limit).map(item => ({
      id: item.id,
      type: item.type,
      title: item.type === 'vendor' ? 
        (item.data as Vendor).businessName : 
        (item.data as Product).name,
      subtitle: item.type === 'vendor' ? 
        (item.data as Vendor).businessDescription || 'Vendor' :
        `BHD ${(item.data as Product).price.toFixed(2)}`,
      score: item.score,
      confidence: Math.min(item.score / 10, 1),
      reasons: item.reasons,
      data: item.data,
      metadata: {
        algorithm: 'cultural',
        trending: false,
        personalized: false,
        cultural: true,
      },
    }));
  }, []);

  const refreshRecommendations = useCallback(async (): Promise<void> => {
    await updateTrendingData();
    await cache.invalidateByTag('recommendations');
  }, [cache]);

  const getRecommendationInsights = useCallback(async (): Promise<any> => {
    const insights = await personalizationEngine.getInsights();
    const trendingStats = {
      topTrendingVendors: Array.from(trendingData.current.vendors.entries())
        .sort(([, a], [, b]) => b - a)
        .slice(0, 5),
      topTrendingProducts: Array.from(trendingData.current.products.entries())
        .sort(([, a], [, b]) => b - a)
        .slice(0, 5),
    };
    
    return {
      ...insights,
      trending: trendingStats,
      algorithms: algorithms.current.map(alg => ({
        name: alg.name,
        weight: alg.weight,
        enabled: alg.enabled,
      })),
    };
  }, [personalizationEngine]);

  const contextValue: SmartRecommendationEngineContextType = {
    getRecommendations,
    getPersonalizedVendors,
    getPersonalizedProducts,
    getTrendingRecommendations,
    getSimilarItems,
    getSeasonalRecommendations,
    getCulturalRecommendations,
    refreshRecommendations,
    getRecommendationInsights,
  };

  return (
    <SmartRecommendationEngineContext.Provider value={contextValue}>
      {children}
    </SmartRecommendationEngineContext.Provider>
  );
};
