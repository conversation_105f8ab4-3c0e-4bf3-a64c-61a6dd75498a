import React from 'react';
import { StyleSheet, Dimensions } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { RTLView, RTLText, RTLTouchableOpacity, RTLIcon, RTLScrollView } from '../RTL';
import { useThemedStyles, useVendors, useProducts, useI18n } from '../../hooks';
import { SPACING, FONT_SIZES, FONT_WEIGHTS, BORDER_RADIUS } from '../../constants/theme';
import { PREMIUM_GRADIENTS } from '../../constants/advancedColors';
import { SubtleGlow } from '../MoonlightEffects';
import type { ThemeColors } from '../../contexts/ThemeContext';
import type { Vendor } from '../../contexts/DataContext';

const { width: screenWidth } = Dimensions.get('window');
const COLLECTION_WIDTH = screenWidth * 0.8;

interface CuratedCollectionsProps {
  onVendorPress: (vendorId: string) => void;
  onCollectionPress?: (collectionType: string, vendors: Vendor[]) => void;
}

interface VendorCollection {
  id: string;
  title: string;
  description: string;
  icon: string;
  vendors: Vendor[];
  gradient: readonly string[];
  badge?: string;
  badgeColor?: string;
}

export const CuratedCollections: React.FC<CuratedCollectionsProps> = ({
  onVendorPress,
  onCollectionPress,
}) => {
  const styles = useThemedStyles(createStyles);
  const { getApprovedVendors } = useVendors();
  const { getProductsByVendor } = useProducts();
  const { t } = useI18n();

  const allVendors = getApprovedVendors();

  // Create curated collections based on different criteria
  const collections = React.useMemo((): VendorCollection[] => {
    const newArrivals = allVendors
      .filter(vendor => {
        // Simulate new vendors (joined in last 30 days)
        return Math.random() > 0.7;
      })
      .slice(0, 6);

    const topRated = allVendors
      .filter(vendor => vendor.rating >= 4.5)
      .sort((a, b) => b.rating - a.rating)
      .slice(0, 8);

    const mostProducts = allVendors
      .map(vendor => ({
        ...vendor,
        productCount: getProductsByVendor(vendor.id).filter(p => p.isActive).length,
      }))
      .filter(vendor => vendor.productCount >= 5)
      .sort((a, b) => b.productCount - a.productCount)
      .slice(0, 6);

    const seasonal = allVendors
      .filter(vendor => {
        const products = getProductsByVendor(vendor.id);
        // Simulate seasonal relevance
        return products.some(p => Math.random() > 0.8);
      })
      .slice(0, 5);

    const localFavorites = allVendors
      .filter(vendor => vendor.rating >= 4.0)
      .sort(() => Math.random() - 0.5) // Randomize for local favorites
      .slice(0, 7);

    return [
      {
        id: 'new-arrivals',
        title: t('collections.newArrivals'),
        description: t('collections.newArrivalsDesc'),
        icon: 'sparkles',
        vendors: newArrivals,
        gradient: PREMIUM_GRADIENTS.oceanDepth,
        badge: t('collections.new'),
        badgeColor: '#10B981',
      },
      {
        id: 'top-rated',
        title: t('collections.topRated'),
        description: t('collections.topRatedDesc'),
        icon: 'star',
        vendors: topRated,
        gradient: PREMIUM_GRADIENTS.royalSpotlight,
        badge: t('collections.premium'),
        badgeColor: '#FFD700',
      },
      {
        id: 'most-products',
        title: t('collections.varietyKings'),
        description: t('collections.varietyKingsDesc'),
        icon: 'grid',
        vendors: mostProducts,
        gradient: PREMIUM_GRADIENTS.elegantDepth,
      },
      {
        id: 'seasonal',
        title: t('collections.seasonal'),
        description: t('collections.seasonalDesc'),
        icon: 'leaf',
        vendors: seasonal,
        gradient: PREMIUM_GRADIENTS.mysticAurora,
        badge: t('collections.trending'),
        badgeColor: '#F59E0B',
      },
      {
        id: 'local-favorites',
        title: t('collections.localFavorites'),
        description: t('collections.localFavoritesDesc'),
        icon: 'heart',
        vendors: localFavorites,
        gradient: PREMIUM_GRADIENTS.royalElegance,
      },
    ].filter(collection => collection.vendors.length > 0);
  }, [allVendors, getProductsByVendor, t]);

  const renderVendorPreview = (vendor: Vendor, index: number) => (
    <RTLTouchableOpacity
      key={vendor.id}
      style={[styles.vendorPreview, { marginLeft: index > 0 ? -SPACING.sm : 0 }]}
      onPress={() => onVendorPress(vendor.id)}
    >
      <RTLView style={styles.vendorPreviewLogo}>
        <RTLIcon name="storefront" size={16} color="#3B82F6" />
      </RTLView>
    </RTLTouchableOpacity>
  );

  const renderCollection = (collection: VendorCollection) => (
    <RTLView key={collection.id} style={styles.collectionContainer}>
      <RTLTouchableOpacity
        style={styles.collection}
        onPress={() => onCollectionPress?.(collection.id, collection.vendors)}
        activeOpacity={0.9}
      >
        <SubtleGlow intensity={0.6}>
          <LinearGradient
            colors={collection.gradient}
            style={styles.collectionGradient}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          >
            {/* Badge */}
            {collection.badge && (
              <RTLView style={[styles.collectionBadge, { borderColor: collection.badgeColor }]}>
                <RTLText style={[styles.badgeText, { color: collection.badgeColor }]}>
                  {collection.badge}
                </RTLText>
              </RTLView>
            )}

            {/* Header */}
            <RTLView style={styles.collectionHeader}>
              <RTLView style={styles.collectionIcon}>
                <RTLIcon name={collection.icon} size={24} color="#3B82F6" />
              </RTLView>
              <RTLView style={styles.collectionInfo}>
                <RTLText style={styles.collectionTitle} numberOfLines={1}>
                  {collection.title}
                </RTLText>
                <RTLText style={styles.collectionDescription} numberOfLines={2}>
                  {collection.description}
                </RTLText>
              </RTLView>
            </RTLView>

            {/* Vendor Count */}
            <RTLView style={styles.vendorCount}>
              <RTLIcon name="storefront" size={14} color="rgba(255, 255, 255, 0.8)" />
              <RTLText style={styles.vendorCountText}>
                {t('collections.vendorCount', { count: collection.vendors.length })}
              </RTLText>
            </RTLView>

            {/* Vendor Previews */}
            <RTLView style={styles.vendorPreviews}>
              {collection.vendors.slice(0, 4).map((vendor, index) => 
                renderVendorPreview(vendor, index)
              )}
              {collection.vendors.length > 4 && (
                <RTLView style={[styles.vendorPreview, styles.moreVendors, { marginLeft: -SPACING.sm }]}>
                  <RTLText style={styles.moreVendorsText}>
                    +{collection.vendors.length - 4}
                  </RTLText>
                </RTLView>
              )}
            </RTLView>

            {/* Action */}
            <RTLView style={styles.collectionAction}>
              <RTLView style={styles.exploreButton}>
                <RTLText style={styles.exploreText}>{t('collections.explore')}</RTLText>
                <RTLIcon name="arrow-forward" size={14} color="#3B82F6" />
              </RTLView>
            </RTLView>
          </LinearGradient>
        </SubtleGlow>
      </RTLTouchableOpacity>
    </RTLView>
  );

  if (collections.length === 0) {
    return null;
  }

  return (
    <RTLView style={styles.container}>
      {/* Header */}
      <RTLView style={styles.header}>
        <RTLView style={styles.titleContainer}>
          <RTLIcon name="albums" size={24} color="#3B82F6" />
          <RTLText style={styles.title}>{t('collections.curatedCollections')}</RTLText>
        </RTLView>
        <RTLText style={styles.subtitle}>{t('collections.handpickedForYou')}</RTLText>
      </RTLView>

      {/* Collections */}
      <RTLScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
        enableRTLScrolling={true}
      >
        {collections.map(renderCollection)}
      </RTLScrollView>
    </RTLView>
  );
};

const createStyles = (colors: ThemeColors) => StyleSheet.create({
  container: {
    marginBottom: SPACING.lg,
  },
  header: {
    paddingHorizontal: SPACING.md,
    marginBottom: SPACING.md,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.xs,
  },
  title: {
    fontSize: FONT_SIZES.xl,
    fontWeight: FONT_WEIGHTS.bold,
    color: colors.textPrimary,
    marginLeft: SPACING.sm,
  },
  subtitle: {
    fontSize: FONT_SIZES.sm,
    color: colors.textSecondary,
  },
  scrollContent: {
    paddingHorizontal: SPACING.md,
  },
  collectionContainer: {
    width: COLLECTION_WIDTH,
    marginRight: SPACING.md,
  },
  collection: {
    height: 160,
    borderRadius: BORDER_RADIUS.xl,
    overflow: 'hidden',
  },
  collectionGradient: {
    flex: 1,
    padding: SPACING.md,
    justifyContent: 'space-between',
  },
  collectionBadge: {
    position: 'absolute',
    top: SPACING.sm,
    right: SPACING.sm,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    paddingHorizontal: SPACING.sm,
    paddingVertical: SPACING.xs,
    borderRadius: BORDER_RADIUS.md,
    borderWidth: 1,
  },
  badgeText: {
    fontSize: FONT_SIZES.xs,
    fontWeight: FONT_WEIGHTS.semiBold,
  },
  collectionHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  collectionIcon: {
    width: 40,
    height: 40,
    borderRadius: BORDER_RADIUS.lg,
    backgroundColor: 'rgba(59, 130, 246, 0.1)',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: SPACING.md,
  },
  collectionInfo: {
    flex: 1,
  },
  collectionTitle: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.bold,
    color: '#FFFFFF',
    marginBottom: SPACING.xs,
  },
  collectionDescription: {
    fontSize: FONT_SIZES.sm,
    color: 'rgba(255, 255, 255, 0.8)',
    lineHeight: 18,
  },
  vendorCount: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: SPACING.sm,
  },
  vendorCountText: {
    fontSize: FONT_SIZES.xs,
    color: 'rgba(255, 255, 255, 0.8)',
    marginLeft: SPACING.xs,
    fontWeight: FONT_WEIGHTS.medium,
  },
  vendorPreviews: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  vendorPreview: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: 'rgba(59, 130, 246, 0.2)',
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.3)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  vendorPreviewLogo: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  moreVendors: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  moreVendorsText: {
    fontSize: FONT_SIZES.xs,
    color: '#FFFFFF',
    fontWeight: FONT_WEIGHTS.semiBold,
  },
  collectionAction: {
    alignItems: 'flex-end',
  },
  exploreButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(59, 130, 246, 0.2)',
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    borderRadius: BORDER_RADIUS.md,
    borderWidth: 1,
    borderColor: '#3B82F6',
  },
  exploreText: {
    fontSize: FONT_SIZES.xs,
    fontWeight: FONT_WEIGHTS.semiBold,
    color: '#3B82F6',
    marginRight: SPACING.xs,
  },
});
