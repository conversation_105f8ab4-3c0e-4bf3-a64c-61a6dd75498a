import { EventEmitter } from '../utils/EventEmitter';
import type Product  from '../contexts/DataContext';
import type User  from '../contexts/AuthContext';
import { storage } from '../utils/storage';

export interface Review {
  id: string;
  productId: string;
  userId: string;
  userName: string;
  userAvatar?: string;
  rating: number; // 1-5 stars
  title: string;
  content: string;
  images?: string[];
  verified: boolean; // Verified purchase
  helpful: number; // Helpful votes
  notHelpful: number; // Not helpful votes
  createdAt: string;
  updatedAt?: string;
  response?: {
    vendorId: string;
    vendorName: string;
    content: string;
    createdAt: string;
  };
}

export interface WishlistItem {
  id: string;
  userId: string;
  productId: string;
  addedAt: string;
  notes?: string;
  priceAlert?: {
    enabled: boolean;
    targetPrice: number;
  };
}

export interface SocialShare {
  id: string;
  userId: string;
  type: 'product' | 'review' | 'wishlist' | 'order';
  contentId: string;
  platform: 'facebook' | 'twitter' | 'instagram' | 'whatsapp' | 'email' | 'copy_link';
  sharedAt: string;
  metadata?: Record<string, any>;
}

export interface UserFollow {
  id: string;
  followerId: string;
  followingId: string;
  type: 'user' | 'vendor';
  createdAt: string;
}

export interface ProductRating {
  productId: string;
  averageRating: number;
  totalReviews: number;
  ratingDistribution: {
    1: number;
    2: number;
    3: number;
    4: number;
    5: number;
  };
}

class SocialService extends EventEmitter {
  private static instance: SocialService;
  private reviews: Review[] = [];
  private wishlists: Map<string, WishlistItem[]> = new Map();
  private shares: SocialShare[] = [];
  private follows: UserFollow[] = [];
  private productRatings: Map<string, ProductRating> = new Map();

  private constructor() {
    super();
    this.loadStoredData();
  }

  public static getInstance(): SocialService {
    if (!SocialService.instance) {
      SocialService.instance = new SocialService();
    }
    return SocialService.instance;
  }

  // Reviews Management
  public async addReview(review: Omit<Review, 'id' | 'createdAt' | 'helpful' | 'notHelpful'>): Promise<string> {
    const newReview: Review = {
      ...review,
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      createdAt: new Date().toISOString(),
      helpful: 0,
      notHelpful: 0,
    };

    this.reviews.push(newReview);
    this.updateProductRating(review.productId);
    await this.saveData();
    
    this.emit('reviewAdded', newReview);
    return newReview.id;
  }

  public async updateReview(reviewId: string, updates: Partial<Review>): Promise<void> {
    const reviewIndex = this.reviews.findIndex(r => r.id === reviewId);
    if (reviewIndex !== -1) {
      this.reviews[reviewIndex] = {
        ...this.reviews[reviewIndex],
        ...updates,
        updatedAt: new Date().toISOString(),
      };
      
      this.updateProductRating(this.reviews[reviewIndex].productId);
      await this.saveData();
      this.emit('reviewUpdated', this.reviews[reviewIndex]);
    }
  }

  public async deleteReview(reviewId: string, userId: string): Promise<void> {
    const reviewIndex = this.reviews.findIndex(r => r.id === reviewId && r.userId === userId);
    if (reviewIndex !== -1) {
      const review = this.reviews[reviewIndex];
      this.reviews.splice(reviewIndex, 1);
      this.updateProductRating(review.productId);
      await this.saveData();
      this.emit('reviewDeleted', review);
    }
  }

  public getProductReviews(
    productId: string,
    options: {
      sortBy?: 'newest' | 'oldest' | 'rating_high' | 'rating_low' | 'helpful';
      filterBy?: {
        rating?: number;
        verified?: boolean;
      };
      limit?: number;
      offset?: number;
    } = {}
  ): Review[] {
    let productReviews = this.reviews.filter(r => r.productId === productId);

    // Apply filters
    if (options.filterBy?.rating) {
      productReviews = productReviews.filter(r => r.rating === options.filterBy!.rating);
    }
    if (options.filterBy?.verified !== undefined) {
      productReviews = productReviews.filter(r => r.verified === options.filterBy!.verified);
    }

    // Apply sorting
    switch (options.sortBy) {
      case 'oldest':
        productReviews.sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());
        break;
      case 'rating_high':
        productReviews.sort((a, b) => b.rating - a.rating);
        break;
      case 'rating_low':
        productReviews.sort((a, b) => a.rating - b.rating);
        break;
      case 'helpful':
        productReviews.sort((a, b) => b.helpful - a.helpful);
        break;
      case 'newest':
      default:
        productReviews.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
        break;
    }

    // Apply pagination
    const offset = options.offset || 0;
    const limit = options.limit || productReviews.length;
    
    return productReviews.slice(offset, offset + limit);
  }

  public async voteReviewHelpful(reviewId: string, userId: string, helpful: boolean): Promise<void> {
    const review = this.reviews.find(r => r.id === reviewId);
    if (review) {
      if (helpful) {
        review.helpful++;
      } else {
        review.notHelpful++;
      }
      await this.saveData();
      this.emit('reviewVoted', { reviewId, userId, helpful });
    }
  }

  public async addVendorResponse(reviewId: string, vendorId: string, vendorName: string, content: string): Promise<void> {
    const review = this.reviews.find(r => r.id === reviewId);
    if (review) {
      review.response = {
        vendorId,
        vendorName,
        content,
        createdAt: new Date().toISOString(),
      };
      await this.saveData();
      this.emit('vendorResponseAdded', review);
    }
  }

  // Wishlist Management
  public async addToWishlist(userId: string, productId: string, notes?: string): Promise<string> {
    const wishlistItem: WishlistItem = {
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      userId,
      productId,
      addedAt: new Date().toISOString(),
      notes,
    };

    const userWishlist = this.wishlists.get(userId) || [];
    
    // Check if item already exists
    if (userWishlist.some(item => item.productId === productId)) {
      throw new Error('Product already in wishlist');
    }

    userWishlist.push(wishlistItem);
    this.wishlists.set(userId, userWishlist);
    
    await this.saveData();
    this.emit('wishlistItemAdded', wishlistItem);
    return wishlistItem.id;
  }

  public async removeFromWishlist(userId: string, productId: string): Promise<void> {
    const userWishlist = this.wishlists.get(userId) || [];
    const itemIndex = userWishlist.findIndex(item => item.productId === productId);
    
    if (itemIndex !== -1) {
      const removedItem = userWishlist[itemIndex];
      userWishlist.splice(itemIndex, 1);
      this.wishlists.set(userId, userWishlist);
      
      await this.saveData();
      this.emit('wishlistItemRemoved', removedItem);
    }
  }

  public getUserWishlist(userId: string): WishlistItem[] {
    return this.wishlists.get(userId) || [];
  }

  public async setPriceAlert(userId: string, productId: string, targetPrice: number): Promise<void> {
    const userWishlist = this.wishlists.get(userId) || [];
    const item = userWishlist.find(item => item.productId === productId);
    
    if (item) {
      item.priceAlert = {
        enabled: true,
        targetPrice,
      };
      await this.saveData();
      this.emit('priceAlertSet', { userId, productId, targetPrice });
    }
  }

  // Social Sharing
  public async shareContent(
    userId: string,
    type: SocialShare['type'],
    contentId: string,
    platform: SocialShare['platform'],
    metadata?: Record<string, any>
  ): Promise<string> {
    const share: SocialShare = {
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      userId,
      type,
      contentId,
      platform,
      sharedAt: new Date().toISOString(),
      metadata,
    };

    this.shares.push(share);
    await this.saveData();
    
    this.emit('contentShared', share);
    return share.id;
  }

  public generateShareContent(type: SocialShare['type'], contentId: string, product?: Product): {
    title: string;
    description: string;
    url: string;
    imageUrl?: string;
  } {
    switch (type) {
      case 'product':
        return {
          title: product ? `Check out ${product.name}` : 'Check out this product',
          description: product ? product.description : 'Amazing product on VendorHub',
          url: `https://vendorhub.com/products/${contentId}`,
          imageUrl: product?.images?.[0],
        };
      case 'review':
        return {
          title: 'Product Review on VendorHub',
          description: 'Read my review of this amazing product',
          url: `https://vendorhub.com/reviews/${contentId}`,
        };
      case 'wishlist':
        return {
          title: 'My Wishlist on VendorHub',
          description: 'Check out my favorite products',
          url: `https://vendorhub.com/wishlists/${contentId}`,
        };
      default:
        return {
          title: 'VendorHub',
          description: 'Discover amazing products on VendorHub',
          url: 'https://vendorhub.com',
        };
    }
  }

  // Following System
  public async followUser(followerId: string, followingId: string, type: 'user' | 'vendor'): Promise<string> {
    // Check if already following
    const existingFollow = this.follows.find(
      f => f.followerId === followerId && f.followingId === followingId && f.type === type
    );
    
    if (existingFollow) {
      throw new Error('Already following this user/vendor');
    }

    const follow: UserFollow = {
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      followerId,
      followingId,
      type,
      createdAt: new Date().toISOString(),
    };

    this.follows.push(follow);
    await this.saveData();
    
    this.emit('userFollowed', follow);
    return follow.id;
  }

  public async unfollowUser(followerId: string, followingId: string, type: 'user' | 'vendor'): Promise<void> {
    const followIndex = this.follows.findIndex(
      f => f.followerId === followerId && f.followingId === followingId && f.type === type
    );
    
    if (followIndex !== -1) {
      const follow = this.follows[followIndex];
      this.follows.splice(followIndex, 1);
      await this.saveData();
      this.emit('userUnfollowed', follow);
    }
  }

  public getFollowing(userId: string, type?: 'user' | 'vendor'): UserFollow[] {
    return this.follows.filter(f => 
      f.followerId === userId && (type ? f.type === type : true)
    );
  }

  public getFollowers(userId: string, type?: 'user' | 'vendor'): UserFollow[] {
    return this.follows.filter(f => 
      f.followingId === userId && (type ? f.type === type : true)
    );
  }

  // Analytics
  public getProductRating(productId: string): ProductRating | undefined {
    return this.productRatings.get(productId);
  }

  public getUserReviewStats(userId: string): {
    totalReviews: number;
    averageRating: number;
    helpfulVotes: number;
    verifiedReviews: number;
  } {
    const userReviews = this.reviews.filter(r => r.userId === userId);
    const totalReviews = userReviews.length;
    const averageRating = totalReviews > 0 
      ? userReviews.reduce((sum, r) => sum + r.rating, 0) / totalReviews 
      : 0;
    const helpfulVotes = userReviews.reduce((sum, r) => sum + r.helpful, 0);
    const verifiedReviews = userReviews.filter(r => r.verified).length;

    return {
      totalReviews,
      averageRating,
      helpfulVotes,
      verifiedReviews,
    };
  }

  // Private Methods
  private updateProductRating(productId: string): void {
    const productReviews = this.reviews.filter(r => r.productId === productId);
    
    if (productReviews.length === 0) {
      this.productRatings.delete(productId);
      return;
    }

    const totalReviews = productReviews.length;
    const averageRating = productReviews.reduce((sum, r) => sum + r.rating, 0) / totalReviews;
    
    const ratingDistribution = { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 };
    productReviews.forEach(review => {
      ratingDistribution[review.rating as keyof typeof ratingDistribution]++;
    });

    this.productRatings.set(productId, {
      productId,
      averageRating,
      totalReviews,
      ratingDistribution,
    });
  }

  // Storage
  private async loadStoredData(): Promise<void> {
    try {
      const [reviewsData, wishlistsData, sharesData, followsData, ratingsData] = await Promise.all([
        storage.getItem('reviews'),
        storage.getItem('wishlists'),
        storage.getItem('socialShares'),
        storage.getItem('userFollows'),
        storage.getItem('productRatings'),
      ]);

      if (reviewsData) {
        this.reviews = JSON.parse(reviewsData);
      }

      if (wishlistsData) {
        const wishlistsArray = JSON.parse(wishlistsData);
        this.wishlists = new Map(wishlistsArray);
      }

      if (sharesData) {
        this.shares = JSON.parse(sharesData);
      }

      if (followsData) {
        this.follows = JSON.parse(followsData);
      }

      if (ratingsData) {
        const ratingsArray = JSON.parse(ratingsData);
        this.productRatings = new Map(ratingsArray);
      }
    } catch (error) {
      console.error('Error loading social data:', error);
    }
  }

  private async saveData(): Promise<void> {
    try {
      const wishlistsArray = Array.from(this.wishlists.entries());
      const ratingsArray = Array.from(this.productRatings.entries());
      
      await Promise.all([
        storage.setItem('reviews', JSON.stringify(this.reviews)),
        storage.setItem('wishlists', JSON.stringify(wishlistsArray)),
        storage.setItem('socialShares', JSON.stringify(this.shares)),
        storage.setItem('userFollows', JSON.stringify(this.follows)),
        storage.setItem('productRatings', JSON.stringify(ratingsArray)),
      ]);
    } catch (error) {
      console.error('Error saving social data:', error);
    }
  }

  // Public API
  public getAllReviews(): Review[] {
    return [...this.reviews];
  }

  public getShareAnalytics(): {
    totalShares: number;
    byPlatform: Record<string, number>;
    byType: Record<string, number>;
    topSharedContent: Array<{ contentId: string; shares: number; type: string }>;
  } {
    const totalShares = this.shares.length;
    const byPlatform: Record<string, number> = {};
    const byType: Record<string, number> = {};
    const contentShares = new Map<string, { count: number; type: string }>();

    this.shares.forEach(share => {
      byPlatform[share.platform] = (byPlatform[share.platform] || 0) + 1;
      byType[share.type] = (byType[share.type] || 0) + 1;
      
      const current = contentShares.get(share.contentId) || { count: 0, type: share.type };
      contentShares.set(share.contentId, { count: current.count + 1, type: share.type });
    });

    const topSharedContent = Array.from(contentShares.entries())
      .map(([contentId, data]) => ({ contentId, shares: data.count, type: data.type }))
      .sort((a, b) => b.shares - a.shares)
      .slice(0, 10);

    return {
      totalShares,
      byPlatform,
      byType,
      topSharedContent,
    };
  }

  public clearUserData(userId: string): void {
    this.reviews = this.reviews.filter(r => r.userId !== userId);
    this.wishlists.delete(userId);
    this.shares = this.shares.filter(s => s.userId !== userId);
    this.follows = this.follows.filter(f => f.followerId !== userId && f.followingId !== userId);
    this.saveData();
  }
}

export default SocialService.getInstance();
