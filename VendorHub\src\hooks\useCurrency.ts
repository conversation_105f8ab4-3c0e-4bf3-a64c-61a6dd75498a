import { useCallback } from 'react';
import { useI18n } from './useI18n';
import { formatCurrency as utilFormatCurrency } from '../utils';

export interface CurrencyFormattingOptions {
  showSymbol?: boolean;
  locale?: string;
}

export interface CurrencyHook {
  formatCurrency: (amount: number, options?: CurrencyFormattingOptions) => string;
  formatPrice: (amount: number) => string;
  formatPriceWithoutSymbol: (amount: number) => string;
  isRTL: boolean;
  currencySymbol: string;
}

/**
 * Hook for RTL-aware currency formatting
 * Automatically handles RTL/LTR currency display based on current language
 */
export const useCurrency = (): CurrencyHook => {
  const { isRTL, currentLanguage } = useI18n();

  const formatCurrency = useCallback((
    amount: number, 
    options?: CurrencyFormattingOptions
  ): string => {
    const { showSymbol = true, locale } = options || {};
    
    return utilFormatCurrency(amount, {
      isRTL,
      locale: locale || (isRTL ? 'ar-BH' : 'en-BH'),
      showSymbol,
    });
  }, [isRTL]);

  const formatPrice = useCallback((amount: number): string => {
    return formatCurrency(amount, { showSymbol: true });
  }, [formatCurrency]);

  const formatPriceWithoutSymbol = useCallback((amount: number): string => {
    return formatCurrency(amount, { showSymbol: false });
  }, [formatCurrency]);

  return {
    formatCurrency,
    formatPrice,
    formatPriceWithoutSymbol,
    isRTL,
    currencySymbol: 'BD', // BHD symbol
  };
};
