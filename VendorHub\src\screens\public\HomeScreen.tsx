import React, { useMemo, useCallback } from 'react';
import { StyleSheet, RefreshControl } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useThemedStyles, useAuth, useProducts, useI18n } from '../../hooks';
import { RTLView, RTLText, RTLScrollView, RTLIcon, RTLSafeAreaView, RTLTouchableOpacity } from '../../components/RTL';
import { Button, Card, EmptyState, HomeBackground, LanguageSelector, Logo, RecommendationsSection, StatisticsCounter } from '../../components';
import {
  SPACING,
  FONT_SIZES,
  FONT_WEIGHTS,
  GRADIENTS,
  ICON_SIZES } from '../../constants/theme';
import { APP_NAME } from '../../constants';
import { formatCurrency } from '../../utils';
import type ThemeColors  from '../../contexts/ThemeContext';

interface HomeScreenProps {
  navigation: any;
}

export const HomeScreen: React.FC<HomeScreenProps> = ({ navigation }) => {
  const styles = useThemedStyles(createStyles);
  const { user, logout } = useAuth();
  const { getFeaturedProducts, getBestSellingProducts, getProductsOnSale } = useProducts();
  const { t } = useI18n();
  const [refreshing, setRefreshing] = React.useState(false);

  // Memoize product lists for better performance
  const featuredProducts = useMemo(() => getFeaturedProducts(6), []);
  const bestSellingProducts = useMemo(() => getBestSellingProducts(6), []);
  const saleProducts = useMemo(() => getProductsOnSale(), []);

  const onRefresh = React.useCallback(async () => {
    setRefreshing(true);
    // Simulate refresh delay
    setTimeout(() => setRefreshing(false), 1000);
  }, []);

  const handleLogout = async () => {
    await logout();
  };

  const handleSearchPress = () => {
    navigation.navigate('Search');
  };

  const handleCategoryPress = (category: string) => {
    navigation.navigate('Search', { category });
  };

  // Memoize categories for better performance
  const categories = useMemo(() => [
    { name: t('home.electronics'), key: 'Electronics', icon: 'phone-portrait-outline', color: '#1E3A8A' },
    { name: t('home.fashion'), key: 'Fashion', icon: 'shirt-outline', color: '#3B82F6' },
    { name: t('home.homeGarden'), key: 'Home & Garden', icon: 'home-outline', color: '#059669' },
    { name: t('home.sports'), key: 'Sports', icon: 'fitness-outline', color: '#D97706' },
    { name: t('home.books'), key: 'Books', icon: 'book-outline', color: '#0EA5E9' },
    { name: t('home.health'), key: 'Health', icon: 'heart-outline', color: '#1D4ED8' },
  ], [t]);

  // Memoize product card rendering for better performance
  const renderProductCard = useCallback((product: any) => (
    <Card
      key={product.id}
      style={styles.productCard}
      variant="elevated"
      onPress={() => navigation.navigate('ProductDetails', { productId: product.id })}
    >
      <RTLView style={styles.productImage}>
        <RTLIcon name="image-outline" size={40} color="#CCCCCC" />
      </RTLView>
      <RTLText style={styles.productName} numberOfLines={2}>
        {product.name}
      </RTLText>
      <RTLView style={styles.productPricing}>
        <RTLText style={styles.productPrice}>
          {formatCurrency(product.price)}
        </RTLText>
        {product.originalPrice && product.originalPrice > product.price && (
          <RTLText style={styles.originalPrice}>
            {formatCurrency(product.originalPrice)}
          </RTLText>
        )}
      </RTLView>
      <RTLView style={styles.productRating}>
        <RTLIcon name="star" size={14} color="#FFD700" />
        <RTLText style={styles.ratingText}>{product.rating.toFixed(1)}</RTLText>
        <RTLText style={styles.reviewCount}>({product.reviewCount})</RTLText>
      </RTLView>
    </Card>
  ), [navigation]);

  return (
    <HomeBackground>
      <RTLSafeAreaView style={styles.safeArea}>
        <RTLScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
          showsVerticalScrollIndicator={false}
        >


        {/* Header */}
        <LinearGradient colors={GRADIENTS.primary} style={styles.header}>
          <RTLView style={styles.headerContent}>
            <RTLView style={styles.headerLeft}>
              <RTLText style={styles.welcomeText}>{t('home.welcomeTo')}</RTLText>
              <Logo size="medium" color="white" />
            </RTLView>
            <RTLView style={styles.headerRight}>
              <LanguageSelector compact={true} style={styles.languageSelector} />
              <RTLTouchableOpacity style={styles.headerButton} onPress={handleSearchPress}>
                <RTLIcon name="search-outline" size={24} color="#FFFFFF" />
              </RTLTouchableOpacity>
              <RTLTouchableOpacity onPress={handleLogout} style={styles.headerButton}>
                <RTLIcon name="log-out-outline" size={24} color="#FFFFFF" />
              </RTLTouchableOpacity>
            </RTLView>
          </RTLView>
        </LinearGradient>

        {/* Categories */}
        <RTLView style={styles.section}>
          <RTLText style={styles.sectionTitle}>{t('home.categories')}</RTLText>
          <RTLScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.categoriesContainer}
            enableRTLScrolling={true}
          >
            {categories.map((category, index) => (
              <RTLView key={index} style={styles.categoryCard}>
                <RTLTouchableOpacity
                  style={styles.categoryTouchable}
                  onPress={() => handleCategoryPress(category.key)}
                >
                  <RTLView style={[styles.categoryIcon, { backgroundColor: category.color }]}>
                    <RTLIcon name={category.icon as any} size={24} color="#FFFFFF" />
                  </RTLView>
                  <RTLText style={styles.categoryName}>{category.name}</RTLText>
                </RTLTouchableOpacity>
              </RTLView>
            ))}
          </RTLScrollView>
        </RTLView>

        {/* Featured Products */}
        <RTLView style={styles.section}>
          <RTLView style={styles.sectionHeader}>
            <RTLText style={styles.sectionTitle}>{t('home.featuredProducts')}</RTLText>
            <RTLTouchableOpacity>
              <RTLText style={styles.seeAllText}>{t('home.viewAll')}</RTLText>
            </RTLTouchableOpacity>
          </RTLView>

          {featuredProducts.length > 0 ? (
            <RTLScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.productsContainer}
              enableRTLScrolling={true}
            >
              {featuredProducts.map(renderProductCard)}
            </RTLScrollView>
          ) : (
            <EmptyState
              icon="cube-outline"
              title={t('home.noProductsFound')}
              description={t('home.checkBackLaterFeatured')}
              size="small"
            />
          )}
        </RTLView>

        {/* Best Selling */}
        <RTLView style={styles.section}>
          <RTLView style={styles.sectionHeader}>
            <RTLText style={styles.sectionTitle}>{t('home.bestSelling')}</RTLText>
            <RTLTouchableOpacity>
              <RTLText style={styles.seeAllText}>{t('home.viewAll')}</RTLText>
            </RTLTouchableOpacity>
          </RTLView>

          {bestSellingProducts.length > 0 ? (
            <RTLScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.productsContainer}
              enableRTLScrolling={true}
            >
              {bestSellingProducts.map(renderProductCard)}
            </RTLScrollView>
          ) : (
            <EmptyState
              icon="trending-up-outline"
              title={t('home.noProductsFound')}
              description={t('home.checkBackLaterTrending')}
              size="small"
            />
          )}
        </RTLView>

        {/* On Sale */}
        {saleProducts.length > 0 && (
          <RTLView style={styles.section}>
            <RTLView style={styles.sectionHeader}>
              <RTLText style={styles.sectionTitle}>{t('home.onSale')}</RTLText>
              <RTLTouchableOpacity>
                <RTLText style={styles.seeAllText}>{t('home.viewAll')}</RTLText>
              </RTLTouchableOpacity>
            </RTLView>

            <RTLScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.productsContainer}
              enableRTLScrolling={true}
            >
              {saleProducts.slice(0, 6).map(renderProductCard)}
            </RTLScrollView>
          </RTLView>
        )}

        {/* Recommendations */}
        <RecommendationsSection
          title={t('home.recommendedForYou')}
          limit={8}
          showReason={true}
        />

        {/* Quick Actions */}
        <RTLView style={styles.section}>
          <RTLText style={styles.sectionTitle}>{t('home.quickActions')}</RTLText>
          <RTLView style={styles.quickActions}>
            <Button
              title={t('home.browseAllShops')}
              onPress={() => navigation.navigate('Shops')}
              variant="outline"
              style={styles.quickActionButton}
              leftIcon={<RTLIcon name="storefront-outline" size={20} color="#667eea" />}
            />
            <Button
              title={t('home.viewCart')}
              onPress={() => navigation.navigate('Cart')}
              variant="outline"
              style={styles.quickActionButton}
              leftIcon={<RTLIcon name="bag-outline" size={20} color="#667eea" />}
            />
          </RTLView>
        </RTLView>
        </RTLScrollView>
      </RTLSafeAreaView>
    </HomeBackground>
  );
};

const createStyles = (colors: ThemeColors) =>
  StyleSheet.create({
    container: {
      flex: 1,
    },
    safeArea: {
      flex: 1,
    },
    scrollView: {
      flex: 1,
    },
    scrollContent: {
      paddingBottom: SPACING.xl,
    },
    header: {
      paddingHorizontal: SPACING.lg,
      paddingVertical: SPACING.lg,
      marginBottom: SPACING.lg,
    },
    headerContent: {
      flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    headerLeft: {
      flex: 1,
    },
    headerRight: {
      flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
      gap: SPACING.sm,
    },
    headerButton: {
      padding: SPACING.sm,
    },
    languageSelector: {
      backgroundColor: 'rgba(255, 255, 255, 0.2)',
      borderRadius: 20,
      paddingHorizontal: SPACING.sm,
      paddingVertical: SPACING.xs,
      marginHorizontal: SPACING.xs,
    },
    welcomeText: {
      fontSize: FONT_SIZES.sm,
      color: '#FFFFFF',
      opacity: 0.8,
    },
    logoText: {
      fontSize: FONT_SIZES.xl,
      fontWeight: FONT_WEIGHTS.bold,
      color: '#FFFFFF',
      marginTop: SPACING.xs,
    },
    section: {
      paddingHorizontal: SPACING.lg,
      marginBottom: SPACING.lg,
    },
    sectionHeader: {
      flexDirection: 'row', // This will be flipped to 'row-reverse' in RTL by RTLView
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: SPACING.md,
      // Ensure consistent spacing for RTL
      paddingHorizontal: SPACING.xs,
    },
    sectionTitle: {
      fontSize: FONT_SIZES.lg,
      fontWeight: FONT_WEIGHTS.semibold,
      color: colors.textPrimary,
      // Ensure proper text alignment in RTL
      textAlign: 'left', // RTL: Will be automatically flipped by RTLText component
      flex: 1, // Take available space
    },
    seeAllText: {
      fontSize: FONT_SIZES.sm,
      color: colors.primary,
      fontWeight: FONT_WEIGHTS.medium,
      // Ensure proper text alignment in RTL
      textAlign: 'left', // RTL: Will be automatically flipped by RTLText component
      marginHorizontal: SPACING.sm, // Add consistent margin
    },
    categoriesContainer: {
      paddingHorizontal: SPACING.lg,
      // Ensure consistent padding for RTL
      paddingStart: SPACING.lg,
      paddingEnd: SPACING.lg,
      // Ensure proper flex direction for horizontal scrolling
      flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
      // Add minimum height to prevent layout issues
      minHeight: 100,
    },
    categoryCard: {
      alignItems: 'center',
      marginHorizontal: SPACING.sm, // Increased for better spacing in RTL
      width: 80,
    },
    categoryTouchable: {
      alignItems: 'center',
      width: '100%',
      // Ensure consistent padding for RTL
      paddingHorizontal: SPACING.xs,
    },
    categoryIcon: {
      width: 60,
      height: 60,
      borderRadius: 30,
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: SPACING.sm,
      // Ensure icon is perfectly centered for RTL
      overflow: 'hidden',
    },
    categoryName: {
      fontSize: FONT_SIZES.xs,
      color: colors.textSecondary,
      textAlign: 'center', // Keep centered in both LTR and RTL
      fontWeight: FONT_WEIGHTS.medium,
      // Ensure text fits within container
      width: '100%',
    },
    productsContainer: {
      paddingHorizontal: SPACING.lg,
      // Ensure proper flex direction for horizontal scrolling
      flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
      // Add minimum height to prevent layout issues
      minHeight: 220,
    },
    productCard: {
      width: 160,
      marginHorizontal: SPACING.md,
    },
    productImage: {
      height: 120,
      backgroundColor: colors.backgroundSecondary,
      borderRadius: 8,
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: SPACING.sm,
      // Ensure equal margins for RTL compatibility
      marginHorizontal: SPACING.xs,
      overflow: 'hidden', // Ensure content stays within bounds
    },
    productName: {
      fontSize: FONT_SIZES.sm,
      fontWeight: FONT_WEIGHTS.medium,
      color: colors.textPrimary,
      marginBottom: SPACING.xs,
      minHeight: 32,
    },
    productPricing: {
      flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
      alignItems: 'center',
      marginBottom: SPACING.xs,
      justifyContent: 'flex-start', // This will be flipped in RTL
    },
    productPrice: {
      fontSize: FONT_SIZES.md,
      fontWeight: FONT_WEIGHTS.semibold,
      color: colors.textPrimary,
      marginHorizontal: SPACING.xs,
      textAlign: 'left', // RTL: Will be automatically flipped by RTLText component
    },
    originalPrice: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
      textDecorationLine: 'line-through',
      textAlign: 'left', // RTL: Will be automatically flipped by RTLText component
    },
    productRating: {
      flexDirection: 'row', // This will be flipped to 'row-reverse' in RTL by RTLView
      alignItems: 'center',
      justifyContent: 'flex-start', // This will be flipped in RTL
    },
    ratingText: {
      fontSize: FONT_SIZES.xs,
      color: colors.textSecondary,
      marginHorizontal: SPACING.xs,
    },
    reviewCount: {
      fontSize: FONT_SIZES.xs,
      color: colors.textSecondary,
    },
    quickActions: {
      gap: SPACING.md,
    },
    quickActionButton: {
      justifyContent: 'flex-start',
      paddingHorizontal: SPACING.lg,
    },
  });
