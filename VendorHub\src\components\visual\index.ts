// Visual Enhancement Components
export { ParallaxScrollView } from './ParallaxScrollView';
export { GlassmorphismCard } from './GlassmorphismCard';
export { 
  AnimatedPressable, 
  FadeInView, 
  PulseView, 
  ShimmerView, 
  BouncyView 
} from './MicroAnimations';
export { 
  DynamicThemeProvider, 
  useDynamicTheme, 
  useVendorTheme, 
  useSeasonalTheme 
} from './DynamicThemeProvider';

// Re-export types
export type { ThemeColors } from '../../contexts/ThemeContext';
