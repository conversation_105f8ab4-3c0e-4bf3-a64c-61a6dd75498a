import React, { useState, useRef, useCallback, useEffect } from 'react';
import { StyleSheet, Animated, Dimensions, Image as RNImage } from 'react-native';
import { RTLView, RTLIcon } from '../RTL';
import { useThemedStyles, useI18n } from '../../hooks';
import { useIntelligentCache } from './IntelligentCache';
import { usePerformanceMonitor } from './PerformanceMonitor';
import { SPACING, BORDER_RADIUS } from '../../constants/theme';
import { ShimmerView } from '../visual/MicroAnimations';
import type { ThemeColors } from '../../contexts/ThemeContext';

const { width: screenWidth } = Dimensions.get('window');

interface ImageSource {
  uri: string;
  width?: number;
  height?: number;
  quality?: 'low' | 'medium' | 'high';
}

interface OptimizedImageLoaderProps {
  source: ImageSource | ImageSource[];
  style?: any;
  resizeMode?: 'cover' | 'contain' | 'stretch' | 'repeat' | 'center';
  placeholder?: React.ReactNode;
  fallback?: React.ReactNode;
  onLoad?: () => void;
  onError?: (error: any) => void;
  onProgress?: (progress: number) => void;
  priority?: 'low' | 'medium' | 'high' | 'critical';
  enableCache?: boolean;
  enableProgressiveLoading?: boolean;
  enableLazyLoading?: boolean;
  lazyLoadOffset?: number;
  enableRetry?: boolean;
  maxRetries?: number;
  retryDelay?: number;
  enableWebP?: boolean;
  enableBlur?: boolean;
  blurRadius?: number;
  borderRadius?: number;
  enableFadeIn?: boolean;
  fadeInDuration?: number;
  enablePreload?: boolean;
  preloadDelay?: number;
}

interface LoadingState {
  isLoading: boolean;
  isLoaded: boolean;
  hasError: boolean;
  progress: number;
  retryCount: number;
  currentSource: ImageSource | null;
}

export const OptimizedImageLoader: React.FC<OptimizedImageLoaderProps> = ({
  source,
  style,
  resizeMode = 'cover',
  placeholder,
  fallback,
  onLoad,
  onError,
  onProgress,
  priority = 'medium',
  enableCache = true,
  enableProgressiveLoading = true,
  enableLazyLoading = false,
  lazyLoadOffset = 100,
  enableRetry = true,
  maxRetries = 3,
  retryDelay = 1000,
  enableWebP = true,
  enableBlur = false,
  blurRadius = 5,
  borderRadius = 0,
  enableFadeIn = true,
  fadeInDuration = 300,
  enablePreload = false,
  preloadDelay = 100,
}) => {
  const styles = useThemedStyles(createStyles);
  const { t } = useI18n();
  const cache = useIntelligentCache();
  const performanceMonitor = usePerformanceMonitor();

  const [loadingState, setLoadingState] = useState<LoadingState>({
    isLoading: false,
    isLoaded: false,
    hasError: false,
    progress: 0,
    retryCount: 0,
    currentSource: null,
  });

  const [isInView, setIsInView] = useState(!enableLazyLoading);
  
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const blurAnim = useRef(new Animated.Value(enableBlur ? blurRadius : 0)).current;
  const progressAnim = useRef(new Animated.Value(0)).current;
  
  const imageRef = useRef<any>(null);
  const retryTimer = useRef<NodeJS.Timeout>();
  const preloadTimer = useRef<NodeJS.Timeout>();
  const loadStartTime = useRef<number>(0);

  // Get optimal image source
  const getOptimalSource = useCallback((): ImageSource => {
    const sources = Array.isArray(source) ? source : [source];
    
    // Sort by quality and select best match for screen density
    const sortedSources = sources.sort((a, b) => {
      const qualityOrder = { low: 1, medium: 2, high: 3 };
      return qualityOrder[b.quality || 'medium'] - qualityOrder[a.quality || 'medium'];
    });

    // Select source based on screen density and size
    const pixelRatio = RNImage.getSize ? 2 : 1; // Simplified
    const targetWidth = (style?.width || screenWidth) * pixelRatio;
    
    for (const src of sortedSources) {
      if (!src.width || src.width >= targetWidth) {
        return src;
      }
    }
    
    return sortedSources[0];
  }, [source, style]);

  // Generate cache key
  const getCacheKey = useCallback((imageSource: ImageSource): string => {
    return `image:${imageSource.uri}:${imageSource.width || 0}:${imageSource.height || 0}`;
  }, []);

  // Optimize image URL for WebP support
  const optimizeImageUrl = useCallback((uri: string): string => {
    if (!enableWebP) return uri;
    
    // Add WebP format parameter if supported
    // This would depend on your image service
    if (uri.includes('?')) {
      return `${uri}&format=webp`;
    } else {
      return `${uri}?format=webp`;
    }
  }, [enableWebP]);

  // Load image with caching
  const loadImage = useCallback(async (imageSource: ImageSource) => {
    const cacheKey = getCacheKey(imageSource);
    loadStartTime.current = Date.now();
    
    setLoadingState(prev => ({
      ...prev,
      isLoading: true,
      hasError: false,
      currentSource: imageSource,
    }));

    try {
      // Check cache first
      if (enableCache) {
        const cachedImage = await cache.get(cacheKey);
        if (cachedImage) {
          setLoadingState(prev => ({
            ...prev,
            isLoading: false,
            isLoaded: true,
            progress: 100,
          }));
          
          if (enableFadeIn) {
            Animated.timing(fadeAnim, {
              toValue: 1,
              duration: fadeInDuration,
              useNativeDriver: true,
            }).start();
          }
          
          onLoad?.();
          return;
        }
      }

      // Optimize URL
      const optimizedUri = optimizeImageUrl(imageSource.uri);
      
      // Preload image
      const imageLoadPromise = new Promise<void>((resolve, reject) => {
        RNImage.prefetch(optimizedUri)
          .then(() => {
            // Cache the successful load
            if (enableCache) {
              cache.set(cacheKey, { uri: optimizedUri, loaded: true }, {
                ttl: 24 * 60 * 60 * 1000, // 24 hours
                tags: ['image-cache'],
                priority: priority === 'critical' ? 'high' : 'medium',
              });
            }
            
            resolve();
          })
          .catch(reject);
      });

      // Simulate progress for better UX
      if (enableProgressiveLoading) {
        const progressInterval = setInterval(() => {
          setLoadingState(prev => {
            const newProgress = Math.min(prev.progress + Math.random() * 20, 90);
            
            Animated.timing(progressAnim, {
              toValue: newProgress / 100,
              duration: 200,
              useNativeDriver: false,
            }).start();
            
            onProgress?.(newProgress);
            return { ...prev, progress: newProgress };
          });
        }, 200);

        imageLoadPromise.finally(() => {
          clearInterval(progressInterval);
        });
      }

      await imageLoadPromise;

      // Complete loading
      setLoadingState(prev => ({
        ...prev,
        isLoading: false,
        isLoaded: true,
        progress: 100,
        retryCount: 0,
      }));

      // Animate progress to 100%
      Animated.timing(progressAnim, {
        toValue: 1,
        duration: 200,
        useNativeDriver: false,
      }).start();

      // Fade in animation
      if (enableFadeIn) {
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: fadeInDuration,
          useNativeDriver: true,
        }).start();
      }

      // Remove blur effect
      if (enableBlur) {
        Animated.timing(blurAnim, {
          toValue: 0,
          duration: fadeInDuration,
          useNativeDriver: false,
        }).start();
      }

      // Performance tracking
      const loadTime = Date.now() - loadStartTime.current;
      if (loadTime > 2000) { // Slow image load
        performanceMonitor.reportError(
          new Error(`Slow image load: ${loadTime}ms for ${imageSource.uri}`),
          'network'
        );
      }

      onLoad?.();

    } catch (error) {
      console.warn('Image load error:', error);
      
      setLoadingState(prev => ({
        ...prev,
        isLoading: false,
        hasError: true,
      }));

      // Retry logic
      if (enableRetry && loadingState.retryCount < maxRetries) {
        retryTimer.current = setTimeout(() => {
          setLoadingState(prev => ({
            ...prev,
            retryCount: prev.retryCount + 1,
            hasError: false,
          }));
          
          loadImage(imageSource);
        }, retryDelay * Math.pow(2, loadingState.retryCount)); // Exponential backoff
      } else {
        onError?.(error);
      }
    }
  }, [
    enableCache,
    enableFadeIn,
    enableBlur,
    enableRetry,
    enableProgressiveLoading,
    fadeInDuration,
    blurRadius,
    maxRetries,
    retryDelay,
    priority,
    onLoad,
    onError,
    onProgress,
    cache,
    performanceMonitor,
    getCacheKey,
    optimizeImageUrl,
    loadingState.retryCount,
  ]);

  // Handle lazy loading
  const handleLayout = useCallback((event: any) => {
    if (!enableLazyLoading) return;
    
    const { y } = event.nativeEvent.layout;
    const screenHeight = Dimensions.get('window').height;
    
    if (y <= screenHeight + lazyLoadOffset) {
      setIsInView(true);
    }
  }, [enableLazyLoading, lazyLoadOffset]);

  // Start loading when in view
  useEffect(() => {
    if (!isInView) return;
    
    const optimalSource = getOptimalSource();
    
    if (enablePreload) {
      preloadTimer.current = setTimeout(() => {
        loadImage(optimalSource);
      }, preloadDelay);
    } else {
      loadImage(optimalSource);
    }

    return () => {
      if (preloadTimer.current) {
        clearTimeout(preloadTimer.current);
      }
      if (retryTimer.current) {
        clearTimeout(retryTimer.current);
      }
    };
  }, [isInView, getOptimalSource, loadImage, enablePreload, preloadDelay]);

  // Render loading placeholder
  const renderPlaceholder = () => {
    if (placeholder) {
      return placeholder;
    }

    return (
      <RTLView style={[styles.placeholder, style]}>
        <ShimmerView style={styles.shimmer}>
          <RTLIcon name="image-outline" size={32} color="rgba(255, 255, 255, 0.5)" />
        </ShimmerView>
        
        {enableProgressiveLoading && loadingState.isLoading && (
          <RTLView style={styles.progressContainer}>
            <Animated.View
              style={[
                styles.progressBar,
                {
                  width: progressAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: ['0%', '100%'],
                  }),
                },
              ]}
            />
          </RTLView>
        )}
      </RTLView>
    );
  };

  // Render error fallback
  const renderFallback = () => {
    if (fallback) {
      return fallback;
    }

    return (
      <RTLView style={[styles.fallback, style]}>
        <RTLIcon name="image-off-outline" size={32} color="rgba(239, 68, 68, 0.7)" />
        {enableRetry && loadingState.retryCount < maxRetries && (
          <RTLView style={styles.retryContainer}>
            <RTLIcon name="refresh" size={16} color="rgba(255, 255, 255, 0.7)" />
          </RTLView>
        )}
      </RTLView>
    );
  };

  // Don't render anything if lazy loading and not in view
  if (enableLazyLoading && !isInView) {
    return (
      <RTLView 
        style={[styles.lazyContainer, style]} 
        onLayout={handleLayout}
      />
    );
  }

  // Render error state
  if (loadingState.hasError && !loadingState.isLoading) {
    return renderFallback();
  }

  // Render loading state
  if (!loadingState.isLoaded || loadingState.isLoading) {
    return renderPlaceholder();
  }

  // Render loaded image
  const imageStyle = [
    styles.image,
    style,
    {
      borderRadius: borderRadius || style?.borderRadius || 0,
      opacity: enableFadeIn ? fadeAnim : 1,
    },
    enableBlur && {
      blurRadius: blurAnim,
    },
  ];

  return (
    <Animated.Image
      ref={imageRef}
      source={{ uri: loadingState.currentSource?.uri }}
      style={imageStyle}
      resizeMode={resizeMode}
      onLoad={onLoad}
      onError={onError}
    />
  );
};

const createStyles = (colors: ThemeColors) => StyleSheet.create({
  image: {
    width: '100%',
    height: '100%',
  },
  placeholder: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  shimmer: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  progressContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 3,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  progressBar: {
    height: '100%',
    backgroundColor: '#3B82F6',
  },
  fallback: {
    backgroundColor: 'rgba(239, 68, 68, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(239, 68, 68, 0.3)',
    borderStyle: 'dashed',
  },
  retryContainer: {
    position: 'absolute',
    top: SPACING.sm,
    right: SPACING.sm,
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  lazyContainer: {
    backgroundColor: 'transparent',
  },
});
