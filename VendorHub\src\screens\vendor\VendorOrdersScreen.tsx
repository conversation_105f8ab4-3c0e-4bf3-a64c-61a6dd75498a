import React, { useState, useMemo } from 'react';
import { StyleSheet, RefreshControl, Alert } from 'react-native';
import { RTLFlatList, RTLIcon, RTLSafeAreaView, RTLText, RTLTouchableOpacity, RTLView } from '../../components/RTL';
import { useThemedStyles, useAuth, useOrders, useVendors, useI18n } from '../../hooks';
import { Card, Button, Input, EmptyState, StatusBadge, SwipeableRow } from '../../components';
import {
  SPACING,
  FONT_SIZES,
  FONT_WEIGHTS,
  BORDER_RADIUS,
  ICON_SIZES } from '../../constants/theme';
import { formatCurrency } from '../../utils';
import type { ThemeColors } from '../../contexts/ThemeContext';
import type { Order } from '../../contexts/DataContext';
import type { OrderStatus } from '../../constants';

interface VendorOrdersScreenProps {
  navigation: any;
}

export const VendorOrdersScreen: React.FC<VendorOrdersScreenProps> = ({ navigation }) => {
  const styles = useThemedStyles(createStyles);
  const { user } = useAuth();
  const { getOrdersByVendor, updateOrderStatus, bulkUpdateOrderStatus } = useOrders();
  const { getVendorById } = useVendors();
  const { t } = useI18n();
  
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilter, setSelectedFilter] = useState<'all' | OrderStatus>('all');
  const [selectedOrders, setSelectedOrders] = useState<string[]>([]);
  const [isSelectionMode, setIsSelectionMode] = useState(false);

  const vendor = user ? getVendorById(user.id) : null;
  const vendorOrders = vendor ? getOrdersByVendor(vendor.id) : [];

  const filteredOrders = useMemo(() => {
    let filtered = vendorOrders;

    // Filter by status
    if (selectedFilter !== 'all') {
      filtered = filtered.filter(order => order.status === selectedFilter);
    }

    // Filter by search query
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(order => 
        order.id.toLowerCase().includes(query) ||
        order.customerName.toLowerCase().includes(query) ||
        order.items.some(item => item.productName.toLowerCase().includes(query))
      );
    }

    return filtered.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
  }, [vendorOrders, selectedFilter, searchQuery]);

  const orderStats = useMemo(() => {
    const stats = {
      total: vendorOrders.length,
      pending: 0,
      confirmed: 0,
      shipped: 0,
      delivered: 0,
      cancelled: 0,
    };

    vendorOrders.forEach(order => {
      stats[order.status as keyof typeof stats]++;
    });

    return stats;
  }, [vendorOrders]);

  const onRefresh = React.useCallback(async () => {
    setRefreshing(true);
    // Simulate refresh delay
    setTimeout(() => setRefreshing(false), 1000);
  }, []);

  const handleOrderPress = (orderId: string) => {
    if (isSelectionMode) {
      toggleOrderSelection(orderId);
    } else {
      navigation.navigate('OrderDetails', { orderId });
    }
  };

  const toggleOrderSelection = (orderId: string) => {
    setSelectedOrders(prev => 
      prev.includes(orderId) 
        ? prev.filter(id => id !== orderId)
        : [...prev, orderId]
    );
  };

  const handleLongPress = (orderId: string) => {
    if (!isSelectionMode) {
      setIsSelectionMode(true);
      setSelectedOrders([orderId]);
    }
  };

  const exitSelectionMode = () => {
    setIsSelectionMode(false);
    setSelectedOrders([]);
  };

  const handleBulkStatusUpdate = async (newStatus: OrderStatus) => {
    if (selectedOrders.length === 0) return;

    Alert.alert(
      t('orders.updateOrders'),
      t('orders.updateOrdersConfirmation', { count: selectedOrders.length, status: newStatus }),
      [
        { text: t('common.cancel'), style: 'cancel' },
        {
          text: t('common.update'),
          onPress: async () => {
            try {
              await bulkUpdateOrderStatus(selectedOrders, newStatus);
              exitSelectionMode();
              Alert.alert(t('common.success'), t('orders.ordersUpdatedSuccess'));
            } catch (error) {
              Alert.alert(t('common.error'), t('orders.failedToUpdateOrders'));
            }
          },
        },
      ]
    );
  };

  const handleStatusUpdate = async (orderId: string, newStatus: OrderStatus) => {
    try {
      await updateOrderStatus(orderId, newStatus);
      Alert.alert(t('common.success'), t('orders.orderStatusUpdatedSuccess'));
    } catch (error) {
      Alert.alert(t('common.error'), t('orders.failedToUpdateOrderStatus'));
    }
  };

  const getStatusColor = (status: OrderStatus) => {
    switch (status) {
      case 'pending': return '#FF9800';
      case 'confirmed': return '#2196F3';
      case 'shipped': return '#9C27B0';
      case 'delivered': return '#4CAF50';
      case 'cancelled': return '#F44336';
      default: return '#757575';
    }
  };

  const getNextStatus = (currentStatus: OrderStatus): OrderStatus | null => {
    switch (currentStatus) {
      case 'pending': return 'confirmed';
      case 'confirmed': return 'shipped';
      case 'shipped': return 'delivered';
      default: return null;
    }
  };

  const filterOptions = [
    { key: 'all', label: t('orders.allOrders'), count: orderStats.total },
    { key: 'pending', label: t('orders.pending'), count: orderStats.pending },
    { key: 'confirmed', label: t('orders.confirmed'), count: orderStats.confirmed },
    { key: 'shipped', label: t('orders.shipped'), count: orderStats.shipped },
    { key: 'delivered', label: t('orders.delivered'), count: orderStats.delivered },
    { key: 'cancelled', label: t('orders.cancelled'), count: orderStats.cancelled },
  ];

  const renderOrderItem = ({ item }: { item: Order }) => {
    const isSelected = selectedOrders.includes(item.id);
    const nextStatus = getNextStatus(item.status);

    const swipeActions = {
      leftActions: nextStatus ? [
        {
          id: 'update-status',
          title: `Mark ${nextStatus}`,
          icon: 'checkmark-outline',
          color: '#FFFFFF',
          backgroundColor: getStatusColor(nextStatus),
          onPress: () => handleStatusUpdate(item.id, nextStatus),
        },
      ] : [],
      rightActions: [
        {
          id: 'view-details',
          title: t('orders.viewDetails'),
          icon: 'eye-outline',
          color: '#FFFFFF',
          backgroundColor: '#2196F3',
          onPress: () => navigation.navigate('OrderDetails', { orderId: item.id }),
        },
        {
          id: 'cancel-order',
          title: t('common.cancel'),
          icon: 'close-outline',
          color: '#FFFFFF',
          backgroundColor: '#F44336',
          onPress: () => handleStatusUpdate(item.id, 'cancelled'),
        },
      ],
    };

    return (
      <SwipeableRow
        leftActions={swipeActions.leftActions}
        rightActions={swipeActions.rightActions}
        style={styles.swipeableContainer}
      >
        <Card 
          style={[
            styles.orderCard,
            isSelected && styles.orderCardSelected,
          ].filter(Boolean) as any}
          variant="elevated"
        >
          <RTLTouchableOpacity
            style={styles.orderContent}
            onPress={() => handleOrderPress(item.id)}
            onLongPress={() => handleLongPress(item.id)}
          >
            {isSelectionMode && (
              <RTLView style={styles.selectionIndicator}>
                <RTLIcon
                  name={isSelected ? "checkmark-circle" : "ellipse-outline"}
                  size={24}
                  color={isSelected ? "#4CAF50" : "#CCCCCC"}
                />
              </RTLView>
            )}

            <RTLView style={styles.orderHeader}>
              <RTLView style={styles.orderInfo}>
                <RTLText style={styles.orderId}>Order #{item.id.slice(-6)}</RTLText>
                <RTLText style={styles.orderDate}>
                  {new Date(item.createdAt).toLocaleDateString()}
                </RTLText>
              </RTLView>
              <StatusBadge
                status={item.status}
                customColor={getStatusColor(item.status)}
              />
            </RTLView>

            <RTLText style={styles.customerName}>{item.customerName}</RTLText>

            <RTLView style={styles.orderItems}>
              <RTLText style={styles.itemsText}>
                {item.items.length} item{item.items.length !== 1 ? 's' : ''}
              </RTLText>
              <RTLText style={styles.itemsPreview} numberOfLines={1}>
                {item.items.map(item => item.productName).join(', ')}
              </RTLText>
            </RTLView>

            <RTLView style={styles.orderFooter}>
              <RTLText style={styles.orderTotal}>
                {formatCurrency(item.totalAmount)}
              </RTLText>
              {nextStatus && !isSelectionMode && (
                <RTLTouchableOpacity
                  style={[styles.quickActionButton, { backgroundColor: getStatusColor(nextStatus) }]}
                  onPress={() => handleStatusUpdate(item.id, nextStatus)}
                >
                  <RTLText style={styles.quickActionText}>
                    Mark {nextStatus}
                  </RTLText>
                </RTLTouchableOpacity>
              )}
            </RTLView>
          </RTLTouchableOpacity>
        </Card>
      </SwipeableRow>
    );
  };

  const renderHeader = () => (
    <RTLView style={styles.header}>
      {/* Search Bar */}
      <Input
        placeholder="Search orders..."
        value={searchQuery}
        onChangeText={setSearchQuery}
        leftIcon="search-outline"
        style={styles.searchInput}
      />

      {/* Filter Chips */}
      <RTLFlatList
        data={filterOptions}
        horizontal
        showsHorizontalScrollIndicator={false}
        keyExtractor={(item) => item.key}
        contentContainerStyle={styles.filterContainer}
        renderItem={({ item }) => (
          <RTLTouchableOpacity
            style={[
              styles.filterChip,
              selectedFilter === item.key && styles.filterChipActive,
            ]}
            onPress={() => setSelectedFilter(item.key as any)}
          >
            <RTLText
              style={[
                styles.filterChipText,
                selectedFilter === item.key && styles.filterChipTextActive,
              ].filter(Boolean) as any}
            >
              {item.label} ({item.count})
            </RTLText>
          </RTLTouchableOpacity>
        )}
      />

      {/* Selection Mode Header */}
      {isSelectionMode && (
        <RTLView style={styles.selectionHeader}>
          <RTLTouchableOpacity onPress={exitSelectionMode} style={styles.exitSelectionButton}>
            <RTLIcon name="close" size={24} color="#666" />
          </RTLTouchableOpacity>
          <RTLText style={styles.selectionText}>
            {selectedOrders.length} selected
          </RTLText>
          <RTLView style={styles.bulkActions}>
            <RTLTouchableOpacity
              style={[styles.bulkActionButton, { backgroundColor: '#2196F3' }]}
              onPress={() => handleBulkStatusUpdate('confirmed')}
            >
              <RTLText style={styles.bulkActionText}>Confirm</RTLText>
            </RTLTouchableOpacity>
            <RTLTouchableOpacity
              style={[styles.bulkActionButton, { backgroundColor: '#9C27B0' }]}
              onPress={() => handleBulkStatusUpdate('shipped')}
            >
              <RTLText style={styles.bulkActionText}>Ship</RTLText>
            </RTLTouchableOpacity>
          </RTLView>
        </RTLView>
      )}

      {/* Results Info */}
      <RTLView style={styles.resultsInfo}>
        <RTLText style={styles.resultsText}>
          {filteredOrders.length} order{filteredOrders.length !== 1 ? 's' : ''} found
        </RTLText>
      </RTLView>
    </RTLView>
  );

  return (
    <RTLSafeAreaView style={styles.container}>
      <RTLFlatList
        data={filteredOrders}
        renderItem={renderOrderItem}
        keyExtractor={(item) => item.id}
        ListHeaderComponent={renderHeader}
        ListEmptyComponent={
          <EmptyState
            icon="receipt-outline"
            title={t('orders.noOrdersFound')}
            description={
              searchQuery || selectedFilter !== 'all'
                ? t('orders.adjustSearchFilter')
                : t('orders.ordersWillAppearHere')
            }
          />
        }
        contentContainerStyle={styles.listContent}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      />
    </RTLSafeAreaView>
  );
};

const createStyles = (colors: ThemeColors) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    paddingHorizontal: SPACING.lg,
    paddingTop: SPACING.md,
    paddingBottom: SPACING.lg,
    backgroundColor: colors.background,
  },
  searchInput: {
    marginBottom: SPACING.md,
  },
  filterContainer: {
    paddingBottom: SPACING.md,
    gap: SPACING.sm,
  },
  filterChip: {
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    borderRadius: BORDER_RADIUS.full,
    backgroundColor: colors.surface,
    borderWidth: 1,
    borderColor: colors.border,
    marginRight: SPACING.sm,
  },
  filterChipActive: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  filterChipText: {
    fontSize: FONT_SIZES.sm,
    color: colors.text,
    fontWeight: FONT_WEIGHTS.medium,
  },
  filterChipTextActive: {
    color: '#FFFFFF',
  },
  selectionHeader: {
    flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.md,
    backgroundColor: colors.surface,
    borderRadius: BORDER_RADIUS.md,
    marginBottom: SPACING.md,
  },
  exitSelectionButton: {
    padding: SPACING.sm,
  },
  selectionText: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.semiBold,
    color: colors.text,
    flex: 1,
    textAlign: 'center',
  },
  bulkActions: {
    flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
    gap: SPACING.sm,
  },
  bulkActionButton: {
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    borderRadius: BORDER_RADIUS.md,
  },
  bulkActionText: {
    fontSize: FONT_SIZES.sm,
    color: '#FFFFFF',
    fontWeight: FONT_WEIGHTS.medium,
  },
  resultsInfo: {
    paddingTop: SPACING.sm,
  },
  resultsText: {
    fontSize: FONT_SIZES.sm,
    color: colors.textSecondary,
  },
  listContent: {
    paddingBottom: SPACING.xl,
  },
  swipeableContainer: {
    marginHorizontal: SPACING.lg,
    marginBottom: SPACING.md,
  },
  orderCard: {
    padding: SPACING.lg,
  },
  orderCardSelected: {
    borderWidth: 2,
    borderColor: '#4CAF50',
    backgroundColor: 'rgba(76, 175, 80, 0.05)',
  },
  orderContent: {
    gap: SPACING.md,
  },
  selectionIndicator: {
    position: 'absolute',
    top: -SPACING.sm,
    right: -SPACING.sm,
    zIndex: 1,
  },
  orderHeader: {
    flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  orderInfo: {
    flex: 1,
  },
  orderId: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.semiBold,
    color: colors.text,
    marginBottom: SPACING.xs,
  },
  orderDate: {
    fontSize: FONT_SIZES.sm,
    color: colors.textSecondary,
  },
  customerName: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.medium,
    color: colors.text,
  },
  orderItems: {
    gap: SPACING.xs,
  },
  itemsText: {
    fontSize: FONT_SIZES.sm,
    fontWeight: FONT_WEIGHTS.medium,
    color: colors.text,
  },
  itemsPreview: {
    fontSize: FONT_SIZES.sm,
    color: colors.textSecondary,
  },
  orderFooter: {
    flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  orderTotal: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.bold,
    color: '#4CAF50',
  },
  quickActionButton: {
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    borderRadius: BORDER_RADIUS.md,
  },
  quickActionText: {
    fontSize: FONT_SIZES.sm,
    color: '#FFFFFF',
    fontWeight: FONT_WEIGHTS.medium,
  },
});
