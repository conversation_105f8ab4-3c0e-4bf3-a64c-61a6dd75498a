#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Configuration
const config = {
  srcDir: path.join(__dirname, '../src'),
  outputFile: path.join(__dirname, '../JSX_CLOSING_TAGS_FIXES_REPORT.md'),
  excludePatterns: [
    /node_modules/,
    /\.git/,
    /\.expo/,
    /dist/,
    /build/,
    /coverage/,
    /\.DS_Store/,
    /\.env/,
    /package-lock\.json/,
    /yarn\.lock/,
    /\.log$/,
    /\.md$/,
    /\.json$/,
    /\.js\.map$/,
    /\.d\.ts$/,
    /scripts/,
    /assets/,
  ],
  fileExtensions: ['.tsx', '.ts', '.jsx', '.js'],
  
  // JSX closing tag fixes
  jsxFixes: [
    {
      name: 'RTLViewClosingTag',
      pattern: /<RTLView[^>]*>[\s\S]*?<\/View>/g,
      replacement: (match) => match.replace(/<\/View>/, '</RTLView>'),
      description: 'Fixed RTLView closing tag mismatch'
    },
    {
      name: 'RTLTextClosingTag',
      pattern: /<RTLText[^>]*>[\s\S]*?<\/Text>/g,
      replacement: (match) => match.replace(/<\/Text>/, '</RTLText>'),
      description: 'Fixed RTLText closing tag mismatch'
    },
    {
      name: 'RTLTouchableOpacityClosingTag',
      pattern: /<RTLTouchableOpacity[^>]*>[\s\S]*?<\/TouchableOpacity>/g,
      replacement: (match) => match.replace(/<\/TouchableOpacity>/, '</RTLTouchableOpacity>'),
      description: 'Fixed RTLTouchableOpacity closing tag mismatch'
    },
    {
      name: 'RTLScrollViewClosingTag',
      pattern: /<RTLScrollView[^>]*>[\s\S]*?<\/ScrollView>/g,
      replacement: (match) => match.replace(/<\/ScrollView>/, '</RTLScrollView>'),
      description: 'Fixed RTLScrollView closing tag mismatch'
    },
    {
      name: 'RTLSafeAreaViewClosingTag',
      pattern: /<RTLSafeAreaView[^>]*>[\s\S]*?<\/SafeAreaView>/g,
      replacement: (match) => match.replace(/<\/SafeAreaView>/, '</RTLSafeAreaView>'),
      description: 'Fixed RTLSafeAreaView closing tag mismatch'
    },
    {
      name: 'RTLInputClosingTag',
      pattern: /<RTLInput[^>]*>[\s\S]*?<\/TextInput>/g,
      replacement: (match) => match.replace(/<\/TextInput>/, '</RTLInput>'),
      description: 'Fixed RTLInput closing tag mismatch'
    }
  ]
};

class JSXClosingTagsFixer {
  constructor() {
    this.results = {
      filesProcessed: 0,
      filesFixed: 0,
      totalFixes: 0,
      fixesByType: {},
      fixedFiles: []
    };
  }

  shouldExcludeFile(filePath) {
    return config.excludePatterns.some(pattern => pattern.test(filePath));
  }

  isValidFileExtension(filePath) {
    return config.fileExtensions.some(ext => filePath.endsWith(ext));
  }

  scanDirectory(dirPath) {
    const items = fs.readdirSync(dirPath);
    
    for (const item of items) {
      const fullPath = path.join(dirPath, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        if (!this.shouldExcludeFile(fullPath)) {
          this.scanDirectory(fullPath);
        }
      } else if (stat.isFile()) {
        if (this.isValidFileExtension(fullPath) && !this.shouldExcludeFile(fullPath)) {
          this.processFile(fullPath);
        }
      }
    }
  }

  processFile(filePath) {
    this.results.filesProcessed++;
    
    try {
      let content = fs.readFileSync(filePath, 'utf8');
      let originalContent = content;
      let fileFixed = false;
      let fileFixes = [];

      // Apply each fix pattern
      config.jsxFixes.forEach(fix => {
        const matches = content.match(fix.pattern);
        
        if (matches && matches.length > 0) {
          const newContent = content.replace(fix.pattern, fix.replacement);
          if (newContent !== content) {
            content = newContent;
            fileFixed = true;
            fileFixes.push({
              type: fix.name,
              count: matches.length,
              description: fix.description
            });
            
            if (!this.results.fixesByType[fix.name]) {
              this.results.fixesByType[fix.name] = 0;
            }
            this.results.fixesByType[fix.name] += matches.length;
            this.results.totalFixes += matches.length;
          }
        }
      });

      // Write file if changes were made
      if (fileFixed) {
        fs.writeFileSync(filePath, content, 'utf8');
        this.results.filesFixed++;
        this.results.fixedFiles.push({
          path: path.relative(config.srcDir, filePath),
          fixes: fileFixes
        });
        
        console.log(`✅ Fixed: ${path.relative(config.srcDir, filePath)}`);
      }
    } catch (error) {
      console.error(`❌ Error processing ${filePath}:`, error.message);
    }
  }

  generateReport() {
    const report = [];
    
    report.push(
      '# JSX Closing Tags Fixes Report',
      '',
      `**Generated:** ${new Date().toLocaleString()}`,
      `**Files Processed:** ${this.results.filesProcessed}`,
      `**Files Fixed:** ${this.results.filesFixed}`,
      `**Total Fixes Applied:** ${this.results.totalFixes}`,
      ''
    );

    // Fixes summary table
    if (Object.keys(this.results.fixesByType).length > 0) {
      report.push(
        '## 📊 Fixes Summary',
        '',
        '| Fix Type | Count | Description |',
        '|----------|-------|-------------|'
      );
      
      Object.entries(this.results.fixesByType).forEach(([type, count]) => {
        const fix = config.jsxFixes.find(f => f.name === type);
        report.push(`| ${type} | ${count} | ${fix ? fix.description : 'Unknown'} |`);
      });
      
      report.push('');
    }

    // Fixed files details
    if (this.results.fixedFiles.length > 0) {
      report.push('## 📁 Fixed Files', '');
      
      this.results.fixedFiles.forEach(file => {
        report.push(`### ${file.path}`, '');
        report.push(`**Total fixes:** ${file.fixes.reduce((sum, fix) => sum + fix.count, 0)}`, '');
        
        file.fixes.forEach(fix => {
          report.push(`- **${fix.type}** (${fix.count} occurrences): ${fix.description}`);
        });
        
        report.push('');
      });
    }

    return report.join('\n');
  }

  run() {
    console.log('🔍 Scanning for JSX closing tag mismatches...');
    
    // Scan source directory
    if (fs.existsSync(config.srcDir)) {
      this.scanDirectory(config.srcDir);
    }

    // Generate and save report
    const report = this.generateReport();
    fs.writeFileSync(config.outputFile, report, 'utf8');
    
    console.log('\n📊 Summary:');
    console.log(`Files processed: ${this.results.filesProcessed}`);
    console.log(`Files fixed: ${this.results.filesFixed}`);
    console.log(`Total fixes: ${this.results.totalFixes}`);
    console.log(`Report saved: ${config.outputFile}`);
    
    if (this.results.totalFixes > 0) {
      console.log('\n✅ JSX closing tag mismatches have been fixed!');
    } else {
      console.log('\n✅ No JSX closing tag mismatches found.');
    }
  }
}

// Run the fixer
const fixer = new JSXClosingTagsFixer();
fixer.run();
