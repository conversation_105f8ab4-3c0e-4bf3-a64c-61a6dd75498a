import React, { useState } from 'react';
import { StyleSheet, Alert, RefreshControl } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useThemedStyles, useAuth, useOrders, useCart, useI18n } from '../../hooks';
import { Card, Button, Input, EmptyState, StatusBadge, SingleImagePicker, LanguageSelector } from '../../components';
import { RTLView, RTLText, RTLIcon, RTLSafeAreaView, RTLScrollView, RTLTouchableOpacity } from '../../components/RTL';
import {
  SPACING,
  FONT_SIZES,
  FONT_WEIGHTS,
  BORDER_RADIUS,
  GRADIENTS,
  ICON_SIZES } from '../../constants/theme';
import { formatCurrency } from '../../utils';
import type { ThemeColors } from '../../contexts/ThemeContext';

interface ProfileScreenProps {
  navigation: any;
}

interface ProfileFormData {
  name: string;
  email: string;
  phone: string;
  address: string;
  avatar: string | null;
}

export const ProfileScreen: React.FC<ProfileScreenProps> = ({ navigation }) => {
  const styles = useThemedStyles(createStyles);
  const { user, updateUser, logout } = useAuth();
  const { getCustomerOrders } = useOrders();
  const { cartItemCount } = useCart();
  const { t } = useI18n();
  
  const [isEditing, setIsEditing] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  
  const [formData, setFormData] = useState<ProfileFormData>({
    name: user?.name || '',
    email: user?.email || '',
    phone: user?.phone || '',
    address: user?.address || '',
    avatar: user?.avatar || null,
  });

  const customerOrders = user ? getCustomerOrders(user.id) : [];
  const recentOrders = customerOrders.slice(0, 3);

  const onRefresh = React.useCallback(async () => {
    setRefreshing(true);
    // Simulate refresh delay
    setTimeout(() => setRefreshing(false), 1000);
  }, []);

  const handleSaveProfile = async () => {
    if (!user) return;

    setIsLoading(true);
    try {
      await updateUser({
        name: formData.name.trim(),
        phone: formData.phone.trim(),
        address: formData.address.trim(),
        avatar: formData.avatar || undefined,
      });
      
      setIsEditing(false);
      Alert.alert(t('common.success'), t('profile.profileUpdated'));
    } catch (error) {
      Alert.alert(t('common.error'), t('profile.profileUpdateFailed'));
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancelEdit = () => {
    setFormData({
      name: user?.name || '',
      email: user?.email || '',
      phone: user?.phone || '',
      address: user?.address || '',
      avatar: user?.avatar || null,
    });
    setIsEditing(false);
  };

  const handleLogout = () => {
    Alert.alert(
      t('profile.logout'),
      t('profile.logoutConfirmation'),
      [
        { text: t('common.cancel'), style: 'cancel' },
        { text: t('profile.logout'), style: 'destructive', onPress: logout },
      ]
    );
  };

  const handleOrderPress = (orderId: string) => {
    navigation.navigate('OrderDetails', { orderId });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return '#FF9800';
      case 'confirmed': return '#2196F3';
      case 'shipped': return '#9C27B0';
      case 'delivered': return '#4CAF50';
      case 'cancelled': return '#F44336';
      default: return '#757575';
    }
  };

  if (!user) {
    return (
      <RTLSafeAreaView style={styles.container}>
        <EmptyState
          icon="person-outline"
          title={t('profile.notLoggedIn')}
          description={t('profile.pleaseLoginToView')}
          actionLabel={t('auth.login')}
          onAction={() => navigation.navigate('Auth')}
        />
      </RTLSafeAreaView>
    );
  }

  return (
    <RTLSafeAreaView style={styles.container}>
      <RTLScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <LinearGradient colors={GRADIENTS.primary} style={styles.header}>
          <RTLView style={styles.headerContent}>
            <RTLView style={styles.profileSection}>
              <SingleImagePicker
                image={formData.avatar}
                onImageChange={(uri) => setFormData(prev => ({ ...prev, avatar: uri }))}
                placeholder="person-outline"
                style={styles.avatarPicker}

              />
              <RTLView style={styles.profileInfo}>
                <RTLText style={styles.userName}>{user.name}</RTLText>
                <RTLText style={styles.userEmail}>{user.email}</RTLText>
                <RTLView style={styles.userStats}>
                  <RTLView style={styles.statItem}>
                    <RTLText style={styles.statValue}>{customerOrders.length}</RTLText>
                    <RTLText style={styles.statLabel}>{t('profile.orders')}</RTLText>
                  </RTLView>
                  <RTLView style={styles.statDivider} />
                  <RTLView style={styles.statItem}>
                    <RTLText style={styles.statValue}>{cartItemCount}</RTLText>
                    <RTLText style={styles.statLabel}>{t('profile.cartItems')}</RTLText>
                  </RTLView>
                </RTLView>
              </RTLView>
            </RTLView>
            <RTLTouchableOpacity onPress={handleLogout} style={styles.logoutButton}>
              <RTLIcon name="log-out-outline" size={24} color="#FFFFFF" />
            </RTLTouchableOpacity>
          </RTLView>
        </LinearGradient>

        {/* Profile Form */}
        <RTLView style={styles.section}>
          <RTLView style={styles.sectionHeader}>
            <RTLText style={styles.sectionTitle}>{t('profile.personalInformation')}</RTLText>
            {!isEditing ? (
              <RTLTouchableOpacity onPress={() => setIsEditing(true)} style={styles.editButton}>
                <RTLIcon name="pencil-outline" size={20} color="#667eea" />
                <RTLText style={styles.editButtonText}>{t('common.edit')}</RTLText>
              </RTLTouchableOpacity>
            ) : (
              <RTLView style={styles.editActions}>
                <RTLTouchableOpacity onPress={handleCancelEdit} style={styles.cancelButton}>
                  <RTLText style={styles.cancelButtonText}>{t('profile.cancelEdit')}</RTLText>
                </RTLTouchableOpacity>
                <RTLTouchableOpacity onPress={handleSaveProfile} style={styles.saveButton}>
                  <RTLText style={styles.saveButtonText}>{t('common.save')}</RTLText>
                </RTLTouchableOpacity>
              </RTLView>
            )}
          </RTLView>

          <Card style={styles.formCard} variant="elevated">
            <Input
              label={t('profile.name')}
              value={formData.name}
              onChangeText={(text) => setFormData(prev => ({ ...prev, name: text }))}
              placeholder={t('profile.enterFullName')}
              leftIcon="person-outline"
              editable={isEditing}
              style={!isEditing && styles.disabledInput}
            />

            <Input
              label={t('profile.email')}
              value={formData.email}
              placeholder={t('profile.enterEmail')}
              leftIcon="mail-outline"
              editable={false}
              style={styles.disabledInput}
            />

            <Input
              label={t('profile.phone')}
              value={formData.phone}
              onChangeText={(text) => setFormData(prev => ({ ...prev, phone: text }))}
              placeholder={t('profile.enterPhoneNumber')}
              leftIcon="call-outline"
              keyboardType="phone-pad"
              editable={isEditing}
              style={!isEditing && styles.disabledInput}
            />

            <Input
              label={t('profile.address')}
              value={formData.address}
              onChangeText={(text) => setFormData(prev => ({ ...prev, address: text }))}
              placeholder={t('profile.enterAddress')}
              leftIcon="location-outline"
              multiline
              numberOfLines={3}
              editable={isEditing}
              style={!isEditing && styles.disabledInput}
            />
          </Card>
        </RTLView>

        {/* Recent Orders */}
        <RTLView style={styles.section}>
          <RTLView style={styles.sectionHeader}>
            <RTLText style={styles.sectionTitle}>{t('profile.recentOrders')}</RTLText>
            {customerOrders.length > 0 && (
              <RTLTouchableOpacity
                onPress={() => navigation.navigate('OrderHistory')}
                style={styles.viewAllButton}
              >
                <RTLText style={styles.viewAllText}>{t('profile.viewAll')}</RTLText>
                <RTLIcon name="chevron-forward" size={16} color="#667eea" />
              </RTLTouchableOpacity>
            )}
          </RTLView>

          {recentOrders.length > 0 ? (
            <RTLView style={styles.ordersList}>
              {recentOrders.map((order) => (
                <Card key={order.id} style={styles.orderCard} variant="elevated">
                  <RTLTouchableOpacity
                    style={styles.orderContent}
                    onPress={() => handleOrderPress(order.id)}
                  >
                    <RTLView style={styles.orderHeader}>
                      <RTLText style={styles.orderId}>Order #{order.id.slice(-6)}</RTLText>
                      <StatusBadge
                        status={order.status}
                        customColor={getStatusColor(order.status)}
                      />
                    </RTLView>
                    <RTLText style={styles.orderDate}>
                      {new Date(order.createdAt).toLocaleDateString()}
                    </RTLText>
                    <RTLView style={styles.orderFooter}>
                      <RTLText style={styles.orderTotal}>
                        {formatCurrency(order.totalAmount)}
                      </RTLText>
                      <RTLText style={styles.orderItems}>
                        {order.items.length} {order.items.length === 1 ? t('cart.item') : t('cart.items')}
                      </RTLText>
                    </RTLView>
                  </RTLTouchableOpacity>
                </Card>
              ))}
            </RTLView>
          ) : (
            <EmptyState
              icon="receipt-outline"
              title={t('profile.noRecentOrders')}
              description={t('profile.startShoppingToSeeOrders')}
              actionLabel={t('cart.startShopping')}
              onAction={() => navigation.navigate('Home')}
            />
          )}
        </RTLView>

        {/* Quick Actions */}
        <RTLView style={styles.section}>
          <RTLText style={styles.sectionTitle}>{t('profile.quickActions')}</RTLText>
          <RTLView style={styles.quickActions}>
            <RTLTouchableOpacity
              style={styles.actionButton}
              onPress={() => navigation.navigate('OrderHistory')}
            >
              <RTLView style={[styles.actionIcon, { backgroundColor: '#4CAF50' }]}>
                <RTLIcon name="receipt-outline" size={24} color="#FFFFFF" />
              </RTLView>
              <RTLText style={styles.actionText}>{t('profile.orderHistory')}</RTLText>
            </RTLTouchableOpacity>

            <RTLTouchableOpacity
              style={styles.actionButton}
              onPress={() => navigation.navigate('Cart')}
            >
              <RTLView style={[styles.actionIcon, { backgroundColor: '#2196F3' }]}>
                <RTLIcon name="bag-outline" size={24} color="#FFFFFF" />
              </RTLView>
              <RTLText style={styles.actionText}>{t('profile.shoppingCart')}</RTLText>
            </RTLTouchableOpacity>

            <RTLTouchableOpacity
              style={styles.actionButton}
              onPress={() => navigation.navigate('Search')}
            >
              <RTLView style={[styles.actionIcon, { backgroundColor: '#FF9800' }]}>
                <RTLIcon name="search-outline" size={24} color="#FFFFFF" />
              </RTLView>
              <RTLText style={styles.actionText}>{t('profile.searchProducts')}</RTLText>
            </RTLTouchableOpacity>
          </RTLView>
        </RTLView>

        {/* Language Settings */}
        <RTLView style={styles.section}>
          <RTLText style={styles.sectionTitle}>{t('profile.languageSettings')}</RTLText>
          <Card style={styles.languageCard}>
            <LanguageSelector showLabel={true} />
          </Card>
        </RTLView>
      </RTLScrollView>
    </RTLSafeAreaView>
  );
};

const createStyles = (colors: ThemeColors) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    paddingTop: SPACING.lg,
    paddingBottom: SPACING.xl,
    paddingHorizontal: SPACING.lg,
  },
  headerContent: {
    flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  profileSection: {
    flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
    alignItems: 'center',
    flex: 1,
  },
  avatarPicker: {
    width: 80,
    height: 80,
    borderRadius: 40,
    marginRight: SPACING.lg,
  },
  profileInfo: {
    flex: 1,
  },
  userName: {
    fontSize: FONT_SIZES.xl,
    fontWeight: FONT_WEIGHTS.bold,
    color: '#FFFFFF',
    marginBottom: SPACING.xs,
  },
  userEmail: {
    fontSize: FONT_SIZES.md,
    color: 'rgba(255, 255, 255, 0.8)',
    marginBottom: SPACING.md,
  },
  userStats: {
    flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
    alignItems: 'center',
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.bold,
    color: '#FFFFFF',
  },
  statLabel: {
    fontSize: FONT_SIZES.sm,
    color: 'rgba(255, 255, 255, 0.7)',
  },
  statDivider: {
    width: 1,
    height: 30,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    marginHorizontal: SPACING.lg,
  },
  logoutButton: {
    padding: SPACING.sm,
    borderRadius: BORDER_RADIUS.md,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  section: {
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.lg,
  },
  sectionHeader: {
    flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  sectionTitle: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.semiBold,
    color: colors.text,
  },
  editButton: {
    flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
    alignItems: 'center',
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    borderRadius: BORDER_RADIUS.md,
    backgroundColor: colors.surface,
  },
  editButtonText: {
    fontSize: FONT_SIZES.sm,
    color: '#667eea',
    marginLeft: SPACING.xs,
    fontWeight: FONT_WEIGHTS.medium,
  },
  editActions: {
    flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
    gap: SPACING.sm,
  },
  cancelButton: {
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    borderRadius: BORDER_RADIUS.md,
    backgroundColor: colors.surface,
  },
  cancelButtonText: {
    fontSize: FONT_SIZES.sm,
    color: colors.textSecondary,
    fontWeight: FONT_WEIGHTS.medium,
  },
  saveButton: {
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    borderRadius: BORDER_RADIUS.md,
    backgroundColor: '#667eea',
  },
  saveButtonText: {
    fontSize: FONT_SIZES.sm,
    color: '#FFFFFF',
    fontWeight: FONT_WEIGHTS.medium,
  },
  formCard: {
    padding: SPACING.lg,
  },
  disabledInput: {
    opacity: 0.6,
  },
  viewAllButton: {
    flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
    alignItems: 'center',
  },
  viewAllText: {
    fontSize: FONT_SIZES.sm,
    color: '#667eea',
    fontWeight: FONT_WEIGHTS.medium,
    marginRight: SPACING.xs,
  },
  ordersList: {
    gap: SPACING.md,
  },
  orderCard: {
    padding: SPACING.lg,
  },
  orderContent: {
    gap: SPACING.sm,
  },
  orderHeader: {
    flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  orderId: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.semiBold,
    color: colors.text,
  },
  orderDate: {
    fontSize: FONT_SIZES.sm,
    color: colors.textSecondary,
  },
  orderFooter: {
    flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  orderTotal: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.bold,
    color: '#4CAF50',
  },
  orderItems: {
    fontSize: FONT_SIZES.sm,
    color: colors.textSecondary,
  },
  quickActions: {
    flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
    justifyContent: 'space-around',
    marginTop: SPACING.md,
  },
  actionButton: {
    alignItems: 'center',
    flex: 1,
  },
  actionIcon: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  actionText: {
    fontSize: FONT_SIZES.sm,
    color: colors.text,
    textAlign: 'center',
    fontWeight: FONT_WEIGHTS.medium,
  },
  languageCard: {
    padding: SPACING.md,
  },
});
