#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Configuration
const config = {
  srcDir: path.join(__dirname, '../src'),
  appDir: path.join(__dirname, '../app'),
  componentsDir: path.join(__dirname, '../components'),
  excludePatterns: [
    /node_modules/,
    /\.git/,
    /\.expo/,
    /dist/,
    /build/,
    /coverage/,
    /\.DS_Store/,
    /\.env/,
    /package-lock\.json/,
    /yarn\.lock/,
    /\.log$/,
    /\.md$/,
    /\.json$/,
    /\.js\.map$/,
    /\.d\.ts$/,
    /constants/,
    /utils/,
    /services/,
    /hooks/,
    /contexts/,
    /navigation/,
    /scripts/,
    /assets/,
  ],
  fileExtensions: ['.tsx', '.ts', '.jsx', '.js'],
};

class TextNodeFinder {
  constructor() {
    this.results = [];
  }

  shouldExcludeFile(filePath) {
    const relativePath = path.relative(process.cwd(), filePath);
    return config.excludePatterns.some(pattern => pattern.test(relativePath));
  }

  hasValidExtension(filePath) {
    return config.fileExtensions.some(ext => filePath.endsWith(ext));
  }

  analyzeFileContent(filePath, content) {
    const relativePath = path.relative(process.cwd(), filePath);
    const lines = content.split('\n');
    
    lines.forEach((line, index) => {
      const lineNumber = index + 1;
      const trimmedLine = line.trim();
      
      // Skip comments and imports
      if (trimmedLine.startsWith('//') || trimmedLine.startsWith('/*') || 
          trimmedLine.startsWith('import') || trimmedLine.startsWith('export')) {
        return;
      }

      // Look for very specific patterns that could cause text node issues
      const patterns = [
        // Direct string literals in JSX
        {
          regex: />\s*['"`][^<>]*['"`]\s*</g,
          description: 'Direct string literal in JSX',
          severity: 'high'
        },
        // Variables that might be strings in JSX
        {
          regex: />\s*\{[a-zA-Z_$][a-zA-Z0-9_$]*\}\s*</g,
          description: 'Variable in JSX - might be string',
          severity: 'medium'
        },
        // Function calls that might return strings
        {
          regex: />\s*\{[a-zA-Z_$][a-zA-Z0-9_$]*\([^)]*\)\}\s*</g,
          description: 'Function call in JSX - might return string',
          severity: 'medium'
        },
        // Template literals
        {
          regex: />\s*\{`[^`]*`\}\s*</g,
          description: 'Template literal in JSX',
          severity: 'high'
        },
        // Conditional expressions with strings
        {
          regex: />\s*\{[^}]*\?\s*['"`][^'"`]*['"`]/g,
          description: 'Conditional with string literal',
          severity: 'high'
        },
        // String concatenation
        {
          regex: />\s*\{[^}]*\+[^}]*\}\s*</g,
          description: 'String concatenation in JSX',
          severity: 'medium'
        },
        // Object property access that might be strings
        {
          regex: />\s*\{[a-zA-Z_$][a-zA-Z0-9_$]*\.[a-zA-Z_$][a-zA-Z0-9_$]*\}\s*</g,
          description: 'Object property access - might be string',
          severity: 'medium'
        },
        // Array map that might return strings
        {
          regex: />\s*\{[^}]*\.map\([^)]*\)\}\s*</g,
          description: 'Array map - might return strings',
          severity: 'high'
        },
        // Split operations
        {
          regex: />\s*\{[^}]*\.split\([^)]*\)\}\s*</g,
          description: 'String split operation',
          severity: 'high'
        },
        // Join operations
        {
          regex: />\s*\{[^}]*\.join\([^)]*\)\}\s*</g,
          description: 'Array join operation',
          severity: 'high'
        },
        // Replace operations
        {
          regex: />\s*\{[^}]*\.replace\([^)]*\)\}\s*</g,
          description: 'String replace operation',
          severity: 'medium'
        }
      ];

      patterns.forEach(pattern => {
        const matches = trimmedLine.match(pattern.regex);
        if (matches) {
          // Skip if it's already inside a Text component
          if (!trimmedLine.includes('<RTLText') && !trimmedLine.includes('<Text') && 
              !trimmedLine.includes('</RTLText') && !trimmedLine.includes('</Text')) {
            this.results.push({
              file: relativePath,
              line: lineNumber,
              content: trimmedLine,
              description: pattern.description,
              severity: pattern.severity,
              matches: matches
            });
          }
        }
      });

      // Special checks for common problematic patterns
      if (trimmedLine.includes('return ') && trimmedLine.includes('.map(') && 
          !trimmedLine.includes('<') && !trimmedLine.includes('RTLText')) {
        this.results.push({
          file: relativePath,
          line: lineNumber,
          content: trimmedLine,
          description: 'Map operation in return statement - might return strings',
          severity: 'high',
          matches: ['map in return']
        });
      }

      // Check for whitespace between tags
      if (/>\s+</.test(trimmedLine) && !trimmedLine.includes('RTLText') && !trimmedLine.includes('Text')) {
        this.results.push({
          file: relativePath,
          line: lineNumber,
          content: trimmedLine,
          description: 'Whitespace between JSX elements',
          severity: 'low',
          matches: ['whitespace']
        });
      }
    });
  }

  scanDirectory(dirPath) {
    if (!fs.existsSync(dirPath)) {
      return;
    }

    const items = fs.readdirSync(dirPath);

    items.forEach(item => {
      const itemPath = path.join(dirPath, item);
      const stat = fs.statSync(itemPath);

      if (stat.isDirectory()) {
        this.scanDirectory(itemPath);
      } else if (stat.isFile()) {
        if (this.hasValidExtension(itemPath) && !this.shouldExcludeFile(itemPath)) {
          try {
            const content = fs.readFileSync(itemPath, 'utf8');
            this.analyzeFileContent(itemPath, content);
          } catch (error) {
            console.warn(`Warning: Could not read file ${itemPath}: ${error.message}`);
          }
        }
      }
    });
  }

  run() {
    console.log('🔍 Scanning for text node issues...');
    
    // Scan directories
    [config.srcDir, config.appDir, config.componentsDir].forEach(dir => {
      if (fs.existsSync(dir)) {
        console.log(`Scanning: ${dir}`);
        this.scanDirectory(dir);
      }
    });

    console.log(`\n📊 Found ${this.results.length} potential text node issues:`);

    if (this.results.length === 0) {
      console.log('🎉 No text node issues found!');
      return 0;
    }

    // Sort by severity
    const severityOrder = { high: 3, medium: 2, low: 1 };
    this.results.sort((a, b) => severityOrder[b.severity] - severityOrder[a.severity]);

    // Group by file
    const groupedResults = {};
    this.results.forEach(result => {
      if (!groupedResults[result.file]) {
        groupedResults[result.file] = [];
      }
      groupedResults[result.file].push(result);
    });

    Object.entries(groupedResults).forEach(([file, issues]) => {
      console.log(`\n📄 ${file}:`);
      issues.forEach(issue => {
        const severityIcon = issue.severity === 'high' ? '🔴' : issue.severity === 'medium' ? '🟡' : '🔵';
        console.log(`  ${severityIcon} Line ${issue.line}: ${issue.description}`);
        console.log(`      ${issue.content}`);
      });
    });

    console.log('\n💡 Priority fixes needed:');
    const highPriority = this.results.filter(r => r.severity === 'high');
    if (highPriority.length > 0) {
      console.log(`🔴 ${highPriority.length} high-priority issues found`);
      console.log('   Focus on: string literals, template literals, map operations, conditionals');
    }

    return this.results.length > 0 ? 1 : 0;
  }
}

// Run the finder
const finder = new TextNodeFinder();
const exitCode = finder.run();
process.exit(exitCode);
