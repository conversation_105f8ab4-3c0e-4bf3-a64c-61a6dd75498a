import React, { useState, useMemo, useCallback, useRef } from 'react';
import { StyleSheet, Animated } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { RTLView, RTLText, RTLTouchableOpacity, RTLIcon } from '../RTL';
import { useThemedStyles, useProducts, useI18n } from '../../hooks';
import { SPACING, FONT_SIZES, FONT_WEIGHTS, BORDER_RADIUS } from '../../constants/theme';
import { PREMIUM_GRADIENTS } from '../../constants/advancedColors';
import { SubtleGlow } from '../MoonlightEffects';
import { SmartImage } from '../media/SmartImage';
import type { ThemeColors } from '../../contexts/ThemeContext';
import type { Vendor } from '../../contexts/DataContext';

interface EnhancedVendorCardProps {
  vendor: Vendor;
  onPress: (vendorId: string) => void;
  onPreview?: (vendorId: string) => void;
  layout?: 'compact' | 'detailed' | 'featured';
  showLiveActivity?: boolean;
}

export const EnhancedVendorCard: React.FC<EnhancedVendorCardProps> = ({
  vendor,
  onPress,
  onPreview,
  layout = 'detailed',
  showLiveActivity = true,
}) => {
  const styles = useThemedStyles(createStyles);
  const { getProductsByVendor } = useProducts();
  const { t } = useI18n();
  
  const [isPressed, setIsPressed] = useState(false);
  const scaleAnim = useRef(new Animated.Value(1)).current;

  // Memoize expensive calculations
  const vendorData = useMemo(() => {
    const vendorProducts = getProductsByVendor(vendor.id);
    const activeProducts = vendorProducts.filter(p => p.isActive);
    const hasNewProducts = activeProducts.some(p => {
      // Simulate new products (created in last 7 days)
      return Math.random() > 0.7;
    });
    const hasActiveSales = activeProducts.some(p => p.salePrice && p.salePrice < p.price);

    return {
      vendorProducts,
      activeProducts,
      hasNewProducts,
      hasActiveSales
    };
  }, [getProductsByVendor, vendor.id]);

  const handlePressIn = useCallback(() => {
    setIsPressed(true);
    Animated.spring(scaleAnim, {
      toValue: 0.98,
      useNativeDriver: true,
      tension: 300,
      friction: 10,
    }).start();
  }, [scaleAnim]);

  const handlePressOut = useCallback(() => {
    setIsPressed(false);
    Animated.spring(scaleAnim, {
      toValue: 1,
      useNativeDriver: true,
      tension: 300,
      friction: 10,
    }).start();
  }, [scaleAnim]);

  const renderLiveActivityIndicators = useCallback(() => {
    if (!showLiveActivity) return null;

    const indicators = [];

    if (vendorData.hasNewProducts) {
      indicators.push(
        <RTLView key="new" style={styles.activityIndicator}>
          <RTLIcon name="add-circle" size={12} color="#10B981" />
          <RTLText style={styles.activityText}>{t('vendor.newProducts')}</RTLText>
        </RTLView>
      );
    }

    if (vendorData.hasActiveSales) {
      indicators.push(
        <RTLView key="sale" style={[styles.activityIndicator, styles.saleIndicator]}>
          <RTLIcon name="pricetag" size={12} color="#F59E0B" />
          <RTLText style={[styles.activityText, styles.saleText]}>{t('vendor.onSale')}</RTLText>
        </RTLView>
      );
    }

    // Simulate recent orders
    if (Math.random() > 0.6) {
      indicators.push(
        <RTLView key="orders" style={[styles.activityIndicator, styles.orderIndicator]}>
          <RTLIcon name="flash" size={12} color="#EF4444" />
          <RTLText style={[styles.activityText, styles.orderText]}>{t('vendor.hotOrders')}</RTLText>
        </RTLView>
      );
    }

    return indicators.length > 0 ? (
      <RTLView style={styles.activityContainer}>
        {indicators}
      </RTLView>
    ) : null;
  }, [showLiveActivity, vendorData.hasNewProducts, vendorData.hasActiveSales, styles, t]);

  const renderCompactLayout = () => (
    <RTLView style={styles.compactContent}>
      <RTLView style={styles.compactHeader}>
        <RTLView style={styles.compactLogo}>
          {vendor.logoUrl ? (
            <SmartImage
              source={{ uri: vendor.logoUrl }}
              style={styles.logoImage}
              aspectRatio={1}
              borderRadius={BORDER_RADIUS.sm}
              fallbackIcon="storefront"
              priority="normal"
            />
          ) : (
            <RTLIcon name="storefront" size={24} color="#3B82F6" />
          )}
        </RTLView>
        <RTLView style={styles.compactInfo}>
          <RTLText style={styles.compactName} numberOfLines={1}>
            {vendor.businessName}
          </RTLText>
          <RTLView style={styles.compactStats}>
            <RTLIcon name="star" size={12} color="#FFD700" />
            <RTLText style={styles.compactStatText}>{(vendor.rating || 0).toFixed(1)}</RTLText>
            <RTLText style={styles.compactStatText}>•</RTLText>
            <RTLText style={styles.compactStatText}>{vendorData.activeProducts.length}</RTLText>
          </RTLView>
        </RTLView>
      </RTLView>
      {renderLiveActivityIndicators()}
    </RTLView>
  );

  const renderDetailedLayout = () => (
    <RTLView style={styles.detailedContent}>
      <RTLView style={styles.vendorHeader}>
        <RTLView style={styles.vendorLogo}>
          {vendor.logoUrl ? (
            <SmartImage
              source={{ uri: vendor.logoUrl }}
              style={styles.logoImage}
              aspectRatio={1}
              borderRadius={BORDER_RADIUS.md}
              fallbackIcon="storefront"
              priority="normal"
            />
          ) : (
            <RTLIcon name="storefront" size={32} color="#3B82F6" />
          )}
        </RTLView>
        <RTLView style={styles.vendorInfo}>
          <RTLText style={styles.vendorName} numberOfLines={1}>
            {vendor.businessName}
          </RTLText>
          <RTLText style={styles.vendorDescription} numberOfLines={2}>
            {vendor.businessDescription || t('vendor.qualityProducts')}
          </RTLText>
        </RTLView>
      </RTLView>

      <RTLView style={styles.statsRow}>
        <RTLView style={styles.statItem}>
          <RTLIcon name="star" size={14} color="#FFD700" />
          <RTLText style={styles.statText}>{(vendor.rating || 0).toFixed(1)}</RTLText>
        </RTLView>
        <RTLView style={styles.statItem}>
          <RTLIcon name="cube-outline" size={14} color="#3B82F6" />
          <RTLText style={styles.statText}>{vendorData.activeProducts.length}</RTLText>
        </RTLView>
        <RTLView style={styles.statItem}>
          <RTLIcon name="location-outline" size={14} color="#6B7280" />
          <RTLText style={styles.statText}>{vendor.location || t('vendor.local')}</RTLText>
        </RTLView>
      </RTLView>

      {renderLiveActivityIndicators()}
    </RTLView>
  );

  const renderFeaturedLayout = () => (
    <RTLView style={styles.featuredContent}>
      <RTLView style={styles.featuredBadge}>
        <RTLIcon name="star" size={14} color="#FFD700" />
        <RTLText style={styles.featuredText}>{t('vendor.featured')}</RTLText>
      </RTLView>

      <RTLView style={styles.featuredHeader}>
        <RTLView style={styles.featuredLogo}>
          {vendor.logoUrl ? (
            <SmartImage
              source={{ uri: vendor.logoUrl }}
              style={styles.logoImage}
              aspectRatio={1}
              borderRadius={BORDER_RADIUS.lg}
              fallbackIcon="storefront"
              priority="high"
            />
          ) : (
            <RTLIcon name="storefront" size={40} color="#3B82F6" />
          )}
        </RTLView>
        <RTLView style={styles.featuredInfo}>
          <RTLText style={styles.featuredName} numberOfLines={1}>
            {vendor.businessName}
          </RTLText>
          <RTLText style={styles.featuredDescription} numberOfLines={3}>
            {vendor.businessDescription || t('vendor.premiumQuality')}
          </RTLText>
        </RTLView>
      </RTLView>

      <RTLView style={styles.featuredStats}>
        <RTLView style={styles.featuredStatItem}>
          <RTLIcon name="star" size={16} color="#FFD700" />
          <RTLText style={styles.featuredStatText}>{(vendor.rating || 0).toFixed(1)}</RTLText>
          <RTLText style={styles.featuredStatLabel}>{t('vendor.rating')}</RTLText>
        </RTLView>
        <RTLView style={styles.featuredStatItem}>
          <RTLIcon name="cube-outline" size={16} color="#3B82F6" />
          <RTLText style={styles.featuredStatText}>{vendorData.activeProducts.length}</RTLText>
          <RTLText style={styles.featuredStatLabel}>{t('vendor.products')}</RTLText>
        </RTLView>
      </RTLView>

      {renderLiveActivityIndicators()}

      <RTLView style={styles.featuredAction}>
        <LinearGradient
          colors={['#3B82F6', '#1D4ED8']}
          style={styles.featuredButton}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
        >
          <RTLText style={styles.featuredButtonText}>{t('vendor.visitShop')}</RTLText>
          <RTLIcon name="arrow-forward" size={16} color="#FFFFFF" />
        </LinearGradient>
      </RTLView>
    </RTLView>
  );

  const getCardHeight = () => {
    switch (layout) {
      case 'compact': return 80;
      case 'detailed': return 140;
      case 'featured': return 200;
      default: return 140;
    }
  };

  const getGradient = () => {
    switch (layout) {
      case 'featured': return PREMIUM_GRADIENTS.royalSpotlight;
      case 'compact': return PREMIUM_GRADIENTS.subtleElegance;
      default: return PREMIUM_GRADIENTS.elegantDepth;
    }
  };

  return (
    <Animated.View style={[{ transform: [{ scale: scaleAnim }] }]}>
      <RTLTouchableOpacity
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        onPress={() => onPress(vendor.id)}
        onLongPress={() => onPreview?.(vendor.id)}
        activeOpacity={0.9}
      >
        <SubtleGlow intensity={layout === 'featured' ? 0.8 : 0.4}>
          <LinearGradient
            colors={getGradient()}
            style={[styles.card, { height: getCardHeight() }]}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          >
            {layout === 'compact' && renderCompactLayout()}
            {layout === 'detailed' && renderDetailedLayout()}
            {layout === 'featured' && renderFeaturedLayout()}
          </LinearGradient>
        </SubtleGlow>
      </RTLTouchableOpacity>
    </Animated.View>
  );
};

const createStyles = (colors: ThemeColors) => StyleSheet.create({
  card: {
    borderRadius: BORDER_RADIUS.xl,
    marginBottom: SPACING.md,
    overflow: 'hidden',
  },
  logoImage: {
    width: '100%',
    height: '100%',
  },

  // Activity Indicators
  activityContainer: {
    flexDirection: 'row', // RTL handled by RTLView
    flexWrap: 'wrap',
    marginTop: SPACING.sm,
  },
  activityIndicator: {
    flexDirection: 'row', // RTL handled by RTLView
    alignItems: 'center',
    backgroundColor: 'rgba(16, 185, 129, 0.2)',
    paddingHorizontal: SPACING.sm,
    paddingVertical: SPACING.xs,
    borderRadius: BORDER_RADIUS.sm,
    marginEnd: SPACING.sm, // RTL-aware
    marginBottom: SPACING.xs,
    borderWidth: 1,
    borderColor: '#10B981',
  },
  saleIndicator: {
    backgroundColor: 'rgba(245, 158, 11, 0.2)',
    borderColor: '#F59E0B',
  },
  orderIndicator: {
    backgroundColor: 'rgba(239, 68, 68, 0.2)',
    borderColor: '#EF4444',
  },
  activityText: {
    fontSize: FONT_SIZES.xs,
    color: '#10B981',
    marginStart: SPACING.xs, // RTL-aware
    fontWeight: FONT_WEIGHTS.medium,
  },
  saleText: {
    color: '#F59E0B',
  },
  orderText: {
    color: '#EF4444',
  },

  // Compact Layout
  compactContent: {
    padding: SPACING.md,
    justifyContent: 'space-between',
    flex: 1,
  },
  compactHeader: {
    flexDirection: 'row', // RTL handled by RTLView
    alignItems: 'center',
    flex: 1,
  },
  compactLogo: {
    width: 40,
    height: 40,
    borderRadius: BORDER_RADIUS.md,
    backgroundColor: 'rgba(59, 130, 246, 0.1)',
    alignItems: 'center',
    justifyContent: 'center',
    marginEnd: SPACING.md, // RTL-aware
  },
  compactInfo: {
    flex: 1,
  },
  compactName: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.semiBold,
    color: '#FFFFFF',
    marginBottom: SPACING.xs,
  },
  compactStats: {
    flexDirection: 'row', // RTL handled by RTLView
    alignItems: 'center',
  },
  compactStatText: {
    fontSize: FONT_SIZES.xs,
    color: 'rgba(255, 255, 255, 0.8)',
    marginHorizontal: SPACING.xs,
  },

  // Detailed Layout
  detailedContent: {
    padding: SPACING.md,
  },
  vendorHeader: {
    flexDirection: 'row', // RTL handled by RTLView
    alignItems: 'flex-start',
    marginBottom: SPACING.md,
  },
  vendorLogo: {
    width: 48,
    height: 48,
    borderRadius: BORDER_RADIUS.lg,
    backgroundColor: 'rgba(59, 130, 246, 0.1)',
    alignItems: 'center',
    justifyContent: 'center',
    marginEnd: SPACING.md, // RTL-aware
  },
  vendorInfo: {
    flex: 1,
  },
  vendorName: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.bold,
    color: '#FFFFFF',
    marginBottom: SPACING.xs,
  },
  vendorDescription: {
    fontSize: FONT_SIZES.sm,
    color: 'rgba(255, 255, 255, 0.8)',
    lineHeight: 18,
  },
  statsRow: {
    flexDirection: 'row', // RTL handled by RTLView
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  statItem: {
    flexDirection: 'row', // RTL handled by RTLView
    alignItems: 'center',
    marginEnd: SPACING.lg, // RTL-aware
  },
  statText: {
    fontSize: FONT_SIZES.xs,
    color: 'rgba(255, 255, 255, 0.9)',
    marginStart: SPACING.xs, // RTL-aware
    fontWeight: FONT_WEIGHTS.medium,
  },

  // Featured Layout
  featuredContent: {
    padding: SPACING.lg,
  },
  featuredBadge: {
    position: 'absolute',
    top: SPACING.md,
    end: SPACING.md, // RTL-aware positioning
    flexDirection: 'row', // RTL handled by RTLView
    alignItems: 'center',
    backgroundColor: 'rgba(255, 215, 0, 0.2)',
    paddingHorizontal: SPACING.sm,
    paddingVertical: SPACING.xs,
    borderRadius: BORDER_RADIUS.md,
    borderWidth: 1,
    borderColor: '#FFD700',
  },
  featuredText: {
    fontSize: FONT_SIZES.xs,
    fontWeight: FONT_WEIGHTS.semiBold,
    color: '#FFD700',
    marginStart: SPACING.xs, // RTL-aware
  },
  featuredHeader: {
    flexDirection: 'row', // RTL handled by RTLView
    alignItems: 'flex-start',
    marginBottom: SPACING.md,
  },
  featuredLogo: {
    width: 56,
    height: 56,
    borderRadius: BORDER_RADIUS.xl,
    backgroundColor: 'rgba(59, 130, 246, 0.1)',
    alignItems: 'center',
    justifyContent: 'center',
    marginEnd: SPACING.md, // RTL-aware
  },
  featuredInfo: {
    flex: 1,
  },
  featuredName: {
    fontSize: FONT_SIZES.xl,
    fontWeight: FONT_WEIGHTS.bold,
    color: '#FFFFFF',
    marginBottom: SPACING.sm,
  },
  featuredDescription: {
    fontSize: FONT_SIZES.sm,
    color: 'rgba(255, 255, 255, 0.8)',
    lineHeight: 20,
  },
  featuredStats: {
    flexDirection: 'row', // RTL handled by RTLView
    justifyContent: 'space-around',
    marginBottom: SPACING.lg,
  },
  featuredStatItem: {
    alignItems: 'center',
  },
  featuredStatText: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.bold,
    color: '#FFFFFF',
    marginTop: SPACING.xs,
  },
  featuredStatLabel: {
    fontSize: FONT_SIZES.xs,
    color: 'rgba(255, 255, 255, 0.7)',
    marginTop: SPACING.xs,
  },
  featuredAction: {
    alignItems: 'center',
  },
  featuredButton: {
    flexDirection: 'row', // RTL handled by RTLView
    alignItems: 'center',
    paddingHorizontal: SPACING.xl,
    paddingVertical: SPACING.md,
    borderRadius: BORDER_RADIUS.lg,
  },
  featuredButtonText: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.semiBold,
    color: '#FFFFFF',
    marginEnd: SPACING.sm, // RTL-aware
  },
});
