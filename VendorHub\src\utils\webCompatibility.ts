// Web compatibility utilities for React Native components
import { Platform } from 'react-native';

// Check if we're running on web
export const isWeb = Platform.OS === 'web';

// Web-compatible animation configuration
export const getAnimationConfig = (config: any) => {
  if (isWeb) {
    // Disable useNativeDriver on web to avoid warnings
    return { ...config, useNativeDriver: false };
  }
  return config;
};

// Web-compatible PanResponder configuration
export const createWebCompatiblePanResponder = (config: any) => {
  if (!isWeb) {
    // Return original config for native platforms
    return config;
  }

  // For web, filter out unsupported properties and provide fallbacks
  const webConfig = { ...config };

  // Remove web-incompatible touch handlers
  const unsupportedHandlers = [
    'onStartShouldSetResponder',
    'onMoveShouldSetResponder',
    'onResponderTerminationRequest',
    'onResponderGrant',
    'onResponderMove',
    'onResponderRelease',
    'onResponderTerminate',
  ];

  unsupportedHandlers.forEach(handler => {
    if (webConfig[handler]) {
      // Convert to web-compatible mouse/touch events
      switch (handler) {
        case 'onResponderGrant':
          webConfig.onMouseDown = webConfig[handler];
          webConfig.onTouchStart = webConfig[handler];
          break;
        case 'onResponderMove':
          webConfig.onMouseMove = webConfig[handler];
          webConfig.onTouchMove = webConfig[handler];
          break;
        case 'onResponderRelease':
          webConfig.onMouseUp = webConfig[handler];
          webConfig.onTouchEnd = webConfig[handler];
          break;
      }
      delete webConfig[handler];
    }
  });

  return webConfig;
};

// Web-compatible style properties
export const createWebCompatibleStyle = (style: any) => {
  if (!isWeb || !style) {
    return style;
  }

  const webStyle = { ...style };

  // Fix CSS property naming issues
  if (webStyle['transform-origin']) {
    webStyle.transformOrigin = webStyle['transform-origin'];
    delete webStyle['transform-origin'];
  }

  // Convert other kebab-case properties to camelCase
  const kebabToCamelMap: { [key: string]: string } = {
    'background-color': 'backgroundColor',
    'border-radius': 'borderRadius',
    'border-width': 'borderWidth',
    'border-color': 'borderColor',
    'font-size': 'fontSize',
    'font-weight': 'fontWeight',
    'font-family': 'fontFamily',
    'line-height': 'lineHeight',
    'text-align': 'textAlign',
    'text-decoration': 'textDecoration',
    'margin-top': 'marginTop',
    'margin-bottom': 'marginBottom',
    'margin-left': 'marginLeft',
    'margin-right': 'marginRight',
    'padding-top': 'paddingTop',
    'padding-bottom': 'paddingBottom',
    'padding-left': 'paddingLeft',
    'padding-right': 'paddingRight',
  };

  Object.keys(kebabToCamelMap).forEach(kebabCase => {
    if (webStyle[kebabCase]) {
      webStyle[kebabToCamelMap[kebabCase]] = webStyle[kebabCase];
      delete webStyle[kebabCase];
    }
  });

  return webStyle;
};

// Web-compatible gesture handler props
export const createWebCompatibleGestureProps = (props: any) => {
  if (!isWeb) {
    return props;
  }

  const webProps = { ...props };

  // Remove gesture handler specific props that don't work on web
  const gestureProps = [
    'onSwipeableOpen',
    'onSwipeableClose',
    'leftThreshold',
    'rightThreshold',
    'friction',
    'overshootLeft',
    'overshootRight',
  ];

  gestureProps.forEach(prop => {
    if (webProps[prop]) {
      // For web, we'll handle these with CSS transitions and mouse events
      delete webProps[prop];
    }
  });

  return webProps;
};

// Platform-specific component wrapper
export const withWebCompatibility = <T extends object>(
  Component: React.ComponentType<T>,
  webAlternative?: React.ComponentType<T>
) => {
  return (props: T) => {
    if (isWeb && webAlternative) {
      return React.createElement(webAlternative, props);
    }
    return React.createElement(Component, props);
  };
};

// Haptic feedback fallback for web
export const webCompatibleHaptics = {
  impactAsync: (style?: any) => {
    if (isWeb) {
      // For web, we can use the Vibration API if available
      if ('vibrate' in navigator) {
        const duration = style?.intensity === 'heavy' ? 100 : style?.intensity === 'medium' ? 50 : 25;
        navigator.vibrate(duration);
      }
      return Promise.resolve();
    }
    // For native, use the actual Haptics
    const Haptics = require('expo-haptics');
    return Haptics.impactAsync(style);
  },
  
  notificationAsync: (type?: any) => {
    if (isWeb) {
      if ('vibrate' in navigator) {
        const pattern = type === 'success' ? [100, 50, 100] : type === 'error' ? [200] : [50];
        navigator.vibrate(pattern);
      }
      return Promise.resolve();
    }
    const Haptics = require('expo-haptics');
    return Haptics.notificationAsync(type);
  },
};

// Export React for the withWebCompatibility function
import React from 'react';
export { React };
