#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Configuration
const config = {
  srcDir: path.join(__dirname, '../src'),
  outputFile: path.join(__dirname, '../TYPE_SYNTAX_FIXES_REPORT.md'),
  excludePatterns: [
    /node_modules/,
    /\.git/,
    /\.expo/,
    /dist/,
    /build/,
    /coverage/,
    /\.DS_Store/,
    /\.env/,
    /package-lock\.json/,
    /yarn\.lock/,
    /\.log$/,
    /\.md$/,
    /\.json$/,
    /\.js\.map$/,
    /\.d\.ts$/,
    /scripts/,
    /assets/,
  ],
  fileExtensions: ['.tsx', '.ts', '.jsx', '.js'],
  
  // Type syntax fixes
  typeFixes: [
    {
      name: 'incorrectTypeBraces',
      pattern: /type\s+\{\s*([^}]+)\s*\}/g,
      replacement: "type $1",
      description: 'Fixed incorrect type { } syntax'
    },
    {
      name: 'duplicateTypeKeyword',
      pattern: /import\s+\{\s*type\s+\{\s*([^}]+)\s*\}\s*,/g,
      replacement: "import { type $1,",
      description: 'Fixed duplicate type keyword in import'
    },
    {
      name: 'typeInImportBraces',
      pattern: /import\s+\{\s*([^}]*),\s*type\s+\{\s*([^}]+)\s*\}\s*\}/g,
      replacement: "import { $1, type $2 }",
      description: 'Fixed type syntax in import statement'
    },
    {
      name: 'multipleTypeKeywords',
      pattern: /type\s+type\s+([A-Za-z_][A-Za-z0-9_]*)/g,
      replacement: "type $1",
      description: 'Fixed duplicate type keywords'
    }
  ]
};

class TypeSyntaxFixer {
  constructor() {
    this.results = {
      filesProcessed: 0,
      filesFixed: 0,
      totalFixes: 0,
      fixesByType: {},
      fixedFiles: []
    };
  }

  scanDirectory(dirPath) {
    const items = fs.readdirSync(dirPath);
    
    for (const item of items) {
      const fullPath = path.join(dirPath, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        // Skip excluded directories
        if (config.excludePatterns.some(pattern => pattern.test(fullPath))) {
          continue;
        }
        this.scanDirectory(fullPath);
      } else if (stat.isFile()) {
        // Check if file should be processed
        if (this.shouldProcessFile(fullPath)) {
          this.processFile(fullPath);
        }
      }
    }
  }

  shouldProcessFile(filePath) {
    // Check file extension
    const ext = path.extname(filePath);
    if (!config.fileExtensions.includes(ext)) {
      return false;
    }
    
    // Check exclude patterns
    if (config.excludePatterns.some(pattern => pattern.test(filePath))) {
      return false;
    }
    
    return true;
  }

  processFile(filePath) {
    this.results.filesProcessed++;
    
    try {
      let content = fs.readFileSync(filePath, 'utf8');
      let originalContent = content;
      let fileFixed = false;
      let fileFixes = [];

      // Apply each fix pattern
      config.typeFixes.forEach(fix => {
        const matches = (content.match(fix.pattern) || []).length;
        
        if (matches > 0) {
          content = content.replace(fix.pattern, fix.replacement);
          fileFixed = true;
          fileFixes.push({
            type: fix.name,
            count: matches,
            description: fix.description
          });
          
          if (!this.results.fixesByType[fix.name]) {
            this.results.fixesByType[fix.name] = 0;
          }
          this.results.fixesByType[fix.name] += matches;
          this.results.totalFixes += matches;
        }
      });

      // Write file if changes were made
      if (fileFixed && content !== originalContent) {
        fs.writeFileSync(filePath, content, 'utf8');
        this.results.filesFixed++;
        this.results.fixedFiles.push({
          path: path.relative(process.cwd(), filePath),
          fixes: fileFixes,
          totalFixes: fileFixes.reduce((sum, fix) => sum + fix.count, 0)
        });
        
        console.log(`✅ Fixed: ${path.relative(process.cwd(), filePath)} (${fileFixes.reduce((sum, fix) => sum + fix.count, 0)} fixes)`);
      }
    } catch (error) {
      console.error(`❌ Error processing ${filePath}:`, error.message);
    }
  }

  generateReport() {
    const report = [
      '# Type Syntax Fixes Report',
      '',
      `**Generated:** ${new Date().toLocaleString()}`,
      `**Files Processed:** ${this.results.filesProcessed}`,
      `**Files Fixed:** ${this.results.filesFixed}`,
      `**Total Fixes Applied:** ${this.results.totalFixes}`,
      '',
      '## 📊 Fixes Summary',
      '',
      '| Fix Type | Count | Description |',
      '|----------|-------|-------------|'
    ];

    // Add fixes summary
    Object.entries(this.results.fixesByType).forEach(([type, count]) => {
      const fix = config.typeFixes.find(f => f.name === type);
      const description = fix ? fix.description : type;
      report.push(`| ${type} | ${count} | ${description} |`);
    });

    if (this.results.fixedFiles.length > 0) {
      report.push('', '## 📁 Fixed Files', '');
      
      this.results.fixedFiles.forEach(file => {
        report.push(`### ${file.path}`, '');
        report.push(`**Total fixes:** ${file.totalFixes}`, '');
        
        file.fixes.forEach(fix => {
          report.push(`- **${fix.type}** (${fix.count} occurrences): ${fix.description}`);
        });
        
        report.push('');
      });
    }

    // Add correct syntax examples
    report.push(
      '## 📋 Correct Type Syntax',
      '',
      '### Import Statements',
      '',
      '```typescript',
      '// ✅ Correct - type keyword before each type',
      "import { Component, type UserRole, type ProductCategory } from './types';",
      '',
      '// ❌ Wrong - type with braces',
      "import { Component, type { UserRole, ProductCategory } } from './types';",
      '```',
      '',
      '### Type Declarations',
      '',
      '```typescript',
      '// ✅ Correct - simple type declaration',
      'type UserRole = "admin" | "vendor" | "customer";',
      '',
      '// ❌ Wrong - type with braces',
      'type { UserRole } = "admin" | "vendor" | "customer";',
      '```',
      '',
      '### Interface Imports',
      '',
      '```typescript',
      '// ✅ Correct - type keyword for type-only imports',
      "import { type User, type Product } from './interfaces';",
      '',
      '// ✅ Correct - mixed imports',
      "import { Component, type User, type Product } from './module';",
      '```'
    );

    return report.join('\n');
  }

  run() {
    console.log('🔍 Scanning for type syntax errors...');
    
    // Scan source directory
    if (fs.existsSync(config.srcDir)) {
      this.scanDirectory(config.srcDir);
    }

    // Generate and save report
    const report = this.generateReport();
    fs.writeFileSync(config.outputFile, report, 'utf8');
    
    console.log('\n📊 Summary:');
    console.log(`Files processed: ${this.results.filesProcessed}`);
    console.log(`Files fixed: ${this.results.filesFixed}`);
    console.log(`Total fixes: ${this.results.totalFixes}`);
    console.log(`Report saved: ${config.outputFile}`);
    
    if (this.results.totalFixes > 0) {
      console.log('\n✅ Type syntax errors have been fixed!');
    } else {
      console.log('\n✅ No type syntax errors found.');
    }
  }
}

// Run the fixer
const fixer = new TypeSyntaxFixer();
fixer.run();
