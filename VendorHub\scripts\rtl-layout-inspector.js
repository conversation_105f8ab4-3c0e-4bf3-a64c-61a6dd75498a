#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Configuration
const config = {
  srcDir: path.join(__dirname, '../src'),
  appDir: path.join(__dirname, '../app'),
  componentsDir: path.join(__dirname, '../components'),
  outputFile: path.join(__dirname, '../RTL_LAYOUT_INSPECTION_REPORT.md'),
  fileExtensions: ['.tsx', '.ts', '.jsx', '.js'],
  
  // Patterns that might need RTL attention
  layoutPatterns: {
    flexDirection: /flexDirection\s*:\s*['"]row['"]|flexDirection\s*:\s*['"]row-reverse['"]/g,
    textAlign: /textAlign\s*:\s*['"]left['"]|textAlign\s*:\s*['"]right['"]/g,
    marginLeft: /marginLeft\s*:\s*\d+|marginStart\s*:\s*\d+/g,
    marginRight: /marginRight\s*:\s*\d+|marginEnd\s*:\s*\d+/g,
    paddingLeft: /paddingLeft\s*:\s*\d+|paddingStart\s*:\s*\d+/g,
    paddingRight: /paddingRight\s*:\s*\d+|paddingEnd\s*:\s*\d+/g,
    left: /left\s*:\s*\d+/g,
    right: /right\s*:\s*\d+/g,
    borderLeftWidth: /borderLeftWidth\s*:\s*\d+|borderStartWidth\s*:\s*\d+/g,
    borderRightWidth: /borderRightWidth\s*:\s*\d+|borderEndWidth\s*:\s*\d+/g,
    justifyContent: /justifyContent\s*:\s*['"]flex-start['"]|justifyContent\s*:\s*['"]flex-end['"]/g,
    alignItems: /alignItems\s*:\s*['"]flex-start['"]|alignItems\s*:\s*['"]flex-end['"]/g,
    transform: /transform\s*:\s*\[\s*\{\s*translateX\s*:\s*[^}]+\}\s*\]/g,
    scrollDirection: /horizontal\s*=\s*\{?true\}?/g,
    iconDirection: /name\s*=\s*['"]arrow-|name\s*=\s*['"]chevron-|name\s*=\s*['"]caret-/g
  },
  
  // RTL-aware properties that should be used instead
  rtlRecommendations: {
    marginLeft: 'marginStart',
    marginRight: 'marginEnd',
    paddingLeft: 'paddingStart',
    paddingRight: 'paddingEnd',
    borderLeftWidth: 'borderStartWidth',
    borderRightWidth: 'borderEndWidth',
    left: 'start',
    right: 'end',
    textAlign: 'I18nManager.isRTL ? "right" : "left"'
  }
};

class RTLLayoutInspector {
  constructor() {
    this.results = {
      filesWithIssues: [],
      totalFilesScanned: 0,
      totalIssues: 0,
      issuesByType: {},
      recommendations: []
    };
  }

  analyzeFile(filePath, content) {
    const relativePath = path.relative(process.cwd(), filePath);
    const lines = content.split('\n');
    
    const fileIssues = {
      path: relativePath,
      issues: [],
      recommendations: []
    };

    // Check each pattern
    Object.entries(config.layoutPatterns).forEach(([patternName, regex]) => {
      let match;
      while ((match = regex.exec(content)) !== null) {
        const lineNumber = content.substring(0, match.index).split('\n').length;
        const lineContent = lines[lineNumber - 1]?.trim() || '';
        
        const issue = {
          type: patternName,
          line: lineNumber,
          content: lineContent,
          match: match[0],
          severity: this.getIssueSeverity(patternName),
          recommendation: this.getRecommendation(patternName, match[0])
        };
        
        fileIssues.issues.push(issue);
        
        // Update global counters
        if (!this.results.issuesByType[patternName]) {
          this.results.issuesByType[patternName] = 0;
        }
        this.results.issuesByType[patternName]++;
        this.results.totalIssues++;
      }
    });

    // Check for horizontal scrolling components
    this.checkHorizontalScrolling(content, lines, fileIssues);
    
    // Check for icon directionality
    this.checkIconDirectionality(content, lines, fileIssues);
    
    // Check for flex layouts
    this.checkFlexLayouts(content, lines, fileIssues);

    if (fileIssues.issues.length > 0) {
      this.results.filesWithIssues.push(fileIssues);
    }
  }

  checkHorizontalScrolling(content, lines, fileIssues) {
    const horizontalScrollRegex = /horizontal\s*=?\s*\{?true\}?/g;
    let match;
    
    while ((match = horizontalScrollRegex.exec(content)) !== null) {
      const lineNumber = content.substring(0, match.index).split('\n').length;
      const lineContent = lines[lineNumber - 1]?.trim() || '';
      
      // Check if this is in a ScrollView or FlatList
      const contextLines = lines.slice(Math.max(0, lineNumber - 3), lineNumber + 2);
      const context = contextLines.join('\n');
      
      if (/RTLScrollView|RTLFlatList|ScrollView|FlatList/.test(context)) {
        fileIssues.issues.push({
          type: 'horizontalScrolling',
          line: lineNumber,
          content: lineContent,
          match: match[0],
          severity: 'medium',
          recommendation: 'Ensure horizontal scrolling starts from right in RTL mode. Consider using inverted prop or custom RTL logic.'
        });
      }
    }
  }

  checkIconDirectionality(content, lines, fileIssues) {
    const directionalIconRegex = /name\s*=\s*['"](?:arrow-|chevron-|caret-|play-|skip-|fast-)(forward|back|left|right|next|previous)['"]|name\s*=\s*['"](?:arrow|chevron|caret)-(left|right|up|down)['"]/g;
    let match;
    
    while ((match = directionalIconRegex.exec(content)) !== null) {
      const lineNumber = content.substring(0, match.index).split('\n').length;
      const lineContent = lines[lineNumber - 1]?.trim() || '';
      
      fileIssues.issues.push({
        type: 'directionalIcon',
        line: lineNumber,
        content: lineContent,
        match: match[0],
        severity: 'high',
        recommendation: 'Use RTLIcon component which automatically handles icon mirroring for RTL layouts.'
      });
    }
  }

  checkFlexLayouts(content, lines, fileIssues) {
    const flexRowRegex = /flexDirection\s*:\s*['"]row['"]/g;
    let match;
    
    while ((match = flexRowRegex.exec(content)) !== null) {
      const lineNumber = content.substring(0, match.index).split('\n').length;
      const lineContent = lines[lineNumber - 1]?.trim() || '';
      
      fileIssues.issues.push({
        type: 'flexRow',
        line: lineNumber,
        content: lineContent,
        match: match[0],
        severity: 'medium',
        recommendation: 'Consider using I18nManager.isRTL to conditionally set flexDirection: "row-reverse" for RTL layouts.'
      });
    }
  }

  getIssueSeverity(patternName) {
    const highSeverity = ['textAlign', 'iconDirection', 'directionalIcon'];
    const mediumSeverity = ['flexDirection', 'justifyContent', 'horizontalScrolling', 'flexRow'];
    
    if (highSeverity.includes(patternName)) return 'high';
    if (mediumSeverity.includes(patternName)) return 'medium';
    return 'low';
  }

  getRecommendation(patternName, match) {
    const recommendations = {
      marginLeft: 'Use marginStart instead of marginLeft for RTL compatibility',
      marginRight: 'Use marginEnd instead of marginRight for RTL compatibility',
      paddingLeft: 'Use paddingStart instead of paddingLeft for RTL compatibility',
      paddingRight: 'Use paddingEnd instead of paddingRight for RTL compatibility',
      borderLeftWidth: 'Use borderStartWidth instead of borderLeftWidth for RTL compatibility',
      borderRightWidth: 'Use borderEndWidth instead of borderRightWidth for RTL compatibility',
      left: 'Use start instead of left for RTL compatibility',
      right: 'Use end instead of right for RTL compatibility',
      textAlign: 'Use conditional text alignment based on I18nManager.isRTL',
      flexDirection: 'Consider RTL-aware flex direction using I18nManager.isRTL',
      justifyContent: 'Consider RTL-aware justification using I18nManager.isRTL',
      transform: 'Ensure transform values are RTL-aware',
      scrollDirection: 'Ensure horizontal scrolling behavior is RTL-aware',
      iconDirection: 'Use RTLIcon component for automatic icon mirroring'
    };
    
    return recommendations[patternName] || 'Review for RTL compatibility';
  }

  scanDirectory(dirPath) {
    if (!fs.existsSync(dirPath)) {
      console.log(`Directory not found: ${dirPath}`);
      return;
    }

    const items = fs.readdirSync(dirPath);
    
    for (const item of items) {
      const fullPath = path.join(dirPath, item);
      
      // Skip certain directories and files
      if (/node_modules|\.git|\.expo|dist|build|\.DS_Store/.test(item)) {
        continue;
      }
      
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        this.scanDirectory(fullPath);
      } else if (stat.isFile() && config.fileExtensions.some(ext => fullPath.endsWith(ext))) {
        try {
          const content = fs.readFileSync(fullPath, 'utf8');
          this.analyzeFile(fullPath, content);
          this.results.totalFilesScanned++;
        } catch (error) {
          console.error(`Error reading file ${fullPath}:`, error.message);
        }
      }
    }
  }

  generateReport() {
    const timestamp = new Date().toLocaleString();
    
    let report = `# RTL Layout Inspection Report\n\n`;
    report += `**Generated:** ${timestamp}\n`;
    report += `**Files Scanned:** ${this.results.totalFilesScanned}\n`;
    report += `**Files with Issues:** ${this.results.filesWithIssues.length}\n`;
    report += `**Total Issues:** ${this.results.totalIssues}\n\n`;
    
    if (this.results.totalIssues === 0) {
      report += `## 🎉 Excellent! No RTL Layout Issues Found\n\n`;
      report += `All scanned files appear to be using RTL-compatible layout patterns.\n\n`;
      return report;
    }
    
    // Issues summary
    report += `## 📊 Issues Summary\n\n`;
    report += `| Issue Type | Count | Severity |\n`;
    report += `|------------|-------|----------|\n`;
    
    Object.entries(this.results.issuesByType).forEach(([type, count]) => {
      const severity = this.getIssueSeverity(type);
      const emoji = severity === 'high' ? '🔴' : severity === 'medium' ? '🟡' : '🟢';
      report += `| ${type} | ${count} | ${emoji} ${severity} |\n`;
    });
    report += `\n`;
    
    // Detailed file analysis
    if (this.results.filesWithIssues.length > 0) {
      report += `## 📁 Files with RTL Layout Issues\n\n`;
      
      this.results.filesWithIssues.forEach(file => {
        report += `### ${file.path}\n\n`;
        report += `**Issues found:** ${file.issues.length}\n\n`;
        
        // Group issues by severity
        const highIssues = file.issues.filter(i => i.severity === 'high');
        const mediumIssues = file.issues.filter(i => i.severity === 'medium');
        const lowIssues = file.issues.filter(i => i.severity === 'low');
        
        if (highIssues.length > 0) {
          report += `#### 🔴 High Priority Issues\n\n`;
          highIssues.forEach(issue => {
            report += `- **Line ${issue.line}:** \`${issue.content}\`\n`;
            report += `  - **Issue:** ${issue.match}\n`;
            report += `  - **Recommendation:** ${issue.recommendation}\n\n`;
          });
        }
        
        if (mediumIssues.length > 0) {
          report += `#### 🟡 Medium Priority Issues\n\n`;
          mediumIssues.forEach(issue => {
            report += `- **Line ${issue.line}:** \`${issue.content}\`\n`;
            report += `  - **Issue:** ${issue.match}\n`;
            report += `  - **Recommendation:** ${issue.recommendation}\n\n`;
          });
        }
        
        if (lowIssues.length > 0) {
          report += `#### 🟢 Low Priority Issues\n\n`;
          lowIssues.forEach(issue => {
            report += `- **Line ${issue.line}:** \`${issue.content}\`\n`;
            report += `  - **Issue:** ${issue.match}\n`;
            report += `  - **Recommendation:** ${issue.recommendation}\n\n`;
          });
        }
      });
    }
    
    // General recommendations
    report += `## 💡 General RTL Layout Recommendations\n\n`;
    report += `1. **Use Start/End instead of Left/Right:** Replace marginLeft/Right with marginStart/End\n`;
    report += `2. **Conditional Layouts:** Use I18nManager.isRTL for conditional styling\n`;
    report += `3. **Icon Mirroring:** Use RTLIcon component for automatic directional icon handling\n`;
    report += `4. **Horizontal Scrolling:** Ensure horizontal lists start from right in RTL mode\n`;
    report += `5. **Text Alignment:** Use conditional text alignment based on language direction\n`;
    report += `6. **Flex Layouts:** Consider row-reverse for RTL in horizontal flex containers\n\n`;
    
    return report;
  }

  run() {
    console.log('🔍 Scanning for RTL layout issues...');
    
    // Scan directories
    [config.srcDir, config.appDir, config.componentsDir].forEach(dir => {
      if (fs.existsSync(dir)) {
        console.log(`Scanning: ${dir}`);
        this.scanDirectory(dir);
      }
    });
    
    // Generate and save report
    const report = this.generateReport();
    fs.writeFileSync(config.outputFile, report);
    
    console.log(`\n📄 Report saved to: ${config.outputFile}`);
    console.log(`📊 Files scanned: ${this.results.totalFilesScanned}`);
    console.log(`📁 Files with issues: ${this.results.filesWithIssues.length}`);
    console.log(`⚠️  Total issues: ${this.results.totalIssues}`);
    
    if (this.results.totalIssues === 0) {
      console.log('\n🎉 No RTL layout issues found!');
    } else {
      console.log('\n🔧 Issues found that may need attention for proper RTL support.');
    }
    
    return this.results.totalIssues === 0;
  }
}

// Run the inspector
if (require.main === module) {
  const inspector = new RTLLayoutInspector();
  const isClean = inspector.run();
  process.exit(isClean ? 0 : 1);
}

module.exports = RTLLayoutInspector;
