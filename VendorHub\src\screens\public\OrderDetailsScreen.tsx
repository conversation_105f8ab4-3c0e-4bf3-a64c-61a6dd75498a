import React, { useState } from 'react';
import {
  StyleSheet,
  Alert,
  RefreshControl } from 'react-native';
import { useThemedStyles, useAuth, useOrders, useVendors, useProducts, useI18n } from '../../hooks';
import { Card, Button, EmptyState, StatusBadge } from '../../components';
import { RTLView, RTLText, RTLIcon, RTLSafeAreaView, RTLScrollView, RTLTouchableOpacity } from '../../components/RTL';
import {
  SPACING,
  FONT_SIZES,
  FONT_WEIGHTS,
  BORDER_RADIUS,
  ICON_SIZES } from '../../constants/theme';
import { formatCurrency } from '../../utils';
import type ThemeColors  from '../../contexts/ThemeContext';

interface OrderDetailsScreenProps {
  navigation: any;
  route: {
    params: {
      orderId: string;
    };
  };
}

export const OrderDetailsScreen: React.FC<OrderDetailsScreenProps> = ({ navigation, route }) => {
  const { orderId } = route.params;
  const styles = useThemedStyles(createStyles);
  const { user } = useAuth();
  const { getOrderById } = useOrders();
  const { getVendorById } = useVendors();
  const { getProductById } = useProducts();
  const { t } = useI18n();
  
  const [refreshing, setRefreshing] = useState(false);

  const order = getOrderById(orderId);

  const onRefresh = React.useCallback(async () => {
    setRefreshing(true);
    // Simulate refresh delay
    setTimeout(() => setRefreshing(false), 1000);
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return '#FF9800';
      case 'confirmed': return '#2196F3';
      case 'shipped': return '#9C27B0';
      case 'delivered': return '#4CAF50';
      case 'cancelled': return '#F44336';
      default: return '#757575';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending': return 'time-outline';
      case 'confirmed': return 'checkmark-circle-outline';
      case 'shipped': return 'car-outline';
      case 'delivered': return 'checkmark-done-outline';
      case 'cancelled': return 'close-circle-outline';
      default: return 'help-circle-outline';
    }
  };

  const getOrderTimeline = (status: string) => {
    const steps = [
      { key: 'pending', label: t('orders.orderPlaced'), icon: 'receipt-outline' },
      { key: 'confirmed', label: t('orders.confirmed'), icon: 'checkmark-circle-outline' },
      { key: 'shipped', label: t('orders.shipped'), icon: 'car-outline' },
      { key: 'delivered', label: t('orders.delivered'), icon: 'checkmark-done-outline' },
    ];

    const statusOrder = ['pending', 'confirmed', 'shipped', 'delivered'];
    const currentIndex = statusOrder.indexOf(status);

    return steps.map((step, index) => ({
      ...step,
      completed: index <= currentIndex,
      active: index === currentIndex,
    }));
  };

  const handleProductPress = (productId: string) => {
    navigation.navigate('ProductDetails', { productId });
  };

  const handleVendorPress = (vendorId: string) => {
    navigation.navigate('VendorShop', { vendorId });
  };

  const handleReorder = () => {
    Alert.alert(
      t('orders.reorderItems'),
      t('orders.addAllItemsToCart'),
      [
        { text: t('common.cancel'), style: 'cancel' },
        { text: t('cart.addToCart'), onPress: () => {
          // Implementation would add items to cart
          Alert.alert(t('common.success'), t('orders.itemsAddedToCart'));
        }},
      ]
    );
  };

  if (!order) {
    return (
      <RTLSafeAreaView style={styles.container}>
        <EmptyState
          icon="receipt-outline"
          title={t('orders.orderNotFound')}
          description={t('orders.orderNotFoundDescription')}
          actionLabel={t('common.goBack')}
          onAction={() => navigation.goBack()}
        />
      </RTLSafeAreaView>
    );
  }

  const timeline = getOrderTimeline(order.status);

  return (
    <RTLSafeAreaView style={styles.container}>
      <RTLScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* Order Header */}
        <Card style={styles.headerCard} variant="elevated">
          <RTLView style={styles.orderHeader}>
            <RTLView style={styles.orderInfo}>
              <RTLText style={styles.orderId}>Order #{order.id.slice(-6)}</RTLText>
              <RTLText style={styles.orderDate}>
                {t('orders.placedOn')} {new Date(order.createdAt).toLocaleDateString('en-US', {
                  weekday: 'long',
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric',
                })}
              </RTLText>
            </RTLView>
            <StatusBadge
              status={order.status}
              customColor={getStatusColor(order.status)}

            />
          </RTLView>

          <RTLView style={styles.orderSummary}>
            <RTLText style={styles.totalAmount}>{formatCurrency(order.totalAmount)}</RTLText>
            <RTLText style={styles.itemCount}>
              {order.items.length} item{order.items.length !== 1 ? 's' : ''}
            </RTLText>
          </RTLView>
        </Card>

        {/* Order Timeline */}
        {order.status !== 'cancelled' && (
          <Card style={styles.timelineCard} variant="elevated">
            <RTLText style={styles.sectionTitle}>{t('orders.orderStatus')}</RTLText>
            <RTLView style={styles.timeline}>
              {timeline.map((step, index) => (
                <RTLView key={step.key} style={styles.timelineStep}>
                  <RTLView style={styles.timelineLeft}>
                    <RTLView style={[
                      styles.timelineIcon,
                      step.completed && styles.timelineIconCompleted,
                      step.active && styles.timelineIconActive,
                    ].filter(Boolean) as any}>
                      <RTLIcon
                        name={step.icon as any}
                        size={20}
                        color={step.completed ? '#FFFFFF' : '#CCCCCC'}
                      />
                    </RTLView>
                    {index < timeline.length - 1 && (
                      <RTLView style={[
                        styles.timelineLine,
                        step.completed && styles.timelineLineCompleted,
                      ].filter(Boolean) as any} />
                    )}
                  </RTLView>
                  <RTLView style={styles.timelineRight}>
                    <RTLText style={[
                      styles.timelineLabel,
                      step.completed && styles.timelineLabelCompleted,
                    ].filter(Boolean) as any}>
                      {step.label}
                    </RTLText>
                  </RTLView>
                </RTLView>
              ))}
            </RTLView>
          </Card>
        )}

        {/* Order Items */}
        <Card style={styles.itemsCard} variant="elevated">
          <RTLText style={styles.sectionTitle}>{t('orders.orderItems')}</RTLText>
          <RTLView style={styles.itemsList}>
            {order.items.map((item, index) => {
              const product = getProductById(item.productId);
              const vendor = getVendorById(item.vendorId);

              return (
                <RTLView key={index} style={styles.orderItem}>
                  <RTLTouchableOpacity
                    style={styles.itemContent}
                    onPress={() => handleProductPress(item.productId)}
                  >
                    <RTLView style={styles.itemImage}>
                      <RTLIcon name="image-outline" size={32} color="#CCCCCC" />
                    </RTLView>
                    <RTLView style={styles.itemDetails}>
                      <RTLText style={styles.itemName} numberOfLines={2}>
                        {item.productName}
                      </RTLText>
                      {vendor && (
                        <RTLTouchableOpacity onPress={() => handleVendorPress(vendor.id)}>
                          <RTLText style={styles.vendorName}>by {vendor.businessName}</RTLText>
                        </RTLTouchableOpacity>
                      )}
                      <RTLView style={styles.itemPricing}>
                        <RTLText style={styles.itemPrice}>
                          {formatCurrency(item.price)} × {item.quantity}
                        </RTLText>
                        <RTLText style={styles.itemTotal}>
                          {formatCurrency(item.totalPrice)}
                        </RTLText>
                      </RTLView>
                    </RTLView>
                  </RTLTouchableOpacity>
                </RTLView>
              );
            })}
          </RTLView>
        </Card>

        {/* Shipping Information */}
        <Card style={styles.shippingCard} variant="elevated">
          <RTLText style={styles.sectionTitle}>{t('orders.shippingInformation')}</RTLText>
          <RTLView style={styles.shippingDetails}>
            <RTLView style={styles.addressSection}>
              <RTLText style={styles.addressLabel}>{t('orders.deliveryAddress')}</RTLText>
              <RTLText style={styles.addressText}>
                {order.shippingAddress.street}
              </RTLText>
              <RTLText style={styles.addressText}>
                {order.shippingAddress.city}, {order.shippingAddress.state} {order.shippingAddress.zipCode}
              </RTLText>
              <RTLText style={styles.addressText}>
                {order.shippingAddress.country}
              </RTLText>
            </RTLView>
          </RTLView>
        </Card>

        {/* Order Actions */}
        <RTLView style={styles.actionsSection}>
          <Button
            title={t('orders.reorderItems')}
            onPress={handleReorder}
            variant="outline"
            style={styles.actionButton}
            leftIcon={<RTLIcon name="refresh-outline" size={20} color="#667eea" />}
          />

          {order.status === 'delivered' && (
            <Button
              title={t('orders.leaveReview')}
              onPress={() => Alert.alert(t('common.featureComingSoon'), t('orders.reviewFunctionalityComingSoon'))}
              style={styles.actionButton}
              leftIcon={<RTLIcon name="star-outline" size={20} color="#FFFFFF" />}
            />
          )}
        </RTLView>
      </RTLScrollView>
    </RTLSafeAreaView>
  );
};

const createStyles = (colors: ThemeColors) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  scrollView: {
    flex: 1,
  },
  headerCard: {
    margin: SPACING.lg,
    padding: SPACING.lg,
  },
  orderHeader: {
    flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: SPACING.lg,
  },
  orderInfo: {
    flex: 1,
  },
  orderId: {
    fontSize: FONT_SIZES.xl,
    fontWeight: FONT_WEIGHTS.bold,
    color: colors.text,
    marginBottom: SPACING.xs,
  },
  orderDate: {
    fontSize: FONT_SIZES.md,
    color: colors.textSecondary,
  },
  orderSummary: {
    alignItems: 'center',
    paddingTop: SPACING.lg,
    borderTopWidth: 1,
    borderTopColor: colors.border,
  },
  totalAmount: {
    fontSize: FONT_SIZES.xxl,
    fontWeight: FONT_WEIGHTS.bold,
    color: '#4CAF50',
    marginBottom: SPACING.xs,
  },
  itemCount: {
    fontSize: FONT_SIZES.md,
    color: colors.textSecondary,
  },
  timelineCard: {
    marginHorizontal: SPACING.lg,
    marginBottom: SPACING.lg,
    padding: SPACING.lg,
  },
  sectionTitle: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.semiBold,
    color: colors.text,
    marginBottom: SPACING.lg,
  },
  timeline: {
    paddingLeft: SPACING.sm,
  },
  timelineStep: {
    flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
    alignItems: 'flex-start',
  },
  timelineLeft: {
    alignItems: 'center',
    marginRight: SPACING.lg,
  },
  timelineIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.surface,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#CCCCCC',
  },
  timelineIconCompleted: {
    backgroundColor: '#4CAF50',
    borderColor: '#4CAF50',
  },
  timelineIconActive: {
    backgroundColor: '#2196F3',
    borderColor: '#2196F3',
  },
  timelineLine: {
    width: 2,
    height: 40,
    backgroundColor: '#CCCCCC',
    marginTop: SPACING.sm,
  },
  timelineLineCompleted: {
    backgroundColor: '#4CAF50',
  },
  timelineRight: {
    flex: 1,
    paddingTop: SPACING.sm,
    paddingBottom: SPACING.lg,
  },
  timelineLabel: {
    fontSize: FONT_SIZES.md,
    color: colors.textSecondary,
  },
  timelineLabelCompleted: {
    color: colors.text,
    fontWeight: FONT_WEIGHTS.medium,
  },
  itemsCard: {
    marginHorizontal: SPACING.lg,
    marginBottom: SPACING.lg,
    padding: SPACING.lg,
  },
  itemsList: {
    gap: SPACING.lg,
  },
  orderItem: {
    paddingBottom: SPACING.lg,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  itemContent: {
    flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
    alignItems: 'flex-start',
  },
  itemImage: {
    width: 60,
    height: 60,
    borderRadius: BORDER_RADIUS.md,
    backgroundColor: colors.surface,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: SPACING.md,
  },
  itemDetails: {
    flex: 1,
    gap: SPACING.xs,
  },
  itemName: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.medium,
    color: colors.text,
  },
  vendorName: {
    fontSize: FONT_SIZES.sm,
    color: '#667eea',
    fontWeight: FONT_WEIGHTS.medium,
  },
  itemPricing: {
    flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: SPACING.sm,
  },
  itemPrice: {
    fontSize: FONT_SIZES.sm,
    color: colors.textSecondary,
  },
  itemTotal: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.semiBold,
    color: colors.text,
  },
  shippingCard: {
    marginHorizontal: SPACING.lg,
    marginBottom: SPACING.lg,
    padding: SPACING.lg,
  },
  shippingDetails: {
    gap: SPACING.lg,
  },
  addressSection: {
    gap: SPACING.xs,
  },
  addressLabel: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.medium,
    color: colors.text,
    marginBottom: SPACING.sm,
  },
  addressText: {
    fontSize: FONT_SIZES.md,
    color: colors.textSecondary,
    lineHeight: 22,
  },
  actionsSection: {
    paddingHorizontal: SPACING.lg,
    paddingBottom: SPACING.xl,
    gap: SPACING.md,
  },
  actionButton: {
    marginBottom: SPACING.sm,
  },
});
