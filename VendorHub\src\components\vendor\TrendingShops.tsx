import React from 'react';
import { StyleSheet, Dimensions } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { RTLView, RTLText, RTLTouchableOpacity, RTLIcon, RTLScrollView } from '../RTL';
import { useThemedStyles, useVendors, useProducts, useI18n } from '../../hooks';
import { SPACING, FONT_SIZES, FONT_WEIGHTS, BORDER_RADIUS } from '../../constants/theme';
import { PREMIUM_GRADIENTS } from '../../constants/advancedColors';
import { SubtleGlow } from '../MoonlightEffects';
import type { ThemeColors } from '../../contexts/ThemeContext';
import type { Vendor } from '../../contexts/DataContext';

const { width: screenWidth } = Dimensions.get('window');
const CARD_WIDTH = screenWidth * 0.7;

interface TrendingShopsProps {
  onVendorPress: (vendorId: string) => void;
  limit?: number;
}

interface TrendingVendor extends Vendor {
  trendScore: number;
  trendReason: string;
  productCount: number;
  recentActivity: boolean;
}

export const TrendingShops: React.FC<TrendingShopsProps> = ({
  onVendorPress,
  limit = 10,
}) => {
  const styles = useThemedStyles(createStyles);
  const { getApprovedVendors } = useVendors();
  const { getProductsByVendor } = useProducts();
  const { t } = useI18n();

  // Calculate trending vendors based on multiple factors
  const trendingVendors = React.useMemo(() => {
    const vendors = getApprovedVendors();
    
    const vendorsWithTrend: TrendingVendor[] = vendors.map(vendor => {
      const products = getProductsByVendor(vendor.id);
      const activeProducts = products.filter(p => p.isActive);
      
      // Calculate trend score based on:
      // 1. Rating (40%)
      // 2. Product count (30%)
      // 3. Recent activity simulation (30%)
      const ratingScore = (vendor.rating / 5) * 40;
      const productScore = Math.min(activeProducts.length / 10, 1) * 30;
      const activityScore = Math.random() * 30; // Simulated recent activity
      
      const trendScore = ratingScore + productScore + activityScore;
      
      // Determine trend reason
      let trendReason = '';
      if (vendor.rating >= 4.5) {
        trendReason = t('vendor.highlyRated');
      } else if (activeProducts.length >= 10) {
        trendReason = t('vendor.manyProducts');
      } else if (activityScore > 20) {
        trendReason = t('vendor.recentlyActive');
      } else {
        trendReason = t('vendor.popular');
      }

      return {
        ...vendor,
        trendScore,
        trendReason,
        productCount: activeProducts.length,
        recentActivity: activityScore > 15,
      };
    });

    return vendorsWithTrend
      .sort((a, b) => b.trendScore - a.trendScore)
      .slice(0, limit);
  }, [getApprovedVendors, getProductsByVendor, limit, t]);

  const renderTrendingCard = (vendor: TrendingVendor, index: number) => {
    const isTopTrending = index < 3;
    
    return (
      <RTLView key={vendor.id} style={styles.cardContainer}>
        <RTLTouchableOpacity
          style={styles.card}
          onPress={() => onVendorPress(vendor.id)}
          activeOpacity={0.9}
        >
          <SubtleGlow intensity={isTopTrending ? 0.8 : 0.5}>
            <LinearGradient
              colors={isTopTrending ? PREMIUM_GRADIENTS.royalSpotlight : PREMIUM_GRADIENTS.elegantDepth}
              style={styles.cardGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
            >
              {/* Trending Badge */}
              <RTLView style={styles.trendingBadge}>
                <RTLIcon name="trending-up" size={14} color="#10B981" />
                <RTLText style={styles.trendingText}>#{index + 1}</RTLText>
              </RTLView>

              {/* Activity Indicator */}
              {vendor.recentActivity && (
                <RTLView style={styles.activityIndicator}>
                  <RTLView style={styles.activityDot} />
                  <RTLText style={styles.activityText}>{t('vendor.active')}</RTLText>
                </RTLView>
              )}

              {/* Vendor Header */}
              <RTLView style={styles.vendorHeader}>
                <RTLView style={styles.vendorLogo}>
                  <RTLIcon name="storefront" size={40} color="#3B82F6" />
                </RTLView>
                <RTLView style={styles.vendorInfo}>
                  <RTLText style={styles.vendorName} numberOfLines={1}>
                    {vendor.businessName}
                  </RTLText>
                  <RTLText style={styles.trendReason} numberOfLines={1}>
                    {vendor.trendReason}
                  </RTLText>
                </RTLView>
              </RTLView>

              {/* Stats */}
              <RTLView style={styles.statsContainer}>
                <RTLView style={styles.statRow}>
                  <RTLView style={styles.statItem}>
                    <RTLIcon name="star" size={14} color="#FFD700" />
                    <RTLText style={styles.statText}>{(vendor.rating || 0).toFixed(1)}</RTLText>
                  </RTLView>
                  <RTLView style={styles.statItem}>
                    <RTLIcon name="cube-outline" size={14} color="#3B82F6" />
                    <RTLText style={styles.statText}>{vendor.productCount}</RTLText>
                  </RTLView>
                </RTLView>
                
                {/* Trend Score Bar */}
                <RTLView style={styles.trendScoreContainer}>
                  <RTLText style={styles.trendScoreLabel}>{t('vendor.trendScore')}</RTLText>
                  <RTLView style={styles.trendScoreBar}>
                    <RTLView 
                      style={[
                        styles.trendScoreFill, 
                        { width: `${Math.min(vendor.trendScore, 100)}%` }
                      ]} 
                    />
                  </RTLView>
                </RTLView>
              </RTLView>

              {/* Action */}
              <RTLView style={styles.actionContainer}>
                <RTLView style={styles.exploreButton}>
                  <RTLText style={styles.exploreText}>{t('vendor.explore')}</RTLText>
                  <RTLIcon name="arrow-forward" size={14} color="#3B82F6" />
                </RTLView>
              </RTLView>
            </LinearGradient>
          </SubtleGlow>
        </RTLTouchableOpacity>
      </RTLView>
    );
  };

  if (trendingVendors.length === 0) {
    return null;
  }

  return (
    <RTLView style={styles.container}>
      {/* Header */}
      <RTLView style={styles.header}>
        <RTLView style={styles.titleContainer}>
          <RTLIcon name="trending-up" size={24} color="#10B981" />
          <RTLText style={styles.title}>{t('vendor.trendingShops')}</RTLText>
        </RTLView>
        <RTLText style={styles.subtitle}>{t('vendor.hotVendorsRightNow')}</RTLText>
      </RTLView>

      {/* Trending Cards */}
      <RTLScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
        enableRTLScrolling={true}
      >
        {trendingVendors.map((vendor, index) => renderTrendingCard(vendor, index))}
      </RTLScrollView>
    </RTLView>
  );
};

const createStyles = (colors: ThemeColors) => StyleSheet.create({
  container: {
    marginBottom: SPACING.lg,
  },
  header: {
    paddingHorizontal: SPACING.md,
    marginBottom: SPACING.md,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.xs,
  },
  title: {
    fontSize: FONT_SIZES.xl,
    fontWeight: FONT_WEIGHTS.bold,
    color: colors.textPrimary,
    marginLeft: SPACING.sm,
  },
  subtitle: {
    fontSize: FONT_SIZES.sm,
    color: colors.textSecondary,
  },
  scrollContent: {
    paddingHorizontal: SPACING.md,
  },
  cardContainer: {
    width: CARD_WIDTH,
    marginRight: SPACING.md,
  },
  card: {
    height: 180,
    borderRadius: BORDER_RADIUS.xl,
    overflow: 'hidden',
  },
  cardGradient: {
    flex: 1,
    padding: SPACING.md,
    justifyContent: 'space-between',
  },
  trendingBadge: {
    position: 'absolute',
    top: SPACING.sm,
    left: SPACING.sm,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(16, 185, 129, 0.2)',
    paddingHorizontal: SPACING.sm,
    paddingVertical: SPACING.xs,
    borderRadius: BORDER_RADIUS.md,
    borderWidth: 1,
    borderColor: '#10B981',
  },
  trendingText: {
    fontSize: FONT_SIZES.xs,
    fontWeight: FONT_WEIGHTS.bold,
    color: '#10B981',
    marginLeft: SPACING.xs,
  },
  activityIndicator: {
    position: 'absolute',
    top: SPACING.sm,
    right: SPACING.sm,
    flexDirection: 'row',
    alignItems: 'center',
  },
  activityDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#10B981',
    marginRight: SPACING.xs,
  },
  activityText: {
    fontSize: FONT_SIZES.xs,
    color: '#10B981',
    fontWeight: FONT_WEIGHTS.medium,
  },
  vendorHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: SPACING.lg,
  },
  vendorLogo: {
    width: 50,
    height: 50,
    borderRadius: BORDER_RADIUS.lg,
    backgroundColor: 'rgba(59, 130, 246, 0.1)',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: SPACING.md,
  },
  vendorInfo: {
    flex: 1,
  },
  vendorName: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.bold,
    color: '#FFFFFF',
    marginBottom: SPACING.xs,
  },
  trendReason: {
    fontSize: FONT_SIZES.xs,
    color: 'rgba(255, 255, 255, 0.8)',
    fontWeight: FONT_WEIGHTS.medium,
  },
  statsContainer: {
    marginVertical: SPACING.sm,
  },
  statRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: SPACING.lg,
  },
  statText: {
    fontSize: FONT_SIZES.xs,
    color: 'rgba(255, 255, 255, 0.9)',
    marginLeft: SPACING.xs,
    fontWeight: FONT_WEIGHTS.medium,
  },
  trendScoreContainer: {
    marginTop: SPACING.xs,
  },
  trendScoreLabel: {
    fontSize: FONT_SIZES.xs,
    color: 'rgba(255, 255, 255, 0.7)',
    marginBottom: SPACING.xs,
  },
  trendScoreBar: {
    height: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 2,
    overflow: 'hidden',
  },
  trendScoreFill: {
    height: '100%',
    backgroundColor: '#10B981',
    borderRadius: 2,
  },
  actionContainer: {
    alignItems: 'flex-end',
  },
  exploreButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(59, 130, 246, 0.2)',
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    borderRadius: BORDER_RADIUS.md,
    borderWidth: 1,
    borderColor: '#3B82F6',
  },
  exploreText: {
    fontSize: FONT_SIZES.xs,
    fontWeight: FONT_WEIGHTS.semiBold,
    color: '#3B82F6',
    marginRight: SPACING.xs,
  },
});
