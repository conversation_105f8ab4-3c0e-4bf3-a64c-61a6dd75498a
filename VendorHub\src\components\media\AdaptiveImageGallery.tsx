import React, { useState, useRef } from 'react';
import { StyleSheet, Dimensions, Animated, PanResponder } from 'react-native';
import { RTLView, RTLText, RTLTouchableOpacity, RTLIcon, RTLScrollView } from '../RTL';
import { useThemedStyles } from '../../hooks';
import { SPACING, FONT_SIZES, FONT_WEIGHTS, BORDER_RADIUS } from '../../constants/theme';
import { SmartImage } from './SmartImage';
import { FadeInView, AnimatedPressable } from '../visual/MicroAnimations';
import type { ThemeColors } from '../../contexts/ThemeContext';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface ImageItem {
  id: string;
  uri: string;
  aspectRatio?: number;
  caption?: string;
  priority?: 'low' | 'normal' | 'high';
}

interface AdaptiveImageGalleryProps {
  images: ImageItem[];
  layout?: 'grid' | 'masonry' | 'carousel' | 'hero';
  columns?: number;
  spacing?: number;
  onImagePress?: (image: ImageItem, index: number) => void;
  showCaptions?: boolean;
  autoPlay?: boolean;
  autoPlayInterval?: number;
  enableZoom?: boolean;
  maxZoom?: number;
}

export const AdaptiveImageGallery: React.FC<AdaptiveImageGalleryProps> = ({
  images,
  layout = 'grid',
  columns = 2,
  spacing = SPACING.sm,
  onImagePress,
  showCaptions = false,
  autoPlay = false,
  autoPlayInterval = 3000,
  enableZoom = false,
  maxZoom = 3,
}) => {
  const styles = useThemedStyles(createStyles);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [zoomScale, setZoomScale] = useState(1);
  const scrollViewRef = useRef<any>(null);
  const zoomAnim = useRef(new Animated.Value(1)).current;

  // Auto-play for carousel
  React.useEffect(() => {
    if (autoPlay && layout === 'carousel' && images.length > 1) {
      const interval = setInterval(() => {
        setCurrentIndex(prev => (prev + 1) % images.length);
      }, autoPlayInterval);
      return () => clearInterval(interval);
    }
  }, [autoPlay, layout, images.length, autoPlayInterval]);

  // Zoom gesture handling
  const panResponder = PanResponder.create({
    onMoveShouldSetPanResponder: () => enableZoom,
    onMoveShouldSetPanResponderCapture: () => enableZoom,
    onPanResponderGrant: () => {
      zoomAnim.setOffset(zoomScale);
    },
    onPanResponderMove: (_, gestureState) => {
      if (enableZoom) {
        const scale = Math.max(1, Math.min(maxZoom, zoomScale + gestureState.dy * 0.01));
        zoomAnim.setValue(scale - zoomScale);
      }
    },
    onPanResponderRelease: (_, gestureState) => {
      zoomAnim.flattenOffset();
      const newScale = Math.max(1, Math.min(maxZoom, zoomScale + gestureState.dy * 0.01));
      setZoomScale(newScale);
      
      Animated.spring(zoomAnim, {
        toValue: 0,
        useNativeDriver: true,
      }).start();
    },
  });

  const getImageDimensions = (image: ImageItem, containerWidth: number) => {
    const aspectRatio = image.aspectRatio || 1;
    const width = containerWidth;
    const height = width / aspectRatio;
    return { width, height };
  };

  const renderGridLayout = () => {
    const itemWidth = (screenWidth - spacing * (columns + 1)) / columns;
    
    return (
      <RTLView style={styles.gridContainer}>
        {images.map((image, index) => {
          const dimensions = getImageDimensions(image, itemWidth);
          
          return (
            <FadeInView key={image.id} duration={600} delay={index * 100}>
              <AnimatedPressable
                style={[styles.gridItem, { width: itemWidth, marginBottom: spacing }]}
                onPress={() => onImagePress?.(image, index)}
              >
                <SmartImage
                  source={{ uri: image.uri }}
                  style={[styles.gridImage, { height: dimensions.height }]}
                  aspectRatio={image.aspectRatio}
                  priority={image.priority}
                  progressive
                  overlayGradient={showCaptions && image.caption ? 
                    ['transparent', 'rgba(0, 0, 0, 0.6)'] : undefined}
                >
                  {showCaptions && image.caption && (
                    <RTLText style={styles.imageCaption} numberOfLines={2}>
                      {image.caption}
                    </RTLText>
                  )}
                </SmartImage>
              </AnimatedPressable>
            </FadeInView>
          );
        })}
      </RTLView>
    );
  };

  const renderMasonryLayout = () => {
    const itemWidth = (screenWidth - spacing * (columns + 1)) / columns;
    const columnHeights = new Array(columns).fill(0);
    const columnItems: ImageItem[][] = new Array(columns).fill(null).map(() => []);

    // Distribute images across columns
    images.forEach((image) => {
      const shortestColumnIndex = columnHeights.indexOf(Math.min(...columnHeights));
      const dimensions = getImageDimensions(image, itemWidth);
      
      columnItems[shortestColumnIndex].push(image);
      columnHeights[shortestColumnIndex] += dimensions.height + spacing;
    });

    return (
      <RTLScrollView horizontal showsHorizontalScrollIndicator={false}>
        <RTLView style={styles.masonryContainer}>
          {columnItems.map((columnImages, columnIndex) => (
            <RTLView key={columnIndex} style={[styles.masonryColumn, { width: itemWidth }]}>
              {columnImages.map((image, imageIndex) => {
                const dimensions = getImageDimensions(image, itemWidth);
                const globalIndex = images.indexOf(image);
                
                return (
                  <FadeInView key={image.id} duration={600} delay={globalIndex * 50}>
                    <AnimatedPressable
                      style={[styles.masonryItem, { marginBottom: spacing }]}
                      onPress={() => onImagePress?.(image, globalIndex)}
                    >
                      <SmartImage
                        source={{ uri: image.uri }}
                        style={[styles.masonryImage, { height: dimensions.height }]}
                        aspectRatio={image.aspectRatio}
                        priority={image.priority}
                        progressive
                      />
                    </AnimatedPressable>
                  </FadeInView>
                );
              })}
            </RTLView>
          ))}
        </RTLView>
      </RTLScrollView>
    );
  };

  const renderCarouselLayout = () => {
    return (
      <RTLView style={styles.carouselContainer}>
        <RTLScrollView
          ref={scrollViewRef}
          horizontal
          pagingEnabled
          showsHorizontalScrollIndicator={false}
          onMomentumScrollEnd={(event) => {
            const newIndex = Math.round(event.nativeEvent.contentOffset.x / screenWidth);
            setCurrentIndex(newIndex);
          }}
        >
          {images.map((image, index) => (
            <Animated.View
              key={image.id}
              style={[
                styles.carouselItem,
                enableZoom && { transform: [{ scale: zoomAnim }] }
              ]}
              {...(enableZoom ? panResponder.panHandlers : {})}
            >
              <AnimatedPressable onPress={() => onImagePress?.(image, index)}>
                <SmartImage
                  source={{ uri: image.uri }}
                  style={styles.carouselImage}
                  aspectRatio={image.aspectRatio}
                  priority={index === currentIndex ? 'high' : 'normal'}
                  progressive
                  overlayGradient={showCaptions && image.caption ? 
                    ['transparent', 'rgba(0, 0, 0, 0.6)'] : undefined}
                >
                  {showCaptions && image.caption && (
                    <RTLText style={styles.carouselCaption}>
                      {image.caption}
                    </RTLText>
                  )}
                </SmartImage>
              </AnimatedPressable>
            </Animated.View>
          ))}
        </RTLScrollView>

        {/* Pagination Dots */}
        <RTLView style={styles.pagination}>
          {images.map((_, index) => (
            <RTLTouchableOpacity
              key={index}
              style={[
                styles.paginationDot,
                index === currentIndex && styles.paginationDotActive,
              ]}
              onPress={() => {
                setCurrentIndex(index);
                scrollViewRef.current?.scrollTo({ x: index * screenWidth, animated: true });
              }}
            />
          ))}
        </RTLView>
      </RTLView>
    );
  };

  const renderHeroLayout = () => {
    const heroImage = images[0];
    const thumbnails = images.slice(1, 5);

    return (
      <RTLView style={styles.heroContainer}>
        {/* Main Hero Image */}
        <FadeInView duration={800}>
          <AnimatedPressable onPress={() => onImagePress?.(heroImage, 0)}>
            <SmartImage
              source={{ uri: heroImage.uri }}
              style={styles.heroImage}
              aspectRatio={heroImage.aspectRatio || 16/9}
              priority="high"
              progressive
              overlayGradient={['transparent', 'rgba(0, 0, 0, 0.3)']}
            >
              {showCaptions && heroImage.caption && (
                <RTLText style={styles.heroCaption}>
                  {heroImage.caption}
                </RTLText>
              )}
            </SmartImage>
          </AnimatedPressable>
        </FadeInView>

        {/* Thumbnail Strip */}
        {thumbnails.length > 0 && (
          <RTLScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.thumbnailStrip}
            contentContainerStyle={styles.thumbnailContent}
          >
            {thumbnails.map((image, index) => (
              <FadeInView key={image.id} duration={600} delay={(index + 1) * 100}>
                <AnimatedPressable
                  style={styles.thumbnailItem}
                  onPress={() => onImagePress?.(image, index + 1)}
                >
                  <SmartImage
                    source={{ uri: image.uri }}
                    style={styles.thumbnailImage}
                    aspectRatio={1}
                    priority="low"
                    progressive
                  />
                </AnimatedPressable>
              </FadeInView>
            ))}
          </RTLScrollView>
        )}
      </RTLView>
    );
  };

  const renderLayout = () => {
    switch (layout) {
      case 'masonry':
        return renderMasonryLayout();
      case 'carousel':
        return renderCarouselLayout();
      case 'hero':
        return renderHeroLayout();
      default:
        return renderGridLayout();
    }
  };

  if (images.length === 0) {
    return (
      <RTLView style={styles.emptyContainer}>
        <RTLIcon name="images-outline" size={48} color="rgba(255, 255, 255, 0.5)" />
        <RTLText style={styles.emptyText}>No images available</RTLText>
      </RTLView>
    );
  }

  return <RTLView style={styles.container}>{renderLayout()}</RTLView>;
};

const createStyles = (colors: ThemeColors) => StyleSheet.create({
  container: {
    flex: 1,
  },

  // Grid Layout
  gridContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    paddingHorizontal: SPACING.sm,
  },
  gridItem: {
    borderRadius: BORDER_RADIUS.md,
    overflow: 'hidden',
  },
  gridImage: {
    width: '100%',
  },

  // Masonry Layout
  masonryContainer: {
    flexDirection: 'row',
    paddingHorizontal: SPACING.sm,
  },
  masonryColumn: {
    marginHorizontal: SPACING.xs,
  },
  masonryItem: {
    borderRadius: BORDER_RADIUS.md,
    overflow: 'hidden',
  },
  masonryImage: {
    width: '100%',
  },

  // Carousel Layout
  carouselContainer: {
    position: 'relative',
  },
  carouselItem: {
    width: screenWidth,
    height: screenHeight * 0.6,
  },
  carouselImage: {
    width: '100%',
    height: '100%',
  },
  pagination: {
    position: 'absolute',
    bottom: SPACING.lg,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  paginationDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.5)',
    marginHorizontal: 4,
  },
  paginationDotActive: {
    backgroundColor: '#FFFFFF',
    width: 24,
  },

  // Hero Layout
  heroContainer: {
    gap: SPACING.md,
  },
  heroImage: {
    width: '100%',
    height: screenHeight * 0.4,
    borderRadius: BORDER_RADIUS.lg,
  },
  thumbnailStrip: {
    maxHeight: 80,
  },
  thumbnailContent: {
    paddingHorizontal: SPACING.sm,
    gap: SPACING.sm,
  },
  thumbnailItem: {
    width: 80,
    height: 80,
    borderRadius: BORDER_RADIUS.md,
    overflow: 'hidden',
  },
  thumbnailImage: {
    width: '100%',
    height: '100%',
  },

  // Captions
  imageCaption: {
    fontSize: FONT_SIZES.sm,
    color: '#FFFFFF',
    fontWeight: FONT_WEIGHTS.medium,
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  carouselCaption: {
    fontSize: FONT_SIZES.lg,
    color: '#FFFFFF',
    fontWeight: FONT_WEIGHTS.semiBold,
    textAlign: 'center',
    textShadowColor: 'rgba(0, 0, 0, 0.7)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  },
  heroCaption: {
    fontSize: FONT_SIZES.xl,
    color: '#FFFFFF',
    fontWeight: FONT_WEIGHTS.bold,
    textShadowColor: 'rgba(0, 0, 0, 0.7)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  },

  // Empty State
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: SPACING.xl,
  },
  emptyText: {
    fontSize: FONT_SIZES.md,
    color: colors.textSecondary,
    marginTop: SPACING.md,
  },
});
