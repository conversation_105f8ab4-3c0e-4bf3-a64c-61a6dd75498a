import { I18nManager } from 'react-native';
import { interpolate, Extrapolation } from 'react-native-reanimated';

/**
 * RTL-aware animation utilities for React Native
 * Ensures animations respect RTL layout direction
 */

export interface RTLAnimationConfig {
  isRTL?: boolean;
  respectRTL?: boolean;
}

/**
 * Get the current RTL state
 */
export const isRTL = (): boolean => {
  return I18nManager.isRTL;
};

/**
 * Convert a horizontal translation value to respect RTL
 * In RTL mode, positive X values should move left, negative values should move right
 */
export const rtlAwareTranslateX = (
  value: number,
  config: RTLAnimationConfig = { respectRTL: true }
): number => {
  if (!config.respectRTL) return value;
  
  const currentRTL = config.isRTL !== undefined ? config.isRTL : isRTL();
  return currentRTL ? -value : value;
};

/**
 * Convert rotation values to respect RTL
 * Some rotations should be mirrored in RTL mode
 */
export const rtlAwareRotation = (
  value: number,
  config: RTLAnimationConfig = { respectRTL: true }
): number => {
  if (!config.respectRTL) return value;
  
  const currentRTL = config.isRTL !== undefined ? config.isRTL : isRTL();
  return currentRTL ? -value : value;
};

/**
 * Create RTL-aware interpolation for horizontal movements
 */
export const rtlAwareInterpolateX = (
  animatedValue: number,
  inputRange: number[],
  outputRange: number[],
  config: RTLAnimationConfig = { respectRTL: true },
  extrapolation: Extrapolation = Extrapolation.CLAMP
): number => {
  if (!config.respectRTL) {
    return interpolate(animatedValue, inputRange, outputRange, extrapolation);
  }
  
  const currentRTL = config.isRTL !== undefined ? config.isRTL : isRTL();
  const adjustedOutputRange = currentRTL 
    ? outputRange.map(value => -value)
    : outputRange;
    
  return interpolate(animatedValue, inputRange, adjustedOutputRange, extrapolation);
};

/**
 * Create RTL-aware interpolation for rotation
 */
export const rtlAwareInterpolateRotation = (
  animatedValue: number,
  inputRange: number[],
  outputRange: number[],
  config: RTLAnimationConfig = { respectRTL: true },
  extrapolation: Extrapolation = Extrapolation.CLAMP
): number => {
  if (!config.respectRTL) {
    return interpolate(animatedValue, inputRange, outputRange, extrapolation);
  }
  
  const currentRTL = config.isRTL !== undefined ? config.isRTL : isRTL();
  const adjustedOutputRange = currentRTL 
    ? outputRange.map(value => -value)
    : outputRange;
    
  return interpolate(animatedValue, inputRange, adjustedOutputRange, extrapolation);
};

/**
 * Get RTL-aware flex direction
 */
export const rtlAwareFlexDirection = (
  direction: 'row' | 'row-reverse' | 'column' | 'column-reverse' = 'row',
  config: RTLAnimationConfig = { respectRTL: true }
): 'row' | 'row-reverse' | 'column' | 'column-reverse' => {
  if (!config.respectRTL || direction === 'column' || direction === 'column-reverse') {
    return direction;
  }
  
  const currentRTL = config.isRTL !== undefined ? config.isRTL : isRTL();
  
  if (currentRTL) {
    return direction === 'row' ? 'row-reverse' : 'row';
  }
  
  return direction;
};

/**
 * Get RTL-aware text alignment
 */
export const rtlAwareTextAlign = (
  align: 'left' | 'right' | 'center' | 'justify' = 'left',
  config: RTLAnimationConfig = { respectRTL: true }
): 'left' | 'right' | 'center' | 'justify' => {
  if (!config.respectRTL || align === 'center' || align === 'justify') {
    return align;
  }
  
  const currentRTL = config.isRTL !== undefined ? config.isRTL : isRTL();
  
  if (currentRTL) {
    return align === 'left' ? 'right' : 'left';
  }
  
  return align;
};

/**
 * Get RTL-aware margin/padding values
 * Swaps left and right values in RTL mode
 */
export const rtlAwareSpacing = (
  spacing: {
    top?: number;
    right?: number;
    bottom?: number;
    left?: number;
  },
  config: RTLAnimationConfig = { respectRTL: true }
) => {
  if (!config.respectRTL) return spacing;
  
  const currentRTL = config.isRTL !== undefined ? config.isRTL : isRTL();
  
  if (currentRTL) {
    return {
      top: spacing.top,
      right: spacing.left,
      bottom: spacing.bottom,
      left: spacing.right,
    };
  }
  
  return spacing;
};

/**
 * Create RTL-aware slide animation values
 * For slide-in animations that should come from the correct side
 */
export const rtlAwareSlideValues = (
  direction: 'left' | 'right' | 'up' | 'down',
  distance: number = 100,
  config: RTLAnimationConfig = { respectRTL: true }
): { x: number; y: number } => {
  if (!config.respectRTL) {
    switch (direction) {
      case 'left':
        return { x: -distance, y: 0 };
      case 'right':
        return { x: distance, y: 0 };
      case 'up':
        return { x: 0, y: -distance };
      case 'down':
        return { x: 0, y: distance };
    }
  }
  
  const currentRTL = config.isRTL !== undefined ? config.isRTL : isRTL();
  
  switch (direction) {
    case 'left':
      return { x: currentRTL ? distance : -distance, y: 0 };
    case 'right':
      return { x: currentRTL ? -distance : distance, y: 0 };
    case 'up':
      return { x: 0, y: -distance };
    case 'down':
      return { x: 0, y: distance };
  }
};

/**
 * Utility to check if an animation should be mirrored in RTL
 * Some animations (like loading spinners) should not be mirrored
 */
export const shouldMirrorInRTL = (animationType: string): boolean => {
  const nonMirroredAnimations = [
    'spin',
    'rotate',
    'pulse',
    'scale',
    'fade',
    'bounce-vertical',
    'loading',
  ];
  
  return !nonMirroredAnimations.includes(animationType);
};

/**
 * Create RTL-aware transform array
 */
export const createRTLAwareTransform = (
  transforms: Array<{
    translateX?: number;
    translateY?: number;
    rotate?: string;
    scale?: number;
    scaleX?: number;
    scaleY?: number;
  }>,
  config: RTLAnimationConfig = { respectRTL: true }
) => {
  if (!config.respectRTL) return transforms;
  
  const currentRTL = config.isRTL !== undefined ? config.isRTL : isRTL();
  
  return transforms.map(transform => {
    const newTransform = { ...transform };
    
    // Mirror translateX in RTL
    if (newTransform.translateX !== undefined && currentRTL) {
      newTransform.translateX = -newTransform.translateX;
    }
    
    // Mirror rotation in RTL for certain cases
    if (newTransform.rotate !== undefined && currentRTL) {
      const rotateValue = parseFloat(newTransform.rotate.replace('deg', ''));
      newTransform.rotate = `${-rotateValue}deg`;
    }
    
    return newTransform;
  });
};

/**
 * RTL-aware animation presets
 */
export const RTLAnimationPresets = {
  slideInFromStart: (config?: RTLAnimationConfig) => 
    rtlAwareSlideValues('left', 100, config),
  slideInFromEnd: (config?: RTLAnimationConfig) => 
    rtlAwareSlideValues('right', 100, config),
  slideOutToStart: (config?: RTLAnimationConfig) => 
    rtlAwareSlideValues('left', -100, config),
  slideOutToEnd: (config?: RTLAnimationConfig) => 
    rtlAwareSlideValues('right', -100, config),
};
