import React, { useMemo, useRef, useEffect, useState } from 'react';
import { FlatList, FlatListProps, StyleSheet, ViewStyle, LayoutChangeEvent } from 'react-native';
import { useI18n } from '../../hooks/useI18n';

interface RTLFlatListProps<T> extends FlatListProps<T> {
  style?: ViewStyle | ViewStyle[];
  contentContainerStyle?: ViewStyle | ViewStyle[];
  enableRTLScrolling?: boolean; // Enable automatic RTL scrolling behavior
}

export const RTLFlatList = <T,>({
  style,
  contentContainerStyle,
  horizontal,
  enableRTLScrolling = true,
  children,
  ...props
}: RTLFlatListProps<T>) => {
  const { isRTL } = useI18n();
  const flatListRef = useRef<FlatList>(null);
  const [flatListWidth, setFlatListWidth] = useState(0);
  const [contentWidth, setContentWidth] = useState(0);
  const [hasInitializedScroll, setHasInitializedScroll] = useState(false);

  const rtlStyle = useMemo(() => {
    if (!style || !isRTL) return style;
    
    const flattenedStyle = StyleSheet.flatten(style);
    const rtlFlattenedStyle = { ...flattenedStyle };
    
    // Flip padding
    if (flattenedStyle.paddingLeft !== undefined) {
      rtlFlattenedStyle.paddingRight = flattenedStyle.paddingLeft;
      delete rtlFlattenedStyle.paddingLeft;
    }
    if (flattenedStyle.paddingRight !== undefined) {
      rtlFlattenedStyle.paddingLeft = flattenedStyle.paddingRight;
      delete rtlFlattenedStyle.paddingRight;
    }
    
    // Flip margin
    if (flattenedStyle.marginLeft !== undefined) {
      rtlFlattenedStyle.marginRight = flattenedStyle.marginLeft;
      delete rtlFlattenedStyle.marginLeft;
    }
    if (flattenedStyle.marginRight !== undefined) {
      rtlFlattenedStyle.marginLeft = flattenedStyle.marginRight;
      delete rtlFlattenedStyle.marginRight;
    }
    
    return rtlFlattenedStyle;
  }, [style, isRTL]);

  const rtlContentContainerStyle = useMemo(() => {
    if (!contentContainerStyle) {
      // Default styles for horizontal scrolling
      if (horizontal) {
        return {
          flexDirection: isRTL ? 'row-reverse' : 'row',
          justifyContent: 'flex-start',
        };
      }
      return contentContainerStyle;
    }

    const flattenedStyle = StyleSheet.flatten(contentContainerStyle);
    const rtlFlattenedStyle = { ...flattenedStyle };

    // For horizontal scrolling, set proper flex direction
    if (horizontal) {
      if (isRTL) {
        // In RTL mode, use row-reverse so first item appears on the right
        rtlFlattenedStyle.flexDirection = 'row-reverse';
      } else {
        // In LTR mode, use normal row direction
        rtlFlattenedStyle.flexDirection = 'row';
      }

      // Ensure proper justification
      if (!flattenedStyle.justifyContent) {
        rtlFlattenedStyle.justifyContent = 'flex-start';
      }
    }

    // Flip padding for RTL
    if (isRTL) {
      if (flattenedStyle.paddingLeft !== undefined) {
        rtlFlattenedStyle.paddingRight = flattenedStyle.paddingLeft;
        delete rtlFlattenedStyle.paddingLeft;
      }
      if (flattenedStyle.paddingRight !== undefined) {
        rtlFlattenedStyle.paddingLeft = flattenedStyle.paddingRight;
        delete rtlFlattenedStyle.paddingRight;
      }
    }

    return rtlFlattenedStyle;
  }, [contentContainerStyle, isRTL, horizontal]);

  // Handle initial scroll position for RTL horizontal scrolling
  useEffect(() => {
    if (
      isRTL &&
      horizontal &&
      enableRTLScrolling &&
      flatListWidth > 0 &&
      contentWidth > flatListWidth &&
      !hasInitializedScroll &&
      props.data &&
      props.data.length > 0
    ) {
      // Calculate the initial scroll position to start at the rightmost position
      const initialScrollX = contentWidth - flatListWidth;

      // Use a small delay to ensure the layout is complete
      setTimeout(() => {
        flatListRef.current?.scrollToOffset({
          offset: initialScrollX,
          animated: false, // Don't animate the initial positioning
        });
        setHasInitializedScroll(true);
      }, 50);
    }
  }, [isRTL, horizontal, enableRTLScrolling, flatListWidth, contentWidth, hasInitializedScroll, props.data]);

  // Handle layout changes to get dimensions
  const handleFlatListLayout = (event: LayoutChangeEvent) => {
    const { width } = event.nativeEvent.layout;
    setFlatListWidth(width);

    // Call the original onLayout if provided
    if (props.onLayout) {
      props.onLayout(event);
    }
  };

  const handleContentSizeChange = (width: number, height: number) => {
    if (horizontal) {
      setContentWidth(width);
    }

    // Call the original onContentSizeChange if provided
    if (props.onContentSizeChange) {
      props.onContentSizeChange(width, height);
    }
  };

  // Reset initialization flag when RTL mode changes or data changes
  useEffect(() => {
    setHasInitializedScroll(false);
  }, [isRTL, props.data]);

  return (
    <FlatList
      ref={flatListRef}
      style={rtlStyle}
      contentContainerStyle={rtlContentContainerStyle}
      horizontal={horizontal}
      onLayout={handleFlatListLayout}
      onContentSizeChange={handleContentSizeChange}
      // Ensure proper scroll behavior in RTL mode
      showsHorizontalScrollIndicator={false}
      {...props}
    >
      {children}
    </FlatList>
  );
};
