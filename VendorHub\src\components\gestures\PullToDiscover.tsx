import React, { useRef, useState } from 'react';
import { StyleSheet, Animated, PanResponder, Dimensions, Vibration } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { RTLView, RTLText, RTLIcon, RTLScrollView } from '../RTL';
import { useThemedStyles, useI18n } from '../../hooks';
import { SPACING, FONT_SIZES, FONT_WEIGHTS, BORDER_RADIUS } from '../../constants/theme';
import { PREMIUM_GRADIENTS } from '../../constants/advancedColors';
import { PulseView, FadeInView } from '../visual/MicroAnimations';
import type { ThemeColors } from '../../contexts/ThemeContext';

const { height: screenHeight } = Dimensions.get('window');
const PULL_THRESHOLD = 120;
const DISCOVER_THRESHOLD = 80;

interface PullToDiscoverProps {
  children: React.ReactNode;
  onRefresh?: () => Promise<void>;
  onDiscover?: () => Promise<void>;
  refreshing?: boolean;
  discovering?: boolean;
  enableDiscover?: boolean;
  pullThreshold?: number;
  discoverThreshold?: number;
  hapticFeedback?: boolean;
}

export const PullToDiscover: React.FC<PullToDiscoverProps> = ({
  children,
  onRefresh,
  onDiscover,
  refreshing = false,
  discovering = false,
  enableDiscover = true,
  pullThreshold = PULL_THRESHOLD,
  discoverThreshold = DISCOVER_THRESHOLD,
  hapticFeedback = true,
}) => {
  const styles = useThemedStyles(createStyles);
  const { t } = useI18n();
  
  const [pullDistance, setPullDistance] = useState(0);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isDiscovering, setIsDiscovering] = useState(false);
  const [canTrigger, setCanTrigger] = useState(true);
  
  const translateY = useRef(new Animated.Value(0)).current;
  const rotateAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const opacityAnim = useRef(new Animated.Value(0)).current;
  
  const scrollViewRef = useRef<any>(null);
  const lastHapticDistance = useRef(0);

  const triggerHaptic = (type: 'light' | 'medium' | 'heavy' = 'light') => {
    if (hapticFeedback) {
      switch (type) {
        case 'light':
          Vibration.vibrate(25);
          break;
        case 'medium':
          Vibration.vibrate(50);
          break;
        case 'heavy':
          Vibration.vibrate([0, 100, 50, 100]);
          break;
      }
    }
  };

  const getProgressPercentage = () => {
    return Math.min(pullDistance / pullThreshold, 1);
  };

  const getDiscoverPercentage = () => {
    return Math.min(pullDistance / discoverThreshold, 1);
  };

  const animateIndicator = () => {
    const progress = getProgressPercentage();
    
    // Rotate animation
    rotateAnim.setValue(progress * 360);
    
    // Scale animation
    scaleAnim.setValue(0.8 + progress * 0.4);
    
    // Opacity animation
    opacityAnim.setValue(Math.min(progress * 2, 1));
  };

  const resetAnimations = () => {
    Animated.parallel([
      Animated.spring(translateY, {
        toValue: 0,
        useNativeDriver: true,
        tension: 100,
        friction: 8,
      }),
      Animated.spring(rotateAnim, {
        toValue: 0,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        useNativeDriver: true,
      }),
      Animated.timing(opacityAnim, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const handleRefresh = async () => {
    if (isRefreshing || !canTrigger) return;
    
    setIsRefreshing(true);
    setCanTrigger(false);
    triggerHaptic('heavy');
    
    try {
      await onRefresh?.();
    } finally {
      setIsRefreshing(false);
      setTimeout(() => setCanTrigger(true), 1000);
      resetAnimations();
    }
  };

  const handleDiscover = async () => {
    if (isDiscovering || !canTrigger || !enableDiscover) return;
    
    setIsDiscovering(true);
    setCanTrigger(false);
    triggerHaptic('medium');
    
    try {
      await onDiscover?.();
    } finally {
      setIsDiscovering(false);
      setTimeout(() => setCanTrigger(true), 1000);
      resetAnimations();
    }
  };

  const panResponder = PanResponder.create({
    onMoveShouldSetPanResponder: (_, gestureState) => {
      return gestureState.dy > 0 && Math.abs(gestureState.dy) > Math.abs(gestureState.dx);
    },

    onPanResponderGrant: () => {
      translateY.setOffset(0);
    },

    onPanResponderMove: (_, gestureState) => {
      const { dy } = gestureState;
      
      if (dy > 0) {
        const resistance = dy > pullThreshold ? 0.3 : 1;
        const adjustedDistance = dy * resistance;
        
        setPullDistance(adjustedDistance);
        translateY.setValue(adjustedDistance);
        animateIndicator();
        
        // Haptic feedback at thresholds
        if (hapticFeedback) {
          if (adjustedDistance > discoverThreshold && lastHapticDistance.current < discoverThreshold) {
            triggerHaptic('light');
            lastHapticDistance.current = adjustedDistance;
          } else if (adjustedDistance > pullThreshold && lastHapticDistance.current < pullThreshold) {
            triggerHaptic('medium');
            lastHapticDistance.current = adjustedDistance;
          }
        }
      }
    },

    onPanResponderRelease: (_, gestureState) => {
      const { dy, vy } = gestureState;
      lastHapticDistance.current = 0;
      
      if (dy > pullThreshold || vy > 1) {
        handleRefresh();
      } else if (enableDiscover && dy > discoverThreshold) {
        handleDiscover();
      } else {
        resetAnimations();
      }
      
      setPullDistance(0);
    },

    onPanResponderTerminate: () => {
      resetAnimations();
      setPullDistance(0);
      lastHapticDistance.current = 0;
    },
  });

  const renderPullIndicator = () => {
    const progress = getProgressPercentage();
    const discoverProgress = getDiscoverPercentage();
    
    let indicatorText = t('common.pullToRefresh');
    let indicatorIcon = 'refresh';
    let indicatorColor = '#3B82F6';
    
    if (isRefreshing) {
      indicatorText = t('common.refreshing');
      indicatorIcon = 'refresh';
    } else if (isDiscovering) {
      indicatorText = t('common.discovering');
      indicatorIcon = 'search';
      indicatorColor = '#10B981';
    } else if (progress >= 1) {
      indicatorText = t('common.releaseToRefresh');
      indicatorIcon = 'checkmark-circle';
      indicatorColor = '#10B981';
    } else if (enableDiscover && discoverProgress >= 1) {
      indicatorText = t('common.releaseToDiscover');
      indicatorIcon = 'search';
      indicatorColor = '#F59E0B';
    }

    return (
      <Animated.View
        style={[
          styles.pullIndicator,
          {
            opacity: opacityAnim,
            transform: [
              { scale: scaleAnim },
              { 
                rotate: rotateAnim.interpolate({
                  inputRange: [0, 360],
                  outputRange: ['0deg', '360deg'],
                })
              },
            ],
          },
        ]}
      >
        <LinearGradient
          colors={PREMIUM_GRADIENTS.elegantDepth}
          style={styles.indicatorGradient}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        >
          <PulseView
            minScale={0.9}
            maxScale={1.1}
            duration={1000}
            loop={isRefreshing || isDiscovering}
          >
            <RTLIcon name={indicatorIcon} size={24} color={indicatorColor} />
          </PulseView>
          
          <RTLText style={[styles.indicatorText, { color: indicatorColor }]}>
            {indicatorText}
          </RTLText>
          
          {/* Progress Ring */}
          <RTLView style={styles.progressRing}>
            <RTLView 
              style={[
                styles.progressFill,
                {
                  transform: [
                    { 
                      rotate: `${progress * 360}deg`
                    }
                  ],
                }
              ]}
            />
          </RTLView>
        </LinearGradient>
      </Animated.View>
    );
  };

  return (
    <RTLView style={styles.container}>
      <Animated.View
        style={[
          styles.content,
          {
            transform: [{ translateY }],
          },
        ]}
        {...panResponder.panHandlers}
      >
        {/* Pull Indicator */}
        {renderPullIndicator()}
        
        {/* Content */}
        <RTLScrollView
          ref={scrollViewRef}
          style={styles.scrollView}
          showsVerticalScrollIndicator={false}
          scrollEventThrottle={16}
        >
          {children}
        </RTLScrollView>
      </Animated.View>
    </RTLView>
  );
};

const createStyles = (colors: ThemeColors) => StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  pullIndicator: {
    position: 'absolute',
    top: -80,
    left: '50%',
    transform: [{ translateX: -40 }],
    width: 80,
    height: 80,
    borderRadius: 40,
    zIndex: 1000,
    overflow: 'hidden',
  },
  indicatorGradient: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 40,
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  indicatorText: {
    fontSize: FONT_SIZES.xs,
    fontWeight: FONT_WEIGHTS.semiBold,
    marginTop: SPACING.xs,
    textAlign: 'center',
  },
  progressRing: {
    position: 'absolute',
    top: -2,
    left: -2,
    right: -2,
    bottom: -2,
    borderRadius: 42,
    borderWidth: 2,
    borderColor: 'transparent',
    borderTopColor: '#3B82F6',
  },
  progressFill: {
    position: 'absolute',
    top: -2,
    left: -2,
    right: -2,
    bottom: -2,
    borderRadius: 42,
    borderWidth: 2,
    borderColor: 'transparent',
    borderTopColor: '#10B981',
  },
});
