import React, { useState, useEffect } from 'react';
import { StyleSheet, Dimensions, Animated } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { RTLView, RTLText, RTLTouchableOpacity, RTLIcon, RTLScrollView } from '../RTL';
import { useThemedStyles, useProducts, useI18n } from '../../hooks';
import { SPACING, FONT_SIZES, FONT_WEIGHTS, BORDER_RADIUS } from '../../constants/theme';
import { PREMIUM_GRADIENTS } from '../../constants/advancedColors';
import { SubtleGlow } from '../MoonlightEffects';
import type { ThemeColors } from '../../contexts/ThemeContext';
import type { Vendor } from '../../contexts/DataContext';

const { width: screenWidth } = Dimensions.get('window');
const STORY_WIDTH = 80;
const STORY_HEIGHT = 100;

interface VendorStoryPreviewProps {
  vendors: Vendor[];
  onVendorPress: (vendorId: string) => void;
  onStoryPress?: (vendorId: string) => void;
}

interface VendorStory {
  vendor: Vendor;
  hasNewContent: boolean;
  storyType: 'new_products' | 'sale' | 'featured' | 'update';
  badge?: string;
  badgeColor?: string;
}

export const VendorStoryPreview: React.FC<VendorStoryPreviewProps> = ({
  vendors,
  onVendorPress,
  onStoryPress,
}) => {
  const styles = useThemedStyles(createStyles);
  const { getProductsByVendor } = useProducts();
  const { t } = useI18n();

  // Create vendor stories with different types
  const vendorStories: VendorStory[] = React.useMemo(() => {
    return vendors.slice(0, 10).map(vendor => {
      const products = getProductsByVendor(vendor.id);
      const activeProducts = products.filter(p => p.isActive);
      const hasNewProducts = Math.random() > 0.7; // Simulate new products
      const hasSale = activeProducts.some(p => p.salePrice && p.salePrice < p.price);
      const isHighRated = vendor.rating >= 4.5;

      let storyType: VendorStory['storyType'] = 'update';
      let badge = '';
      let badgeColor = '#3B82F6';

      if (hasNewProducts) {
        storyType = 'new_products';
        badge = t('vendor.newProducts');
        badgeColor = '#10B981';
      } else if (hasSale) {
        storyType = 'sale';
        badge = t('vendor.onSale');
        badgeColor = '#F59E0B';
      } else if (isHighRated) {
        storyType = 'featured';
        badge = t('vendor.featured');
        badgeColor = '#FFD700';
      }

      return {
        vendor,
        hasNewContent: hasNewProducts || hasSale,
        storyType,
        badge,
        badgeColor,
      };
    });
  }, [vendors, getProductsByVendor, t]);

  const renderStoryItem = (story: VendorStory, index: number) => {
    const [pulseAnim] = useState(new Animated.Value(1));

    useEffect(() => {
      if (story.hasNewContent) {
        const pulse = Animated.loop(
          Animated.sequence([
            Animated.timing(pulseAnim, {
              toValue: 1.1,
              duration: 1000,
              useNativeDriver: true,
            }),
            Animated.timing(pulseAnim, {
              toValue: 1,
              duration: 1000,
              useNativeDriver: true,
            }),
          ])
        );
        pulse.start();
        return () => pulse.stop();
      }
    }, [story.hasNewContent]);

    const getStoryGradient = () => {
      switch (story.storyType) {
        case 'new_products':
          return ['#10B981', '#059669'];
        case 'sale':
          return ['#F59E0B', '#D97706'];
        case 'featured':
          return ['#FFD700', '#F59E0B'];
        default:
          return ['#3B82F6', '#1D4ED8'];
      }
    };

    const getStoryIcon = () => {
      switch (story.storyType) {
        case 'new_products':
          return 'add-circle';
        case 'sale':
          return 'pricetag';
        case 'featured':
          return 'star';
        default:
          return 'storefront';
      }
    };

    return (
      <RTLView key={story.vendor.id} style={styles.storyContainer}>
        <RTLTouchableOpacity
          style={styles.storyTouchable}
          onPress={() => onStoryPress ? onStoryPress(story.vendor.id) : onVendorPress(story.vendor.id)}
          activeOpacity={0.8}
        >
          <Animated.View
            style={[
              styles.storyWrapper,
              story.hasNewContent && { transform: [{ scale: pulseAnim }] }
            ]}
          >
            <SubtleGlow intensity={story.hasNewContent ? 0.8 : 0.4}>
              <LinearGradient
                colors={getStoryGradient()}
                style={styles.storyGradient}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
              >
                {/* Story Ring */}
                {story.hasNewContent && (
                  <RTLView style={[styles.storyRing, { borderColor: story.badgeColor }]} />
                )}

                {/* Vendor Logo */}
                <RTLView style={styles.vendorLogoContainer}>
                  <RTLIcon name="storefront" size={24} color="#FFFFFF" />
                </RTLView>

                {/* Story Type Icon */}
                <RTLView style={[styles.storyTypeIcon, { backgroundColor: story.badgeColor }]}>
                  <RTLIcon name={getStoryIcon()} size={12} color="#FFFFFF" />
                </RTLView>

                {/* Content Preview */}
                <RTLView style={styles.storyContent}>
                  <RTLView style={styles.contentDots}>
                    <RTLView style={[styles.dot, styles.dotActive]} />
                    <RTLView style={styles.dot} />
                    <RTLView style={styles.dot} />
                  </RTLView>
                </RTLView>

                {/* Badge */}
                {story.badge && (
                  <RTLView style={[styles.storyBadge, { backgroundColor: story.badgeColor }]}>
                    <RTLText style={styles.badgeText}>{story.badge}</RTLText>
                  </RTLView>
                )}
              </LinearGradient>
            </SubtleGlow>
          </Animated.View>
        </RTLTouchableOpacity>

        {/* Vendor Name */}
        <RTLText style={styles.vendorName} numberOfLines={2}>
          {story.vendor.businessName}
        </RTLText>
      </RTLView>
    );
  };

  if (vendorStories.length === 0) {
    return null;
  }

  return (
    <RTLView style={styles.container}>
      {/* Header */}
      <RTLView style={styles.header}>
        <RTLView style={styles.titleContainer}>
          <RTLIcon name="play-circle" size={20} color="#3B82F6" />
          <RTLText style={styles.title}>{t('vendor.vendorStories')}</RTLText>
        </RTLView>
        <RTLText style={styles.subtitle}>{t('vendor.latestUpdates')}</RTLText>
      </RTLView>

      {/* Stories */}
      <RTLScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
        enableRTLScrolling={true}
      >
        {vendorStories.map((story, index) => renderStoryItem(story, index))}
      </RTLScrollView>
    </RTLView>
  );
};

const createStyles = (colors: ThemeColors) => StyleSheet.create({
  container: {
    marginBottom: SPACING.lg,
  },
  header: {
    paddingHorizontal: SPACING.md,
    marginBottom: SPACING.md,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.xs,
  },
  title: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.bold,
    color: colors.textPrimary,
    marginLeft: SPACING.sm,
  },
  subtitle: {
    fontSize: FONT_SIZES.sm,
    color: colors.textSecondary,
  },
  scrollContent: {
    paddingHorizontal: SPACING.md,
  },
  storyContainer: {
    width: STORY_WIDTH,
    marginRight: SPACING.md,
    alignItems: 'center',
  },
  storyTouchable: {
    marginBottom: SPACING.sm,
  },
  storyWrapper: {
    width: STORY_WIDTH,
    height: STORY_HEIGHT,
    borderRadius: BORDER_RADIUS.xl,
    overflow: 'hidden',
  },
  storyGradient: {
    flex: 1,
    padding: SPACING.sm,
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  storyRing: {
    position: 'absolute',
    top: -2,
    left: -2,
    right: -2,
    bottom: -2,
    borderRadius: BORDER_RADIUS.xl + 2,
    borderWidth: 2,
  },
  vendorLogoContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: SPACING.xs,
  },
  storyTypeIcon: {
    position: 'absolute',
    top: SPACING.sm,
    right: SPACING.sm,
    width: 20,
    height: 20,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  storyContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  contentDots: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dot: {
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: 'rgba(255, 255, 255, 0.4)',
    marginHorizontal: 2,
  },
  dotActive: {
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    width: 6,
    height: 6,
    borderRadius: 3,
  },
  storyBadge: {
    position: 'absolute',
    bottom: SPACING.xs,
    left: SPACING.xs,
    right: SPACING.xs,
    paddingVertical: SPACING.xs,
    borderRadius: BORDER_RADIUS.sm,
    alignItems: 'center',
  },
  badgeText: {
    fontSize: FONT_SIZES.xs,
    fontWeight: FONT_WEIGHTS.semiBold,
    color: '#FFFFFF',
  },
  vendorName: {
    fontSize: FONT_SIZES.xs,
    color: colors.textPrimary,
    textAlign: 'center',
    lineHeight: 14,
    fontWeight: FONT_WEIGHTS.medium,
  },
});
