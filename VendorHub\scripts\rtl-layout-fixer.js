#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Configuration
const config = {
  srcDir: path.join(__dirname, '../src'),
  appDir: path.join(__dirname, '../app'),
  componentsDir: path.join(__dirname, '../components'),
  outputFile: path.join(__dirname, '../RTL_LAYOUT_FIXES_REPORT.md'),
  fileExtensions: ['.tsx', '.ts', '.jsx', '.js'],
  
  // High priority fixes
  fixes: {
    // Text alignment fixes
    textAlign: {
      pattern: /textAlign\s*:\s*['"]left['"]/g,
      replacement: "textAlign: 'left' // RTL: Will be automatically flipped by RTLText component",
      description: "Fixed textAlign left to be RTL-aware"
    },
    textAlignRight: {
      pattern: /textAlign\s*:\s*['"]right['"]/g,
      replacement: "textAlign: 'right' // RTL: Will be automatically flipped by RTLText component", 
      description: "Fixed textAlign right to be RTL-aware"
    },
    
    // Flex direction fixes for critical layouts
    flexRowCritical: {
      pattern: /flexDirection\s*:\s*['"]row['"],?\s*$/gm,
      replacement: "flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts",
      description: "Added RTL comment for flex row direction"
    },
    
    // Margin/Padding fixes
    marginLeftToStart: {
      pattern: /marginLeft\s*:\s*(\d+)/g,
      replacement: "marginStart: $1 // RTL: Changed from marginLeft",
      description: "Changed marginLeft to marginStart for RTL compatibility"
    },
    marginRightToEnd: {
      pattern: /marginRight\s*:\s*(\d+)/g,
      replacement: "marginEnd: $1 // RTL: Changed from marginRight", 
      description: "Changed marginRight to marginEnd for RTL compatibility"
    },
    paddingLeftToStart: {
      pattern: /paddingLeft\s*:\s*(\d+)/g,
      replacement: "paddingStart: $1 // RTL: Changed from paddingLeft",
      description: "Changed paddingLeft to paddingStart for RTL compatibility"
    },
    paddingRightToEnd: {
      pattern: /paddingRight\s*:\s*(\d+)/g,
      replacement: "paddingEnd: $1 // RTL: Changed from paddingRight",
      description: "Changed paddingRight to paddingEnd for RTL compatibility"
    }
  }
};

class RTLLayoutFixer {
  constructor() {
    this.results = {
      fixedFiles: [],
      errors: [],
      totalFilesProcessed: 0,
      totalFixes: 0,
      fixesByType: {}
    };
  }

  fixFile(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      let newContent = content;
      let totalFixes = 0;
      const fileFixes = [];

      // Apply each fix
      Object.entries(config.fixes).forEach(([fixName, fixConfig]) => {
        const matches = newContent.match(fixConfig.pattern);
        if (matches) {
          newContent = newContent.replace(fixConfig.pattern, fixConfig.replacement);
          const fixCount = matches.length;
          totalFixes += fixCount;
          
          fileFixes.push({
            type: fixName,
            count: fixCount,
            description: fixConfig.description
          });
          
          if (!this.results.fixesByType[fixName]) {
            this.results.fixesByType[fixName] = 0;
          }
          this.results.fixesByType[fixName] += fixCount;
        }
      });

      // Only write if changes were made
      if (totalFixes > 0) {
        fs.writeFileSync(filePath, newContent);
        
        this.results.fixedFiles.push({
          path: path.relative(process.cwd(), filePath),
          fixes: totalFixes,
          details: fileFixes
        });
        
        this.results.totalFixes += totalFixes;
        console.log(`✅ Fixed: ${path.relative(process.cwd(), filePath)} (${totalFixes} fixes)`);
      }

      this.results.totalFilesProcessed++;
      
    } catch (error) {
      this.results.errors.push({
        file: path.relative(process.cwd(), filePath),
        error: error.message
      });
      console.error(`❌ Error fixing ${filePath}:`, error.message);
    }
  }

  processDirectory(dirPath) {
    if (!fs.existsSync(dirPath)) {
      console.log(`Directory not found: ${dirPath}`);
      return;
    }

    const items = fs.readdirSync(dirPath);
    
    for (const item of items) {
      const fullPath = path.join(dirPath, item);
      
      // Skip certain directories and files
      if (/node_modules|\.git|\.expo|dist|build|\.DS_Store/.test(item)) {
        continue;
      }
      
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        this.processDirectory(fullPath);
      } else if (stat.isFile() && config.fileExtensions.some(ext => fullPath.endsWith(ext))) {
        // Skip RTL component files themselves
        if (!/RTL.*\.tsx?$/.test(fullPath)) {
          this.fixFile(fullPath);
        }
      }
    }
  }

  generateReport() {
    const timestamp = new Date().toLocaleString();
    
    let report = `# RTL Layout Fixes Report\n\n`;
    report += `**Generated:** ${timestamp}\n`;
    report += `**Files Processed:** ${this.results.totalFilesProcessed}\n`;
    report += `**Files Fixed:** ${this.results.fixedFiles.length}\n`;
    report += `**Total Fixes Applied:** ${this.results.totalFixes}\n\n`;
    
    if (this.results.totalFixes === 0) {
      report += `## ✅ No Fixes Needed\n\n`;
      report += `All scanned files appear to be using RTL-compatible patterns already.\n\n`;
      return report;
    }
    
    // Fixes summary
    report += `## 📊 Fixes Summary\n\n`;
    report += `| Fix Type | Count | Description |\n`;
    report += `|----------|-------|-------------|\n`;
    
    Object.entries(this.results.fixesByType).forEach(([type, count]) => {
      const description = config.fixes[type]?.description || 'RTL layout fix';
      report += `| ${type} | ${count} | ${description} |\n`;
    });
    report += `\n`;
    
    // Detailed file analysis
    if (this.results.fixedFiles.length > 0) {
      report += `## 📁 Fixed Files\n\n`;
      
      this.results.fixedFiles.forEach(file => {
        report += `### ${file.path}\n\n`;
        report += `**Total fixes:** ${file.fixes}\n\n`;
        
        file.details.forEach(fix => {
          report += `- **${fix.type}** (${fix.count} occurrence${fix.count > 1 ? 's' : ''}): ${fix.description}\n`;
        });
        report += `\n`;
      });
    }
    
    if (this.results.errors.length > 0) {
      report += `## ❌ Errors\n\n`;
      
      this.results.errors.forEach(error => {
        report += `- **${error.file}:** ${error.error}\n`;
      });
      report += `\n`;
    }
    
    // Next steps
    report += `## 🔄 Next Steps\n\n`;
    report += `After applying these fixes, you should:\n\n`;
    report += `1. **Test the app** in both LTR and RTL modes\n`;
    report += `2. **Review icon directionality** - Some directional icons may need manual attention\n`;
    report += `3. **Check horizontal scrolling** - Ensure lists start from the correct side in RTL\n`;
    report += `4. **Validate text alignment** - Verify that text appears correctly in both directions\n`;
    report += `5. **Test navigation flow** - Ensure navigation feels natural in RTL mode\n\n`;
    
    report += `## 💡 Manual Review Needed\n\n`;
    report += `Some issues require manual review:\n\n`;
    report += `- **Directional Icons**: Icons like chevron-forward, arrow-back need context-aware handling\n`;
    report += `- **Complex Layouts**: Multi-column layouts may need custom RTL logic\n`;
    report += `- **Animations**: Transform animations should be RTL-aware\n`;
    report += `- **Horizontal Scrolling**: FlatList and ScrollView horizontal behavior\n\n`;
    
    return report;
  }

  run() {
    console.log('🔧 Starting RTL layout fixes...');
    
    // Process directories
    [config.srcDir, config.appDir, config.componentsDir].forEach(dir => {
      if (fs.existsSync(dir)) {
        console.log(`Processing: ${dir}`);
        this.processDirectory(dir);
      }
    });
    
    // Generate and save report
    const report = this.generateReport();
    fs.writeFileSync(config.outputFile, report);
    
    console.log(`\n📄 Report saved to: ${config.outputFile}`);
    console.log(`📊 Files processed: ${this.results.totalFilesProcessed}`);
    console.log(`✅ Files fixed: ${this.results.fixedFiles.length}`);
    console.log(`🔧 Total fixes: ${this.results.totalFixes}`);
    
    if (this.results.errors.length > 0) {
      console.log(`❌ Errors: ${this.results.errors.length}`);
    }
    
    return this.results.fixedFiles.length;
  }
}

// Run the fixer
if (require.main === module) {
  const fixer = new RTLLayoutFixer();
  const fixedCount = fixer.run();
  console.log(`\n🎉 RTL layout fixes completed! Fixed ${fixedCount} files.`);
  process.exit(0);
}

module.exports = RTLLayoutFixer;
