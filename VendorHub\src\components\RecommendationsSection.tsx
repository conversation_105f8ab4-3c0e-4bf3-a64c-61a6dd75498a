import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  ActivityIndicator } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useThemedStyles, useAuth, useProducts, useOrders, useI18n } from '../hooks';
import { Card } from './Card';
import { StatusBadge } from './StatusBadge';
import { RTLView, RTLText, RTLScrollView, RTLTouchableOpacity, RTLIcon } from './RTL';
import RecommendationService, { ProductRecommendation, RecommendationContext } from '../services/RecommendationService';
import {
  SPACING,
  FONT_SIZES,
  FONT_WEIGHTS,
  BORDER_RADIUS } from '../constants/theme';
import { formatCurrency } from '../utils';
import type ThemeColors  from '../contexts/ThemeContext';
import type Product  from '../contexts/DataContext';

interface RecommendationsSectionProps {
  title?: string;
  context?: RecommendationContext;
  limit?: number;
  style?: any;
  horizontal?: boolean;
  showReason?: boolean;
  onProductPress?: (product: Product) => void;
}

export const RecommendationsSection: React.FC<RecommendationsSectionProps> = ({
  title,
  context,
  limit = 10,
  style,
  horizontal = true,
  showReason = false,
  onProductPress,
}) => {
  const styles = useThemedStyles(createStyles);
  const navigation = useNavigation();
  const { user } = useAuth();
  const { getAllProducts } = useProducts();
  const { getAllOrders } = useOrders();
  const { t } = useI18n();

  const [recommendations, setRecommendations] = useState<ProductRecommendation[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Use translation for default title
  const displayTitle = title || t('home.recommendedForYou');

  useEffect(() => {
    loadRecommendations();
  }, [user, context]);

  const loadRecommendations = async () => {
    if (!user) {
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      const products = getAllProducts();
      const orders = getAllOrders();

      const recs = await RecommendationService.getRecommendations(
        user.id,
        products,
        orders,
        context,
        limit
      );

      setRecommendations(recs);
    } catch (err) {
      console.error('Error loading recommendations:', err);
      setError(t('recommendations.failedToLoadRecommendations'));
    } finally {
      setIsLoading(false);
    }
  };

  const handleProductPress = (product: Product) => {
    if (onProductPress) {
      onProductPress(product);
    } else {
      (navigation as any).navigate('ProductDetails', { productId: product.id });
    }

    // Track the view
    if (user) {
      RecommendationService.trackProductView(
        user.id,
        product.id,
        product.category,
        product.vendorId
      );
    }
  };

  const getReasonText = (reason: ProductRecommendation['reason']) => {
    switch (reason) {
      case 'viewed_similar': return t('recommendations.viewedSimilar');
      case 'bought_together': return t('recommendations.boughtTogether');
      case 'trending': return t('recommendations.trending');
      case 'category_match': return t('recommendations.categoryMatch');
      case 'vendor_match': return t('recommendations.vendorMatch');
      case 'price_range': return t('recommendations.priceRange');
      case 'collaborative': return t('recommendations.collaborative');
      default: return t('recommendations.recommended');
    }
  };

  const getReasonIcon = (reason: ProductRecommendation['reason']) => {
    switch (reason) {
      case 'viewed_similar': return 'eye-outline';
      case 'bought_together': return 'bag-outline';
      case 'trending': return 'trending-up-outline';
      case 'category_match': return 'heart-outline';
      case 'vendor_match': return 'storefront-outline';
      case 'price_range': return 'pricetag-outline';
      case 'collaborative': return 'people-outline';
      default: return 'star-outline';
    }
  };

  const renderProductCard = (recommendation: ProductRecommendation, index: number) => {
    const { product, reason, confidence } = recommendation;

    return (
      <RTLTouchableOpacity
        key={product.id}
        style={[
          styles.productCard,
          horizontal && styles.horizontalCard,
          !horizontal && index % 2 === 1 && styles.rightCard,
        ]}
        onPress={() => handleProductPress(product)}
      >
        <Card style={styles.cardContent} variant="elevated">
          <RTLView style={styles.imageContainer}>
            <RTLView style={styles.imagePlaceholder}>
              <RTLIcon name="image-outline" size={32} color="#CCCCCC" />
            </RTLView>

            {product.originalPrice && product.originalPrice > product.price && (
              <RTLView style={styles.discountBadge}>
                <RTLText style={styles.discountText}>
                  {Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)}% OFF
                </RTLText>
              </RTLView>
            )}

            {showReason && (
              <RTLView style={styles.reasonBadge}>
                <RTLIcon
                  name={getReasonIcon(reason) as any}
                  size={12}
                  color="#FFFFFF"
                />
              </RTLView>
            )}
          </RTLView>

          <RTLView style={styles.productInfo}>
            <RTLText style={styles.productName} numberOfLines={2}>
              {product.name}
            </RTLText>

            <RTLView style={styles.priceContainer}>
              <RTLText style={styles.currentPrice}>
                {formatCurrency(product.price)}
              </RTLText>
              {product.originalPrice && product.originalPrice > product.price && (
                <RTLText style={styles.originalPrice}>
                  {formatCurrency(product.originalPrice)}
                </RTLText>
              )}
            </RTLView>

            <RTLView style={styles.productMeta}>
              <RTLView style={styles.rating}>
                <RTLIcon name="star" size={12} color="#FFD700" />
                <RTLText style={styles.ratingText}>{(product.rating || 0).toFixed(1)}</RTLText>
              </RTLView>

              <StatusBadge
                status={product.inventory > 0 ? t('products.inStock') : t('products.outOfStock')}
                size="small"
              />
            </RTLView>

            {showReason && (
              <RTLText style={styles.reasonText} numberOfLines={1}>
                {getReasonText(reason)}
              </RTLText>
            )}
          </RTLView>
        </Card>
      </RTLTouchableOpacity>
    );
  };

  const renderContent = () => {
    if (isLoading) {
      return (
        <RTLView style={styles.loadingContainer}>
          <ActivityIndicator size="small" color="#667eea" />
          <RTLText style={styles.loadingText}>{t('recommendations.loadingRecommendations')}</RTLText>
        </RTLView>
      );
    }

    if (error) {
      return (
        <RTLView style={styles.errorContainer}>
          <RTLIcon name="alert-circle-outline" size={24} color="#F44336" />
          <RTLText style={styles.errorText}>{error}</RTLText>
          <RTLTouchableOpacity onPress={loadRecommendations} style={styles.retryButton}>
            <RTLText style={styles.retryText}>{t('recommendations.retry')}</RTLText>
          </RTLTouchableOpacity>
        </RTLView>
      );
    }

    if (recommendations.length === 0) {
      return (
        <RTLView style={styles.emptyContainer}>
          <RTLIcon name="bulb-outline" size={24} color="#999" />
          <RTLText style={styles.emptyText}>{t('recommendations.noRecommendationsAvailable')}</RTLText>
          <RTLText style={styles.emptySubtext}>
            {t('recommendations.browseMoreForRecommendations')}
          </RTLText>
        </RTLView>
      );
    }

    if (horizontal) {
      return (
        <RTLScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.horizontalContainer}
          enableRTLScrolling={true}
        >
          {recommendations.map((rec, index) => renderProductCard(rec, index))}
        </RTLScrollView>
      );
    }

    return (
      <RTLView style={styles.gridContainer}>
        {recommendations.map((rec, index) => renderProductCard(rec, index))}
      </RTLView>
    );
  };

  if (!user) {
    return null;
  }

  return (
    <RTLView style={[styles.container, style]}>
      <RTLView style={styles.header}>
        <RTLText style={styles.title}>{displayTitle}</RTLText>
        {recommendations.length > 0 && (
          <RTLTouchableOpacity onPress={loadRecommendations} style={styles.refreshButton}>
            <RTLIcon name="refresh-outline" size={20} color="#667eea" />
          </RTLTouchableOpacity>
        )}
      </RTLView>

      {renderContent()}
    </RTLView>
  );
};

const createStyles = (colors: ThemeColors) => StyleSheet.create({
  container: {
    marginVertical: SPACING.md,
  },
  header: {
    flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: SPACING.lg,
    marginBottom: SPACING.md,
  },
  title: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.semiBold,
    color: colors.text,
  },
  refreshButton: {
    padding: SPACING.sm,
  },
  loadingContainer: {
    flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: SPACING.xl,
    gap: SPACING.sm,
  },
  loadingText: {
    fontSize: FONT_SIZES.sm,
    color: colors.textSecondary,
  },
  errorContainer: {
    alignItems: 'center',
    paddingVertical: SPACING.xl,
    paddingHorizontal: SPACING.lg,
    gap: SPACING.sm,
  },
  errorText: {
    fontSize: FONT_SIZES.sm,
    color: '#F44336',
    textAlign: 'center',
  },
  retryButton: {
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    backgroundColor: '#667eea',
    borderRadius: BORDER_RADIUS.md,
    marginTop: SPACING.sm,
  },
  retryText: {
    fontSize: FONT_SIZES.sm,
    color: '#FFFFFF',
    fontWeight: FONT_WEIGHTS.medium,
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: SPACING.xl,
    paddingHorizontal: SPACING.lg,
    gap: SPACING.sm,
  },
  emptyText: {
    fontSize: FONT_SIZES.md,
    color: colors.textSecondary,
    fontWeight: FONT_WEIGHTS.medium,
  },
  emptySubtext: {
    fontSize: FONT_SIZES.sm,
    color: colors.textSecondary,
    textAlign: 'center',
  },
  horizontalContainer: {
    paddingHorizontal: SPACING.lg,
    gap: SPACING.md,
  },
  gridContainer: {
    flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
    flexWrap: 'wrap',
    paddingHorizontal: SPACING.lg,
    gap: SPACING.md,
  },
  productCard: {
    width: 160,
  },
  horizontalCard: {
    width: 160,
  },
  rightCard: {
    marginHorizontal: SPACING.md,
  },
  cardContent: {
    padding: SPACING.md,
  },
  imageContainer: {
    position: 'relative',
    marginBottom: SPACING.sm,
  },
  imagePlaceholder: {
    width: '100%',
    height: 120,
    backgroundColor: colors.surface,
    borderRadius: BORDER_RADIUS.md,
    justifyContent: 'center',
    alignItems: 'center',
  },
  discountBadge: {
    position: 'absolute',
    top: SPACING.xs,
    left: SPACING.xs,
    backgroundColor: '#FF4444',
    paddingHorizontal: SPACING.xs,
    paddingVertical: 2,
    borderRadius: BORDER_RADIUS.sm,
  },
  discountText: {
    fontSize: FONT_SIZES.xs,
    fontWeight: FONT_WEIGHTS.bold,
    color: '#FFFFFF',
  },
  reasonBadge: {
    position: 'absolute',
    top: SPACING.xs,
    right: SPACING.xs,
    backgroundColor: 'rgba(102, 126, 234, 0.9)',
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  productInfo: {
    gap: SPACING.xs,
  },
  productName: {
    fontSize: FONT_SIZES.sm,
    fontWeight: FONT_WEIGHTS.medium,
    color: colors.text,
    lineHeight: 18,
  },
  priceContainer: {
    flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
    alignItems: 'center',
    gap: SPACING.xs,
  },
  currentPrice: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.semiBold,
    color: '#4CAF50',
  },
  originalPrice: {
    fontSize: FONT_SIZES.sm,
    color: colors.textSecondary,
    textDecorationLine: 'line-through',
  },
  productMeta: {
    flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  rating: {
    flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
    alignItems: 'center',
    gap: SPACING.xs,
  },
  ratingText: {
    fontSize: FONT_SIZES.xs,
    color: colors.textSecondary,
  },
  reasonText: {
    fontSize: FONT_SIZES.xs,
    color: '#667eea',
    fontStyle: 'italic',
  },
});
