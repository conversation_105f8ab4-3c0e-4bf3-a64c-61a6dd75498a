import React, { useState, useEffect } from 'react';
import { StyleSheet, Alert, KeyboardAvoidingView, Platform } from 'react-native';
import { useThemedStyles, useAuth, useVendors, useI18n } from '../../hooks';
import { Card, Button, Input, LoadingSpinner, ImagePicker, SingleImagePicker } from '../../components';
import { RTLView, RTLText, RTLScrollView, RTLSafeAreaView, RTLIcon } from '../../components/RTL';
import {
  SPACING,
  FONT_SIZES,
  FONT_WEIGHTS,
  BORDER_RADIUS } from '../../constants/theme';
import type { ThemeColors } from '../../contexts/ThemeContext';
import type { Vendor } from '../../contexts/DataContext';

interface ShopSettingsScreenProps {
  navigation: any;
}

interface ShopFormData {
  businessName: string;
  businessDescription: string;
  phone: string;
  address: string;
  website: string;
  socialMedia: {
    facebook: string;
    instagram: string;
    twitter: string;
  };
  businessHours: string;
  logo: string | null;
  coverPhoto: string | null;
  businessPhotos: string[];
}

export const ShopSettingsScreen: React.FC<ShopSettingsScreenProps> = ({ navigation }) => {
  const styles = useThemedStyles(createStyles);
  const { user } = useAuth();
  const { getVendorById } = useVendors();
  const { t } = useI18n();
  
  const [isLoading, setIsLoading] = useState(false);
  const [vendor, setVendor] = useState<Vendor | null>(null);
  const [formData, setFormData] = useState<ShopFormData>({
    businessName: '',
    businessDescription: '',
    phone: '',
    address: '',
    website: '',
    socialMedia: {
      facebook: '',
      instagram: '',
      twitter: '',
    },
    businessHours: '',
    logo: null,
    coverPhoto: null,
    businessPhotos: [],
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    loadVendorData();
  }, []);

  const loadVendorData = () => {
    if (user?.id) {
      const vendorData = getVendorById(user.id);
      if (vendorData) {
        setVendor(vendorData);
        setFormData({
          businessName: vendorData.businessName || '',
          businessDescription: vendorData.businessDescription || '',
          phone: vendorData.phone || '',
          address: typeof vendorData.address === 'string' ? vendorData.address :
            vendorData.address ? `${vendorData.address.street}, ${vendorData.address.city}, ${vendorData.address.state}` : '',
          website: '',
          socialMedia: {
            facebook: '',
            instagram: '',
            twitter: '',
          },
          businessHours: '',
          logo: null,
          coverPhoto: null,
          businessPhotos: [],
        });
      }
    }
  };

  const handleInputChange = (field: string, value: any) => {
    if (field.includes('.')) {
      const [parent, child] = field.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...(prev[parent as keyof ShopFormData] as any),
          [child]: value,
        },
      }));
    } else {
      setFormData(prev => ({ ...prev, [field]: value }));
    }
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.businessName.trim()) {
      newErrors.businessName = t('vendor.businessNameRequired');
    }

    if (!formData.businessDescription.trim()) {
      newErrors.businessDescription = t('vendor.businessDescriptionRequired');
    }

    if (!formData.phone.trim()) {
      newErrors.phone = t('vendor.phoneRequired');
    }

    if (formData.website && !formData.website.match(/^https?:\/\/.+/)) {
      newErrors.website = t('vendor.websiteInvalid');
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    if (!validateForm()) return;

    setIsLoading(true);
    try {
      const updatedVendorData = {
        ...(vendor as any),
        businessName: formData.businessName.trim(),
        businessDescription: formData.businessDescription.trim(),
        phone: formData.phone.trim(),
        // Only update address if it's a simple string for now
        ...(typeof vendor?.address === 'string' ? { address: formData.address.trim() } : {}),
        updatedAt: new Date().toISOString(),
      };

      // TODO: Implement updateVendor function in useVendors hook
      console.log('Updated vendor data:', updatedVendorData);
      
      Alert.alert(
        t('common.success'),
        t('vendor.shopSettingsUpdated'),
        [{ text: t('common.ok'), onPress: () => navigation.goBack() }]
      );
    } catch (error) {
      Alert.alert(t('common.error'), t('vendor.failedToUpdateShop'));
    } finally {
      setIsLoading(false);
    }
  };

  if (!vendor) {
    return (
      <RTLSafeAreaView style={styles.container}>
        <LoadingSpinner />
      </RTLSafeAreaView>
    );
  }

  return (
    <RTLSafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        style={styles.keyboardAvoid}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <RTLScrollView
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          <RTLView style={styles.content}>
            {/* Business Logo */}
            <Card style={styles.section} variant="outlined">
              <RTLText style={styles.sectionTitle}>{t('vendor.businessLogo')}</RTLText>

              <SingleImagePicker
                image={formData.logo}
                onImageChange={(image) => handleInputChange('logo', image)}
                title={t('vendor.businessLogo')}
                subtitle={t('vendor.uploadBusinessLogo')}
                placeholder={t('vendor.tapToAddLogo')}
                aspectRatio={[1, 1]}
                size="large"
                shape="square"
              />

              <RTLText style={styles.helperText}>
                {t('vendor.logoHelperText')}
              </RTLText>
            </Card>

            {/* Cover Photo */}
            <Card style={styles.section} variant="outlined">
              <RTLText style={styles.sectionTitle}>{t('vendor.coverPhoto')}</RTLText>

              <SingleImagePicker
                image={formData.coverPhoto}
                onImageChange={(image) => handleInputChange('coverPhoto', image)}
                title={t('vendor.coverPhoto')}
                subtitle={t('vendor.uploadCoverPhoto')}
                placeholder={t('vendor.tapToAddCoverPhoto')}
                aspectRatio={[16, 9]}
                size="large"
                shape="rectangle"
              />

              <RTLText style={styles.helperText}>
                {t('vendor.coverPhotoHelperText')}
              </RTLText>
            </Card>

            {/* Business Photos */}
            <Card style={styles.section} variant="outlined">
              <RTLText style={styles.sectionTitle}>{t('vendor.businessPhotos')}</RTLText>

              <ImagePicker
                images={formData.businessPhotos}
                onImagesChange={(images) => handleInputChange('businessPhotos', images)}
                maxImages={8}
                title={t('vendor.businessPhotos')}
                subtitle={t('vendor.showcaseBusinessPhotos')}
              />

              <RTLText style={styles.helperText}>
                {t('vendor.businessPhotosHelperText')}
              </RTLText>
            </Card>

            {/* Basic Information */}
            <Card style={styles.section} variant="outlined">
              <RTLText style={styles.sectionTitle}>{t('vendor.basicInformation')}</RTLText>
              
              <Input
                label={t('vendor.businessNameRequired')}
                placeholder={t('vendor.enterBusinessName')}
                value={formData.businessName}
                onChangeText={(value) => handleInputChange('businessName', value)}
                error={errors.businessName}
                style={styles.input}
              />

              <Input
                label={t('vendor.businessDescriptionRequired')}
                placeholder={t('vendor.describeYourBusiness')}
                value={formData.businessDescription}
                onChangeText={(value) => handleInputChange('businessDescription', value)}
                error={errors.businessDescription}
                multiline
                numberOfLines={4}
                style={styles.input}
              />

              <Input
                label={t('vendor.phoneNumberRequired')}
                placeholder={t('vendor.enterPhoneNumber')}
                value={formData.phone}
                onChangeText={(value) => handleInputChange('phone', value)}
                error={errors.phone}
                keyboardType="phone-pad"
                style={styles.input}
              />

              <Input
                label={t('vendor.businessAddress')}
                placeholder={t('vendor.enterBusinessAddress')}
                value={formData.address}
                onChangeText={(value) => handleInputChange('address', value)}
                multiline
                numberOfLines={2}
                style={styles.input}
              />

              <Input
                label={t('vendor.website')}
                placeholder={t('vendor.websitePlaceholder')}
                value={formData.website}
                onChangeText={(value) => handleInputChange('website', value)}
                error={errors.website}
                keyboardType="url"
                autoCapitalize="none"
                style={styles.input}
              />

              <Input
                label={t('vendor.businessHours')}
                placeholder={t('vendor.businessHoursPlaceholder')}
                value={formData.businessHours}
                onChangeText={(value) => handleInputChange('businessHours', value)}
                style={styles.input}
              />
            </Card>

            {/* Social Media */}
            <Card style={styles.section} variant="outlined">
              <RTLText style={styles.sectionTitle}>{t('vendor.socialMedia')}</RTLText>

              <Input
                label={t('vendor.facebook')}
                placeholder={t('vendor.facebookPlaceholder')}
                value={formData.socialMedia.facebook}
                onChangeText={(value) => handleInputChange('socialMedia.facebook', value)}
                keyboardType="url"
                autoCapitalize="none"
                style={styles.input}
              />

              <Input
                label={t('vendor.instagram')}
                placeholder={t('vendor.instagramPlaceholder')}
                value={formData.socialMedia.instagram}
                onChangeText={(value) => handleInputChange('socialMedia.instagram', value)}
                keyboardType="url"
                autoCapitalize="none"
                style={styles.input}
              />

              <Input
                label={t('vendor.twitter')}
                placeholder={t('vendor.twitterPlaceholder')}
                value={formData.socialMedia.twitter}
                onChangeText={(value) => handleInputChange('socialMedia.twitter', value)}
                keyboardType="url"
                autoCapitalize="none"
                style={styles.input}
              />
            </Card>

            {/* Action Buttons */}
            <RTLView style={styles.actionButtons}>
              <Button
                title={t('common.cancel')}
                onPress={() => navigation.goBack()}
                variant="outline"
                style={styles.cancelButton}
              />
              <Button
                title={t('common.saveChanges')}
                onPress={handleSave}
                loading={isLoading}
                style={styles.saveButton}
                leftIcon={<RTLIcon name="checkmark-outline" size={20} color="#FFFFFF" />}
              />
            </RTLView>
          </RTLView>
        </RTLScrollView>
      </KeyboardAvoidingView>
    </RTLSafeAreaView>
  );
};

const createStyles = (colors: ThemeColors) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  keyboardAvoid: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingBottom: SPACING.xl,
  },
  content: {
    padding: SPACING.lg,
  },
  section: {
    marginBottom: SPACING.lg,
  },
  sectionTitle: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.semibold,
    color: colors.text,
    marginBottom: SPACING.md,
  },
  input: {
    marginBottom: SPACING.md,
  },
  helperText: {
    fontSize: FONT_SIZES.sm,
    color: colors.textSecondary,
    marginTop: SPACING.sm,
    lineHeight: 20,
  },
  actionButtons: {
    flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
    justifyContent: 'space-between',
    marginTop: SPACING.xl,
    gap: SPACING.md,
  },
  cancelButton: {
    flex: 1,
  },
  saveButton: {
    flex: 1,
  },
});
