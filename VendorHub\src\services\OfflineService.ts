import NetInfo from '@react-native-community/netinfo';
import { Alert } from 'react-native';
import type { Product, Vendor, Order } from '../contexts/DataContext';
import type User  from '../contexts/AuthContext';
import { storage } from '../utils/storage';

export interface CachedData {
  products: Product[];
  vendors: Vendor[];
  orders: Order[];
  user: User | null;
  lastUpdated: string;
  version: string;
}

export interface OfflineAction {
  id: string;
  type: 'CREATE_PRODUCT' | 'UPDATE_PRODUCT' | 'DELETE_PRODUCT' | 'UPDATE_ORDER' | 'CREATE_ORDER';
  data: any;
  timestamp: string;
  userId: string;
  retryCount: number;
}

class OfflineService {
  private static instance: OfflineService;
  private isOnline: boolean = true;
  private pendingActions: OfflineAction[] = [];
  private cachedData: CachedData | null = null;
  private syncInProgress: boolean = false;

  private constructor() {
    this.initializeOfflineSupport();
  }

  public static getInstance(): OfflineService {
    if (!OfflineService.instance) {
      OfflineService.instance = new OfflineService();
    }
    return OfflineService.instance;
  }

  private async initializeOfflineSupport() {
    // Monitor network connectivity
    NetInfo.addEventListener(state => {
      const wasOffline = !this.isOnline;
      this.isOnline = state.isConnected ?? false;
      
      if (wasOffline && this.isOnline) {
        // Just came back online, sync pending actions
        this.syncPendingActions();
      }
    });

    // Load cached data and pending actions
    await this.loadCachedData();
    await this.loadPendingActions();
  }

  // Cache Management
  public async cacheData(data: Partial<CachedData>) {
    try {
      const existingCache = await this.getCachedData();
      const updatedCache: CachedData = {
        products: data.products || existingCache?.products || [],
        vendors: data.vendors || existingCache?.vendors || [],
        orders: data.orders || existingCache?.orders || [],
        user: data.user !== undefined ? data.user : existingCache?.user || null,
        lastUpdated: new Date().toISOString(),
        version: '1.0.0',
      };

      await storage.setItem('cachedData', JSON.stringify(updatedCache));
      this.cachedData = updatedCache;
    } catch (error) {
      console.error('Error caching data:', error);
    }
  }

  public async getCachedData(): Promise<CachedData | null> {
    if (this.cachedData) {
      return this.cachedData;
    }

    try {
      const cached = await storage.getItem('cachedData');
      if (cached) {
        this.cachedData = JSON.parse(cached);
        return this.cachedData;
      }
    } catch (error) {
      console.error('Error loading cached data:', error);
    }

    return null;
  }

  private async loadCachedData() {
    this.cachedData = await this.getCachedData();
  }

  // Offline Actions Management
  public async addOfflineAction(action: Omit<OfflineAction, 'id' | 'timestamp' | 'retryCount'>) {
    const offlineAction: OfflineAction = {
      ...action,
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      timestamp: new Date().toISOString(),
      retryCount: 0,
    };

    this.pendingActions.push(offlineAction);
    await this.savePendingActions();

    // If online, try to sync immediately
    if (this.isOnline) {
      this.syncPendingActions();
    }
  }

  private async loadPendingActions() {
    try {
      const stored = await storage.getItem('pendingActions');
      if (stored) {
        this.pendingActions = JSON.parse(stored);
      }
    } catch (error) {
      console.error('Error loading pending actions:', error);
    }
  }

  private async savePendingActions() {
    try {
      await storage.setItem('pendingActions', JSON.stringify(this.pendingActions));
    } catch (error) {
      console.error('Error saving pending actions:', error);
    }
  }

  private async syncPendingActions() {
    if (this.syncInProgress || !this.isOnline || this.pendingActions.length === 0) {
      return;
    }

    this.syncInProgress = true;

    try {
      const actionsToSync = [...this.pendingActions];
      const successfulActions: string[] = [];

      for (const action of actionsToSync) {
        try {
          await this.executeAction(action);
          successfulActions.push(action.id);
        } catch (error) {
          console.error(`Failed to sync action ${action.id}:`, error);
          
          // Increment retry count
          action.retryCount++;
          
          // Remove action if it has failed too many times
          if (action.retryCount >= 3) {
            successfulActions.push(action.id);
            console.warn(`Removing action ${action.id} after 3 failed attempts`);
          }
        }
      }

      // Remove successful actions
      this.pendingActions = this.pendingActions.filter(
        action => !successfulActions.includes(action.id)
      );

      await this.savePendingActions();

      if (successfulActions.length > 0) {
        console.log(`Successfully synced ${successfulActions.length} offline actions`);
      }
    } finally {
      this.syncInProgress = false;
    }
  }

  private async executeAction(action: OfflineAction): Promise<void> {
    // This would integrate with your actual API calls
    // For now, we'll simulate the execution
    switch (action.type) {
      case 'CREATE_PRODUCT':
        // await apiService.createProduct(action.data);
        console.log('Executing CREATE_PRODUCT:', action.data);
        break;
      case 'UPDATE_PRODUCT':
        // await apiService.updateProduct(action.data.id, action.data);
        console.log('Executing UPDATE_PRODUCT:', action.data);
        break;
      case 'DELETE_PRODUCT':
        // await apiService.deleteProduct(action.data.id);
        console.log('Executing DELETE_PRODUCT:', action.data);
        break;
      case 'UPDATE_ORDER':
        // await apiService.updateOrder(action.data.id, action.data);
        console.log('Executing UPDATE_ORDER:', action.data);
        break;
      case 'CREATE_ORDER':
        // await apiService.createOrder(action.data);
        console.log('Executing CREATE_ORDER:', action.data);
        break;
      default:
        throw new Error(`Unknown action type: ${action.type}`);
    }
  }

  // Public API
  public isOffline(): boolean {
    return !this.isOnline;
  }

  public async getCachedProducts(): Promise<Product[]> {
    const cached = await this.getCachedData();
    return cached?.products || [];
  }

  public async getCachedVendors(): Promise<Vendor[]> {
    const cached = await this.getCachedData();
    return cached?.vendors || [];
  }

  public async getCachedOrders(): Promise<Order[]> {
    const cached = await this.getCachedData();
    return cached?.orders || [];
  }

  public async getCachedUser(): Promise<User | null> {
    const cached = await this.getCachedData();
    return cached?.user || null;
  }

  public getPendingActionsCount(): number {
    return this.pendingActions.length;
  }

  public async clearCache() {
    try {
      await storage.removeItem('cachedData');
      this.cachedData = null;
    } catch (error) {
      console.error('Error clearing cache:', error);
    }
  }

  public async clearPendingActions() {
    try {
      await storage.removeItem('pendingActions');
      this.pendingActions = [];
    } catch (error) {
      console.error('Error clearing pending actions:', error);
    }
  }

  public async getStorageInfo() {
    try {
      const cachedDataSize = await storage.getItem('cachedData');
      const pendingActionsSize = await storage.getItem('pendingActions');
      
      return {
        cachedDataSize: cachedDataSize ? new Blob([cachedDataSize]).size : 0,
        pendingActionsSize: pendingActionsSize ? new Blob([pendingActionsSize]).size : 0,
        pendingActionsCount: this.pendingActions.length,
        lastCacheUpdate: this.cachedData?.lastUpdated || null,
      };
    } catch (error) {
      console.error('Error getting storage info:', error);
      return {
        cachedDataSize: 0,
        pendingActionsSize: 0,
        pendingActionsCount: 0,
        lastCacheUpdate: null,
      };
    }
  }

  // Offline-first operations
  public async createProductOffline(productData: Partial<Product>, userId: string) {
    // Add to cache immediately for optimistic UI
    const cached = await this.getCachedData();
    if (cached) {
      const newProduct: Product = {
        ...productData as Product,
        id: Date.now().toString(),
        createdAt: new Date().toISOString(),
      };
      
      cached.products.unshift(newProduct);
      await this.cacheData({ products: cached.products });
    }

    // Queue for sync when online
    await this.addOfflineAction({
      type: 'CREATE_PRODUCT',
      data: productData,
      userId,
    });
  }

  public async updateProductOffline(productId: string, updates: Partial<Product>, userId: string) {
    // Update cache immediately
    const cached = await this.getCachedData();
    if (cached) {
      const productIndex = cached.products.findIndex(p => p.id === productId);
      if (productIndex !== -1) {
        cached.products[productIndex] = { ...cached.products[productIndex], ...updates };
        await this.cacheData({ products: cached.products });
      }
    }

    // Queue for sync when online
    await this.addOfflineAction({
      type: 'UPDATE_PRODUCT',
      data: { id: productId, ...updates },
      userId,
    });
  }

  public async deleteProductOffline(productId: string, userId: string) {
    // Remove from cache immediately
    const cached = await this.getCachedData();
    if (cached) {
      cached.products = cached.products.filter(p => p.id !== productId);
      await this.cacheData({ products: cached.products });
    }

    // Queue for sync when online
    await this.addOfflineAction({
      type: 'DELETE_PRODUCT',
      data: { id: productId },
      userId,
    });
  }

  public showOfflineMessage() {
    Alert.alert(
      'Offline Mode',
      'You are currently offline. Changes will be saved and synced when you reconnect to the internet.',
      [{ text: 'OK' }]
    );
  }
}

export default OfflineService.getInstance();
