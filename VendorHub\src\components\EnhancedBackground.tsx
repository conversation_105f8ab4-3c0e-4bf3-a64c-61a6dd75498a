import React from 'react';
import { StyleSheet, ViewStyle } from 'react-native';
import { RTLView } from './RTL';
import { DarkGradientBackground, DarkGradientVariant } from './DarkGradientBackground';
import { MoonlightEffects, SubtleGlow, TwinklingStars } from './MoonlightEffects';

export interface EnhancedBackgroundProps {
  variant?: DarkGradientVariant;
  effectType?: 'moonlight' | 'glow' | 'stars' | 'combined' | 'none';
  intensity?: 'subtle' | 'gentle' | 'moderate';
  children?: React.ReactNode;
  style?: ViewStyle;
}

export const EnhancedBackground: React.FC<EnhancedBackgroundProps> = ({
  variant = 'elegant',
  effectType = 'combined',
  intensity = 'subtle',
  children,
  style,
}) => {
  const renderEffects = () => {
    switch (effectType) {
      case 'moonlight':
        return (
          <MoonlightEffects intensity={intensity} particleCount={8}>
            {children}
          </MoonlightEffects>
        );
      
      case 'glow':
        return (
          <SubtleGlow>
            {children}
          </SubtleGlow>
        );
      
      case 'stars':
        return (
          <TwinklingStars starCount={6}>
            {children}
          </TwinklingStars>
        );
      
      case 'combined':
        return (
          <SubtleGlow>
            <TwinklingStars starCount={4}>
              <MoonlightEffects intensity={intensity} particleCount={6}>
                {children}
              </MoonlightEffects>
            </TwinklingStars>
          </SubtleGlow>
        );
      
      case 'none':
      default:
        return children;
    }
  };

  return (
    <DarkGradientBackground variant={variant} style={[styles.container, style]}>
      {renderEffects()}
    </DarkGradientBackground>
  );
};

// خلفية للشاشة الرئيسية مع تأثيرات خفيفة
export const HomeBackground: React.FC<{ children?: React.ReactNode }> = ({ children }) => {
  return (
    <EnhancedBackground
      variant="elegant"
      effectType="combined"
      intensity="subtle"
    >
      {children}
    </EnhancedBackground>
  );
};

// خلفية للشاشات الترحيبية مع تأثيرات أقوى
export const WelcomeBackground: React.FC<{ children?: React.ReactNode }> = ({ children }) => {
  return (
    <EnhancedBackground
      variant="nebula"
      effectType="combined"
      intensity="gentle"
    >
      {children}
    </EnhancedBackground>
  );
};

// خلفية للشاشات التجارية مع تأثيرات مهنية
export const BusinessBackground: React.FC<{ children?: React.ReactNode }> = ({ children }) => {
  return (
    <EnhancedBackground
      variant="royal"
      effectType="glow"
      intensity="subtle"
    >
      {children}
    </EnhancedBackground>
  );
};

// خلفية للشاشات الليلية مع نجوم
export const NightBackground: React.FC<{ children?: React.ReactNode }> = ({ children }) => {
  return (
    <EnhancedBackground
      variant="void"
      effectType="stars"
      intensity="gentle"
    >
      {children}
    </EnhancedBackground>
  );
};

// خلفية للشاشات الخاصة مع تأثيرات قمرية
export const PremiumBackground: React.FC<{ children?: React.ReactNode }> = ({ children }) => {
  return (
    <EnhancedBackground
      variant="abyss"
      effectType="moonlight"
      intensity="moderate"
    >
      {children}
    </EnhancedBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

export default EnhancedBackground;
