import React, { useState } from 'react';
import {
  StyleSheet,
  Modal } from 'react-native';
import Slider from '@react-native-community/slider';
import { useThemedStyles } from '../../hooks';
import { Card } from '../Card';
import { Button } from '../Button';
import { RTLView, RTLText, RTLTouchableOpacity, RTLScrollView, RTLIcon } from '../RTL';
import {
  SPACING,
  FONT_SIZES,
  FONT_WEIGHTS,
  BORDER_RADIUS } from '../../constants/theme';
import { formatCurrency } from '../../utils';
import { PRODUCT_CATEGORIES, type ProductCategory  } from '../../constants';
import type ThemeColors  from '../../contexts/ThemeContext';

export interface FilterOptions {
  categories: ProductCategory[];
  priceRange: {
    min: number;
    max: number;
  };
  rating: number;
  inStock: boolean;
  onSale: boolean;
  vendors: string[];
  sortBy: 'relevance' | 'price_low' | 'price_high' | 'rating' | 'newest';
}

export interface FilterPanelProps {
  visible: boolean;
  onClose: () => void;
  filters: FilterOptions;
  onFiltersChange: (filters: FilterOptions) => void;
  onApply: () => void;
  onReset: () => void;
  availableVendors?: Array<{ id: string; name: string; productCount: number }>;
  priceRange?: { min: number; max: number };
}

export const FilterPanel: React.FC<FilterPanelProps> = ({
  visible,
  onClose,
  filters,
  onFiltersChange,
  onApply,
  onReset,
  availableVendors = [],
  priceRange = { min: 0, max: 1000 },
}) => {
  const styles = useThemedStyles(createStyles);
  const [tempFilters, setTempFilters] = useState<FilterOptions>(filters);

  const updateTempFilters = (updates: Partial<FilterOptions>) => {
    setTempFilters(prev => ({ ...prev, ...updates }));
  };

  const handleCategoryToggle = (category: ProductCategory) => {
    const categories = tempFilters.categories.includes(category)
      ? tempFilters.categories.filter(c => c !== category)
      : [...tempFilters.categories, category];
    updateTempFilters({ categories });
  };

  const handleVendorToggle = (vendorId: string) => {
    const vendors = tempFilters.vendors.includes(vendorId)
      ? tempFilters.vendors.filter(v => v !== vendorId)
      : [...tempFilters.vendors, vendorId];
    updateTempFilters({ vendors });
  };

  const handleApply = () => {
    onFiltersChange(tempFilters);
    onApply();
    onClose();
  };

  const handleReset = () => {
    const resetFilters: FilterOptions = {
      categories: [],
      priceRange: { min: priceRange.min, max: priceRange.max },
      rating: 0,
      inStock: false,
      onSale: false,
      vendors: [],
      sortBy: 'relevance',
    };
    setTempFilters(resetFilters);
    onFiltersChange(resetFilters);
    onReset();
  };

  const getActiveFiltersCount = () => {
    let count = 0;
    if (tempFilters.categories.length > 0) count++;
    if (tempFilters.priceRange.min > priceRange.min || tempFilters.priceRange.max < priceRange.max) count++;
    if (tempFilters.rating > 0) count++;
    if (tempFilters.inStock) count++;
    if (tempFilters.onSale) count++;
    if (tempFilters.vendors.length > 0) count++;
    if (tempFilters.sortBy !== 'relevance') count++;
    return count;
  };

  const renderSection = (title: string, children: React.ReactNode) => (
    <RTLView style={styles.section}>
      <RTLText style={styles.sectionTitle}>{title}</RTLText>
      {children}
    </RTLView>
  );

  const renderCategories = () => (
    <RTLView style={styles.optionsGrid}>
      {Object.values(PRODUCT_CATEGORIES).map((category) => (
        <RTLTouchableOpacity
          key={category}
          style={[
            styles.optionChip,
            tempFilters.categories.includes(category) && styles.optionChipActive,
          ]}
          onPress={() => handleCategoryToggle(category)}
        >
          <RTLText
            style={[
              styles.optionChipText,
              tempFilters.categories.includes(category) && styles.optionChipTextActive,
            ]}
          >
            {category}
          </RTLText>
        </RTLTouchableOpacity>
      ))}
    </RTLView>
  );

  const renderPriceRange = () => (
    <RTLView style={styles.priceRangeContainer}>
      <RTLView style={styles.priceLabels}>
        <RTLText style={styles.priceLabel}>
          {formatCurrency(tempFilters.priceRange.min)}
        </RTLText>
        <RTLText style={styles.priceLabel}>
          {formatCurrency(tempFilters.priceRange.max)}
        </RTLText>
      </RTLView>
      <RTLView style={styles.slidersContainer}>
        <RTLText style={styles.sliderLabel}>Min Price</RTLText>
        <Slider
          style={styles.slider}
          minimumValue={priceRange.min}
          maximumValue={priceRange.max}
          value={tempFilters.priceRange.min}
          onValueChange={(value) =>
            updateTempFilters({
              priceRange: { ...tempFilters.priceRange, min: Math.round(value) },
            })
          }
          minimumTrackTintColor="#667eea"
          maximumTrackTintColor="#CCCCCC"
        />
        <RTLText style={styles.sliderLabel}>Max Price</RTLText>
        <Slider
          style={styles.slider}
          minimumValue={priceRange.min}
          maximumValue={priceRange.max}
          value={tempFilters.priceRange.max}
          onValueChange={(value) =>
            updateTempFilters({
              priceRange: { ...tempFilters.priceRange, max: Math.round(value) },
            })
          }
          minimumTrackTintColor="#667eea"
          maximumTrackTintColor="#CCCCCC"
        />
      </RTLView>
    </RTLView>
  );

  const renderRating = () => (
    <RTLView style={styles.ratingContainer}>
      {[1, 2, 3, 4, 5].map((rating) => (
        <RTLTouchableOpacity
          key={rating}
          style={styles.ratingOption}
          onPress={() => updateTempFilters({ rating })}
        >
          <RTLView style={styles.stars}>
            {[1, 2, 3, 4, 5].map((star) => (
              <RTLIcon
                key={star}
                name="star"
                size={16}
                color={star <= rating ? "#FFD700" : "#CCCCCC"}
              />
            ))}
          </RTLView>
          <RTLText style={styles.ratingText}>& up</RTLText>
          {tempFilters.rating === rating && (
            <RTLIcon name="checkmark-circle" size={20} color="#4CAF50" />
          )}
        </RTLTouchableOpacity>
      ))}
    </RTLView>
  );

  const renderToggleOptions = () => (
    <RTLView style={styles.toggleContainer}>
      <RTLTouchableOpacity
        style={styles.toggleOption}
        onPress={() => updateTempFilters({ inStock: !tempFilters.inStock })}
      >
        <RTLView style={styles.toggleContent}>
          <RTLIcon name="cube-outline" size={20} color="#667eea" />
          <RTLText style={styles.toggleText}>In Stock Only</RTLText>
        </RTLView>
        <RTLView style={[styles.toggle, tempFilters.inStock && styles.toggleActive]}>
          <RTLView style={[styles.toggleThumb, tempFilters.inStock && styles.toggleThumbActive]} />
        </RTLView>
      </RTLTouchableOpacity>

      <RTLTouchableOpacity
        style={styles.toggleOption}
        onPress={() => updateTempFilters({ onSale: !tempFilters.onSale })}
      >
        <RTLView style={styles.toggleContent}>
          <RTLIcon name="pricetag-outline" size={20} color="#667eea" />
          <RTLText style={styles.toggleText}>On Sale</RTLText>
        </RTLView>
        <RTLView style={[styles.toggle, tempFilters.onSale && styles.toggleActive]}>
          <RTLView style={[styles.toggleThumb, tempFilters.onSale && styles.toggleThumbActive]} />
        </RTLView>
      </RTLTouchableOpacity>
    </RTLView>
  );

  const renderVendors = () => (
    <RTLView style={styles.vendorsContainer}>
      {availableVendors.slice(0, 6).map((vendor) => (
        <RTLTouchableOpacity
          key={vendor.id}
          style={[
            styles.vendorChip,
            tempFilters.vendors.includes(vendor.id) && styles.vendorChipActive,
          ]}
          onPress={() => handleVendorToggle(vendor.id)}
        >
          <RTLText
            style={[
              styles.vendorChipText,
              tempFilters.vendors.includes(vendor.id) && styles.vendorChipTextActive,
            ]}
          >
            {vendor.name}
          </RTLText>
          <RTLText style={styles.vendorProductCount}>({vendor.productCount})</RTLText>
        </RTLTouchableOpacity>
      ))}
    </RTLView>
  );

  const renderSortBy = () => {
    const sortOptions = [
      { value: 'relevance', label: 'Relevance' },
      { value: 'price_low', label: 'Price: Low to High' },
      { value: 'price_high', label: 'Price: High to Low' },
      { value: 'rating', label: 'Customer Rating' },
      { value: 'newest', label: 'Newest First' },
    ];

    return (
      <RTLView style={styles.sortContainer}>
        {sortOptions.map((option) => (
          <RTLTouchableOpacity
            key={option.value}
            style={[
              styles.sortOption,
              tempFilters.sortBy === option.value && styles.sortOptionActive,
            ]}
            onPress={() => updateTempFilters({ sortBy: option.value as any })}
          >
            <RTLText
              style={[
                styles.sortOptionText,
                tempFilters.sortBy === option.value && styles.sortOptionTextActive,
              ]}
            >
              {option.label}
            </RTLText>
            {tempFilters.sortBy === option.value && (
              <RTLIcon name="checkmark" size={16} color="#FFFFFF" />
            )}
          </RTLTouchableOpacity>
        ))}
      </RTLView>
    );
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <RTLView style={styles.container}>
        {/* Header */}
        <RTLView style={styles.header}>
          <RTLTouchableOpacity onPress={onClose}>
            <RTLIcon name="close" size={24} color="#667eea" />
          </RTLTouchableOpacity>
          <RTLText style={styles.headerTitle}>Filters</RTLText>
          <RTLTouchableOpacity onPress={handleReset}>
            <RTLText style={styles.resetText}>Reset</RTLText>
          </RTLTouchableOpacity>
        </RTLView>

        {/* Content */}
        <RTLScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {renderSection('Categories', renderCategories())}
          {renderSection('Price Range', renderPriceRange())}
          {renderSection('Customer Rating', renderRating())}
          {renderSection('Availability', renderToggleOptions())}
          {availableVendors.length > 0 && renderSection('Vendors', renderVendors())}
          {renderSection('Sort By', renderSortBy())}
        </RTLScrollView>

        {/* Footer */}
        <RTLView style={styles.footer}>
          <Button
            title="Clear All"
            onPress={handleReset}
            variant="outline"
            style={styles.clearButton}
          />
          <Button
            title={`Apply${getActiveFiltersCount() > 0 ? ` (${getActiveFiltersCount()})` : ''}`}
            onPress={handleApply}
            style={styles.applyButton}
          />
        </RTLView>
      </RTLView>
    </Modal>
  );
};

const createStyles = (colors: ThemeColors) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    header: {
      flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: SPACING.lg,
      paddingVertical: SPACING.md,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    headerTitle: {
      fontSize: FONT_SIZES.lg,
      fontWeight: FONT_WEIGHTS.bold,
      color: colors.textPrimary,
    },
    resetText: {
      fontSize: FONT_SIZES.md,
      color: colors.primary,
      fontWeight: FONT_WEIGHTS.medium,
    },
    content: {
      flex: 1,
      paddingHorizontal: SPACING.lg,
    },
    section: {
      marginVertical: SPACING.lg,
    },
    sectionTitle: {
      fontSize: FONT_SIZES.md,
      fontWeight: FONT_WEIGHTS.semibold,
      color: colors.textPrimary,
      marginBottom: SPACING.md,
    },
    optionsGrid: {
      flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
      flexWrap: 'wrap',
      gap: SPACING.sm,
    },
    optionChip: {
      paddingHorizontal: SPACING.md,
      paddingVertical: SPACING.sm,
      borderRadius: BORDER_RADIUS.full,
      backgroundColor: colors.surface,
      borderWidth: 1,
      borderColor: colors.border,
    },
    optionChipActive: {
      backgroundColor: '#667eea',
      borderColor: '#667eea',
    },
    optionChipText: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
      fontWeight: FONT_WEIGHTS.medium,
    },
    optionChipTextActive: {
      color: '#FFFFFF',
    },
    priceRangeContainer: {
      marginVertical: SPACING.sm,
    },
    priceLabels: {
      flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
      justifyContent: 'space-between',
      marginBottom: SPACING.sm,
    },
    priceLabel: {
      fontSize: FONT_SIZES.lg,
      fontWeight: FONT_WEIGHTS.bold,
      color: '#667eea',
    },
    slidersContainer: {
      marginVertical: SPACING.sm,
    },
    sliderLabel: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
      marginBottom: SPACING.xs,
    },
    slider: {
      width: '100%',
      height: 40,
      marginBottom: SPACING.md,
    },
    sliderThumb: {
      backgroundColor: '#667eea',
      width: 20,
      height: 20,
    },
    ratingContainer: {
      gap: SPACING.sm,
    },
    ratingOption: {
      flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
      alignItems: 'center',
      paddingVertical: SPACING.sm,
      paddingHorizontal: SPACING.md,
      borderRadius: BORDER_RADIUS.md,
      backgroundColor: colors.surface,
    },
    stars: {
      flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
      marginRight: SPACING.sm,
    },
    ratingText: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
      flex: 1,
    },
    toggleContainer: {
      gap: SPACING.md,
    },
    toggleOption: {
      flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingVertical: SPACING.sm,
    },
    toggleContent: {
      flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
      alignItems: 'center',
      flex: 1,
    },
    toggleText: {
      fontSize: FONT_SIZES.md,
      color: colors.textPrimary,
      marginLeft: SPACING.sm,
    },
    toggle: {
      width: 50,
      height: 30,
      borderRadius: 15,
      backgroundColor: colors.border,
      justifyContent: 'center',
      padding: 2,
    },
    toggleActive: {
      backgroundColor: '#4CAF50',
    },
    toggleThumb: {
      width: 26,
      height: 26,
      borderRadius: 13,
      backgroundColor: '#FFFFFF',
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
      elevation: 5,
    },
    toggleThumbActive: {
      transform: [{ translateX: 20 }],
    },
    vendorsContainer: {
      flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
      flexWrap: 'wrap',
      gap: SPACING.sm,
    },
    vendorChip: {
      flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
      alignItems: 'center',
      paddingHorizontal: SPACING.md,
      paddingVertical: SPACING.sm,
      borderRadius: BORDER_RADIUS.full,
      backgroundColor: colors.surface,
      borderWidth: 1,
      borderColor: colors.border,
    },
    vendorChipActive: {
      backgroundColor: '#667eea',
      borderColor: '#667eea',
    },
    vendorChipText: {
      fontSize: FONT_SIZES.sm,
      color: colors.textPrimary,
      fontWeight: FONT_WEIGHTS.medium,
    },
    vendorChipTextActive: {
      color: '#FFFFFF',
    },
    vendorProductCount: {
      fontSize: FONT_SIZES.xs,
      color: colors.textSecondary,
      marginLeft: SPACING.xs,
    },
    sortContainer: {
      gap: SPACING.sm,
    },
    sortOption: {
      flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingVertical: SPACING.md,
      paddingHorizontal: SPACING.md,
      borderRadius: BORDER_RADIUS.md,
      backgroundColor: colors.surface,
      borderWidth: 1,
      borderColor: colors.border,
    },
    sortOptionActive: {
      backgroundColor: '#667eea',
      borderColor: '#667eea',
    },
    sortOptionText: {
      fontSize: FONT_SIZES.md,
      color: colors.textPrimary,
      fontWeight: FONT_WEIGHTS.medium,
    },
    sortOptionTextActive: {
      color: '#FFFFFF',
    },
    footer: {
      flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
      paddingHorizontal: SPACING.lg,
      paddingVertical: SPACING.md,
      borderTopWidth: 1,
      borderTopColor: colors.border,
      gap: SPACING.md,
    },
    clearButton: {
      flex: 1,
    },
    applyButton: {
      flex: 2,
    },
  });
