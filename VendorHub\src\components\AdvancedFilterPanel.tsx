import React, { useState } from 'react';
import {
  StyleSheet,
  Modal,
  Switch } from 'react-native';
import Slider from '@react-native-community/slider';
import { useThemedStyles, useI18n } from '../hooks';
import { Card } from './Card';
import { Button } from './Button';
import { RTLView, RTLText, RTLScrollView, RTLTouchableOpacity, RTLIcon } from './RTL';
import {
  SPACING,
  FONT_SIZES,
  FONT_WEIGHTS,
  BORDER_RADIUS } from '../constants/theme';
import { PRODUCT_CATEGORIES, type ProductCategory  } from '../constants';
import { formatCurrency } from '../utils';
import type ThemeColors  from '../contexts/ThemeContext';

export interface AdvancedFilterOptions {
  categories: ProductCategory[];
  priceRange: { min: number; max: number };
  rating: number;
  inStock: boolean;
  onSale: boolean;
  freeShipping: boolean;
  vendors: string[];
  sortBy: 'relevance' | 'price_low' | 'price_high' | 'rating' | 'newest' | 'popularity' | 'discount';
  availability: 'all' | 'in_stock' | 'low_stock' | 'out_of_stock';
  condition: 'all' | 'new' | 'refurbished' | 'used';
}

interface AdvancedFilterPanelProps {
  visible: boolean;
  onClose: () => void;
  filters: AdvancedFilterOptions;
  onFiltersChange: (filters: AdvancedFilterOptions) => void;
  priceRange: { min: number; max: number };
  availableVendors: Array<{ id: string; name: string; productCount: number }>;
  onApply: () => void;
  onReset: () => void;
}

export const AdvancedFilterPanel: React.FC<AdvancedFilterPanelProps> = ({
  visible,
  onClose,
  filters,
  onFiltersChange,
  priceRange,
  availableVendors,
  onApply,
  onReset,
}) => {
  const styles = useThemedStyles(createStyles);
  const { t } = useI18n();
  const [tempFilters, setTempFilters] = useState<AdvancedFilterOptions>(filters);

  const updateTempFilters = (updates: Partial<AdvancedFilterOptions>) => {
    setTempFilters(prev => ({ ...prev, ...updates }));
  };

  const handleApply = () => {
    onFiltersChange(tempFilters);
    onApply();
    onClose();
  };

  const handleReset = () => {
    const resetFilters: AdvancedFilterOptions = {
      categories: [],
      priceRange: { min: priceRange.min, max: priceRange.max },
      rating: 0,
      inStock: false,
      onSale: false,
      freeShipping: false,
      vendors: [],
      sortBy: 'relevance',
      availability: 'all',
      condition: 'all',
    };
    setTempFilters(resetFilters);
    onFiltersChange(resetFilters);
    onReset();
  };

  const categoryOptions = PRODUCT_CATEGORIES as readonly ProductCategory[];
  const sortOptions = [
    { key: 'relevance', label: t('search.relevance'), icon: 'search-outline' },
    { key: 'price_low', label: t('search.priceLowToHigh'), icon: 'arrow-up-outline' },
    { key: 'price_high', label: t('search.priceHighToLow'), icon: 'arrow-down-outline' },
    { key: 'rating', label: t('search.customerRating'), icon: 'star-outline' },
    { key: 'newest', label: t('search.newestFirst'), icon: 'time-outline' },
    { key: 'popularity', label: t('search.mostPopular'), icon: 'trending-up-outline' },
    { key: 'discount', label: t('search.biggestDiscount'), icon: 'pricetag-outline' },
  ];

  const availabilityOptions = [
    { key: 'all', label: t('search.allProducts') },
    { key: 'in_stock', label: t('search.inStock') },
    { key: 'low_stock', label: t('search.lowStock') },
    { key: 'out_of_stock', label: t('search.outOfStock') },
  ];

  const conditionOptions = [
    { key: 'all', label: t('search.allConditions') },
    { key: 'new', label: t('search.new') },
    { key: 'refurbished', label: t('search.refurbished') },
    { key: 'used', label: t('search.used') },
  ];

  const renderSection = (title: string, children: React.ReactNode) => (
    <RTLView style={styles.section}>
      <RTLText style={styles.sectionTitle}>{title}</RTLText>
      {children}
    </RTLView>
  );

  const renderCategoryFilter = () => (
    renderSection(t('search.categories'), (
      <RTLView style={styles.categoryGrid}>
        {categoryOptions.map((category) => (
          <RTLTouchableOpacity
            key={category}
            style={[
              styles.categoryChip,
              tempFilters.categories.includes(category) && styles.categoryChipActive,
            ]}
            onPress={() => {
              const newCategories = tempFilters.categories.includes(category)
                ? tempFilters.categories.filter(c => c !== category)
                : [...tempFilters.categories, category];
              updateTempFilters({ categories: newCategories });
            }}
          >
            <RTLText
              style={[
                styles.categoryChipText,
                tempFilters.categories.includes(category) && styles.categoryChipTextActive,
              ]}
            >
              {category}
            </RTLText>
          </RTLTouchableOpacity>
        ))}
      </RTLView>
    ))
  );

  const renderPriceFilter = () => (
    renderSection(t('search.priceRange'), (
      <RTLView style={styles.priceContainer}>
        <RTLView style={styles.priceLabels}>
          <RTLText style={styles.priceLabel}>
            {formatCurrency(tempFilters.priceRange.min)}
          </RTLText>
          <RTLText style={styles.priceLabel}>
            {formatCurrency(tempFilters.priceRange.max)}
          </RTLText>
        </RTLView>
        <RTLView style={styles.sliderContainer}>
          <RTLText style={styles.sliderLabel}>Min</RTLText>
          <Slider
            style={styles.slider}
            minimumValue={priceRange.min}
            maximumValue={priceRange.max}
            value={tempFilters.priceRange.min}
            onValueChange={(value) => 
              updateTempFilters({ 
                priceRange: { ...tempFilters.priceRange, min: Math.round(value) }
              })
            }
            minimumTrackTintColor="#667eea"
            maximumTrackTintColor="#CCCCCC"
          />
        </RTLView>
        <RTLView style={styles.sliderContainer}>
          <RTLText style={styles.sliderLabel}>Max</RTLText>
          <Slider
            style={styles.slider}
            minimumValue={priceRange.min}
            maximumValue={priceRange.max}
            value={tempFilters.priceRange.max}
            onValueChange={(value) => 
              updateTempFilters({ 
                priceRange: { ...tempFilters.priceRange, max: Math.round(value) }
              })
            }
            minimumTrackTintColor="#667eea"
            maximumTrackTintColor="#CCCCCC"
          />
        </RTLView>
      </RTLView>
    ))
  );

  const renderRatingFilter = () => (
    renderSection(t('search.minimumRating'), (
      <RTLView style={styles.ratingContainer}>
        {[1, 2, 3, 4, 5].map((rating) => (
          <RTLTouchableOpacity
            key={rating}
            style={[
              styles.ratingOption,
              tempFilters.rating >= rating && styles.ratingOptionActive,
            ]}
            onPress={() => updateTempFilters({ rating: rating === tempFilters.rating ? 0 : rating })}
          >
            <RTLIcon
              name={tempFilters.rating >= rating ? "star" : "star-outline"}
              size={24}
              color={tempFilters.rating >= rating ? "#FFD700" : "#CCCCCC"}
            />
          </RTLTouchableOpacity>
        ))}
        <RTLText style={styles.ratingText}>
          {tempFilters.rating > 0 ? `${tempFilters.rating}${t('search.starsAndUp')}` : t('search.anyRating')}
        </RTLText>
      </RTLView>
    ))
  );

  const renderToggleFilters = () => (
    renderSection(t('search.quickFilters'), (
      <RTLView style={styles.toggleContainer}>
        <RTLView style={styles.toggleRow}>
          <RTLText style={styles.toggleLabel}>In Stock Only</RTLText>
          <Switch
            value={tempFilters.inStock}
            onValueChange={(value) => updateTempFilters({ inStock: value })}
            trackColor={{ false: '#CCCCCC', true: '#667eea' }}
            thumbColor="#FFFFFF"
          />
        </RTLView>
        <RTLView style={styles.toggleRow}>
          <RTLText style={styles.toggleLabel}>On Sale</RTLText>
          <Switch
            value={tempFilters.onSale}
            onValueChange={(value) => updateTempFilters({ onSale: value })}
            trackColor={{ false: '#CCCCCC', true: '#667eea' }}
            thumbColor="#FFFFFF"
          />
        </RTLView>
        <RTLView style={styles.toggleRow}>
          <RTLText style={styles.toggleLabel}>Free Shipping</RTLText>
          <Switch
            value={tempFilters.freeShipping}
            onValueChange={(value) => updateTempFilters({ freeShipping: value })}
            trackColor={{ false: '#CCCCCC', true: '#667eea' }}
            thumbColor="#FFFFFF"
          />
        </RTLView>
      </RTLView>
    ))
  );

  const renderSortOptions = () => (
    renderSection(t('search.sortBy'), (
      <RTLView style={styles.sortContainer}>
        {sortOptions.map((option) => (
          <RTLTouchableOpacity
            key={option.key}
            style={[
              styles.sortOption,
              tempFilters.sortBy === option.key && styles.sortOptionActive,
            ]}
            onPress={() => updateTempFilters({ sortBy: option.key as any })}
          >
            <RTLIcon
              name={option.icon as any}
              size={20}
              color={tempFilters.sortBy === option.key ? "#667eea" : "#666"}
            />
            <RTLText
              style={[
                styles.sortOptionText,
                tempFilters.sortBy === option.key && styles.sortOptionTextActive,
              ]}
            >
              {option.label}
            </RTLText>
          </RTLTouchableOpacity>
        ))}
      </RTLView>
    ))
  );

  const renderVendorFilter = () => (
    renderSection(t('search.vendors'), (
      <RTLView style={styles.vendorContainer}>
        {availableVendors.slice(0, 10).map((vendor) => (
          <RTLTouchableOpacity
            key={vendor.id}
            style={[
              styles.vendorOption,
              tempFilters.vendors.includes(vendor.id) && styles.vendorOptionActive,
            ]}
            onPress={() => {
              const newVendors = tempFilters.vendors.includes(vendor.id)
                ? tempFilters.vendors.filter(v => v !== vendor.id)
                : [...tempFilters.vendors, vendor.id];
              updateTempFilters({ vendors: newVendors });
            }}
          >
            <RTLText
              style={[
                styles.vendorOptionText,
                tempFilters.vendors.includes(vendor.id) && styles.vendorOptionTextActive,
              ]}
            >
              {vendor.name}
            </RTLText>
            <RTLText style={styles.vendorProductCount}>
              {vendor.productCount} products
            </RTLText>
          </RTLTouchableOpacity>
        ))}
      </RTLView>
    ))
  );

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <RTLView style={styles.container}>
        <RTLView style={styles.header}>
          <RTLTouchableOpacity onPress={onClose} style={styles.closeButton}>
            <RTLIcon name="close" size={24} color="#666" />
          </RTLTouchableOpacity>
          <RTLText style={styles.title}>Advanced Filters</RTLText>
          <RTLTouchableOpacity onPress={handleReset} style={styles.resetButton}>
            <RTLText style={styles.resetText}>Reset</RTLText>
          </RTLTouchableOpacity>
        </RTLView>

        <RTLScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {renderCategoryFilter()}
          {renderPriceFilter()}
          {renderRatingFilter()}
          {renderToggleFilters()}
          {renderSortOptions()}
          {renderVendorFilter()}
        </RTLScrollView>

        <RTLView style={styles.footer}>
          <Button
            title="Apply Filters"
            onPress={handleApply}
            style={styles.applyButton}
          />
        </RTLView>
      </RTLView>
    </Modal>
  );
};

const createStyles = (colors: ThemeColors) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  closeButton: {
    padding: SPACING.sm,
  },
  title: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.semiBold,
    color: colors.text,
  },
  resetButton: {
    padding: SPACING.sm,
  },
  resetText: {
    fontSize: FONT_SIZES.md,
    color: colors.primary,
    fontWeight: FONT_WEIGHTS.medium,
  },
  content: {
    flex: 1,
    paddingHorizontal: SPACING.lg,
  },
  section: {
    marginVertical: SPACING.lg,
  },
  sectionTitle: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.semiBold,
    color: colors.text,
    marginBottom: SPACING.md,
  },
  categoryGrid: {
    flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
    flexWrap: 'wrap',
    gap: SPACING.sm,
  },
  categoryChip: {
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    borderRadius: BORDER_RADIUS.full,
    backgroundColor: colors.surface,
    borderWidth: 1,
    borderColor: colors.border,
  },
  categoryChipActive: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  categoryChipText: {
    fontSize: FONT_SIZES.sm,
    color: colors.text,
    fontWeight: FONT_WEIGHTS.medium,
  },
  categoryChipTextActive: {
    color: '#FFFFFF',
  },
  priceContainer: {
    gap: SPACING.md,
  },
  priceLabels: {
    flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
    justifyContent: 'space-between',
  },
  priceLabel: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.semiBold,
    color: '#667eea',
  },
  sliderContainer: {
    flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
    alignItems: 'center',
    gap: SPACING.md,
  },
  sliderLabel: {
    fontSize: FONT_SIZES.sm,
    color: colors.textSecondary,
    width: 30,
  },
  slider: {
    flex: 1,
    height: 40,
  },
  sliderThumb: {
    backgroundColor: '#667eea',
    width: 20,
    height: 20,
  },
  ratingContainer: {
    flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
    alignItems: 'center',
    gap: SPACING.sm,
  },
  ratingOption: {
    padding: SPACING.xs,
  },
  ratingOptionActive: {
    backgroundColor: 'rgba(102, 126, 234, 0.1)',
    borderRadius: BORDER_RADIUS.sm,
  },
  ratingText: {
    fontSize: FONT_SIZES.sm,
    color: colors.textSecondary,
    marginLeft: SPACING.md,
  },
  toggleContainer: {
    gap: SPACING.md,
  },
  toggleRow: {
    flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: SPACING.sm,
  },
  toggleLabel: {
    fontSize: FONT_SIZES.md,
    color: colors.text,
  },
  sortContainer: {
    gap: SPACING.sm,
  },
  sortOption: {
    flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
    alignItems: 'center',
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.md,
    borderRadius: BORDER_RADIUS.md,
    backgroundColor: colors.surface,
    gap: SPACING.md,
  },
  sortOptionActive: {
    backgroundColor: 'rgba(102, 126, 234, 0.1)',
    borderWidth: 1,
    borderColor: '#667eea',
  },
  sortOptionText: {
    fontSize: FONT_SIZES.md,
    color: colors.text,
  },
  sortOptionTextActive: {
    color: '#667eea',
    fontWeight: FONT_WEIGHTS.medium,
  },
  vendorContainer: {
    gap: SPACING.sm,
  },
  vendorOption: {
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.md,
    borderRadius: BORDER_RADIUS.md,
    backgroundColor: colors.surface,
    borderWidth: 1,
    borderColor: colors.border,
  },
  vendorOptionActive: {
    backgroundColor: 'rgba(102, 126, 234, 0.1)',
    borderColor: '#667eea',
  },
  vendorOptionText: {
    fontSize: FONT_SIZES.md,
    color: colors.text,
    fontWeight: FONT_WEIGHTS.medium,
  },
  vendorOptionTextActive: {
    color: '#667eea',
  },
  vendorProductCount: {
    fontSize: FONT_SIZES.sm,
    color: colors.textSecondary,
    marginTop: SPACING.xs,
  },
  footer: {
    padding: SPACING.lg,
    borderTopWidth: 1,
    borderTopColor: colors.border,
  },
  applyButton: {
    marginBottom: 0,
  },
});
