import React from 'react';
import { StyleSheet, ViewStyle } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import Svg, { Path, Circle, Rect, Defs, LinearGradient as SvgLinearGradient, Stop } from 'react-native-svg';
import { RTLView, RTLText } from './RTL';
import { useThemedStyles } from '../hooks';
import { FONT_SIZES, FONT_WEIGHTS, SPACING } from '../constants/theme';
import { BRAND_COLORS, PREMIUM_GRADIENTS } from '../constants/advancedColors';
import type ThemeColors  from '../contexts/ThemeContext';

export interface LogoProps {
  size?: 'small' | 'medium' | 'large' | 'xlarge';
  variant?: 'full' | 'icon' | 'text';
  showText?: boolean;
  style?: ViewStyle;
  color?: 'primary' | 'white' | 'dark';
}

export const Logo: React.FC<LogoProps> = ({
  size = 'medium',
  variant = 'full',
  showText = true,
  style,
  color = 'primary',
}) => {
  const styles = useThemedStyles(createStyles);

  const getSizes = () => {
    switch (size) {
      case 'small':
        return { iconSize: 32, fontSize: FONT_SIZES.md, spacing: SPACING.xs };
      case 'medium':
        return { iconSize: 48, fontSize: FONT_SIZES.lg, spacing: SPACING.sm };
      case 'large':
        return { iconSize: 64, fontSize: FONT_SIZES.xl, spacing: SPACING.md };
      case 'xlarge':
        return { iconSize: 80, fontSize: FONT_SIZES.xxl, spacing: SPACING.lg };
      default:
        return { iconSize: 48, fontSize: FONT_SIZES.lg, spacing: SPACING.sm };
    }
  };

  const getColors = () => {
    switch (color) {
      case 'white':
        return { primary: '#FFFFFF', secondary: '#F1F5F9', text: '#FFFFFF' };
      case 'dark':
        return { primary: '#0F172A', secondary: '#1E293B', text: '#0F172A' };
      default:
        return { primary: BRAND_COLORS.primary, secondary: BRAND_COLORS.accent, text: BRAND_COLORS.primary };
    }
  };

  const { iconSize, fontSize, spacing } = getSizes();
  const colors = getColors();

  // شعار الأيقونة - يمثل متاجر متعددة
  const LogoIcon = () => (
    <Svg width={iconSize} height={iconSize} viewBox="0 0 100 100">
      <Defs>
        <SvgLinearGradient id="shopGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <Stop offset="0%" stopColor={colors.primary} />
          <Stop offset="100%" stopColor={colors.secondary} />
        </SvgLinearGradient>
        <SvgLinearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <Stop offset="0%" stopColor="#3B82F6" />
          <Stop offset="100%" stopColor="#1D4ED8" />
        </SvgLinearGradient>
      </Defs>
      
      {/* المتجر الرئيسي */}
      <Rect
        x="25"
        y="35"
        width="50"
        height="45"
        rx="8"
        fill="url(#shopGradient)"
        stroke={colors.secondary}
        strokeWidth="2"
      />
      
      {/* سقف المتجر */}
      <Path
        d="M20 35 L50 15 L80 35 L75 35 L50 20 L25 35 Z"
        fill="url(#accentGradient)"
        stroke={colors.secondary}
        strokeWidth="1"
      />
      
      {/* المتجر الثاني (صغير) */}
      <Rect
        x="10"
        y="50"
        width="25"
        height="30"
        rx="6"
        fill="url(#shopGradient)"
        opacity="0.8"
        stroke={colors.secondary}
        strokeWidth="1.5"
      />
      
      {/* المتجر الثالث (صغير) */}
      <Rect
        x="65"
        y="50"
        width="25"
        height="30"
        rx="6"
        fill="url(#shopGradient)"
        opacity="0.8"
        stroke={colors.secondary}
        strokeWidth="1.5"
      />
      
      {/* نوافذ المتجر الرئيسي */}
      <Rect x="35" y="45" width="8" height="8" rx="2" fill="#FFFFFF" opacity="0.9" />
      <Rect x="57" y="45" width="8" height="8" rx="2" fill="#FFFFFF" opacity="0.9" />
      
      {/* باب المتجر */}
      <Rect x="45" y="60" width="10" height="20" rx="3" fill="#FFFFFF" opacity="0.9" />
      <Circle cx="52" cy="70" r="1" fill={colors.primary} />
      
      {/* نجوم التقييم */}
      <Path d="M15 25 L17 30 L22 30 L18 33 L20 38 L15 35 L10 38 L12 33 L8 30 L13 30 Z" fill="#FFD700" />
      <Path d="M85 25 L87 30 L92 30 L88 33 L90 38 L85 35 L80 38 L82 33 L78 30 L83 30 Z" fill="#FFD700" />
      
      {/* خط الاتصال بين المتاجر */}
      <Path d="M35 65 Q50 55 65 65" stroke={colors.secondary} strokeWidth="2" fill="none" opacity="0.6" />
    </Svg>
  );

  // نص الشعار
  const LogoText = () => (
    <RTLText style={[styles.logoText, { fontSize, color: colors.text }]}>
      محلات
    </RTLText>
  );

  if (variant === 'icon') {
    return (
      <RTLView style={[styles.container, style]}>
        <LogoIcon />
      </RTLView>
    );
  }

  if (variant === 'text') {
    return (
      <RTLView style={[styles.container, style]}>
        <LogoText />
      </RTLView>
    );
  }

  return (
    <RTLView style={[styles.container, styles.fullLogo, { gap: spacing }, style]}>
      <LogoIcon />
      {showText && <LogoText />}
    </RTLView>
  );
};

// شعار متحرك للشاشات الخاصة
export interface AnimatedLogoProps {
  size?: 'medium' | 'large' | 'xlarge';
  style?: ViewStyle;
}

export const AnimatedLogo: React.FC<AnimatedLogoProps> = ({
  size = 'large',
  style,
}) => {
  const styles = useThemedStyles(createStyles);

  return (
    <LinearGradient
      colors={PREMIUM_GRADIENTS.aurora}
      style={[styles.animatedContainer, style]}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
    >
      <Logo size={size} color="white" />
    </LinearGradient>
  );
};

// شعار للشاشات الداكنة
export const DarkLogo: React.FC<LogoProps> = (props) => {
  return <Logo {...props} color="white" />;
};

// شعار مصغر للتنقل
export const NavLogo: React.FC = () => {
  return <Logo size="small" variant="icon" />;
};

// شعار للشاشة الرئيسية
export const HeroLogo: React.FC = () => {
  return <Logo size="xlarge" variant="full" />;
};

const createStyles = (colors: ThemeColors) =>
  StyleSheet.create({
    container: {
      alignItems: 'center',
      justifyContent: 'center',
    },
    fullLogo: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    logoText: {
      fontWeight: FONT_WEIGHTS.bold,
      textAlign: 'center',
      fontFamily: 'System', // يمكن تخصيص خط عربي مميز
    },
    animatedContainer: {
      padding: SPACING.lg,
      borderRadius: 20,
      alignItems: 'center',
      justifyContent: 'center',
      shadowColor: BRAND_COLORS.primary,
      shadowOffset: { width: 0, height: 8 },
      shadowOpacity: 0.3,
      shadowRadius: 16,
      elevation: 12,
    },
  });

export default Logo;
