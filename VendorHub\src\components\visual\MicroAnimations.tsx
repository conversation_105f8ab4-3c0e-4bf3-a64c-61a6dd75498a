import React, { useEffect, useRef } from 'react';
import { Animated, Easing } from 'react-native';

interface AnimatedPressableProps {
  children: React.ReactNode;
  onPress?: () => void;
  onLongPress?: () => void;
  style?: any;
  scaleValue?: number;
  duration?: number;
  disabled?: boolean;
}

export const AnimatedPressable: React.FC<AnimatedPressableProps> = ({
  children,
  onPress,
  onLongPress,
  style,
  scaleValue = 0.95,
  duration = 150,
  disabled = false,
}) => {
  const scaleAnim = useRef(new Animated.Value(1)).current;

  const handlePressIn = () => {
    if (disabled) return;
    Animated.timing(scaleAnim, {
      toValue: scaleValue,
      duration,
      useNativeDriver: true,
      easing: Easing.out(Easing.quad),
    }).start();
  };

  const handlePressOut = () => {
    if (disabled) return;
    Animated.spring(scaleAnim, {
      toValue: 1,
      useNativeDriver: true,
      tension: 300,
      friction: 10,
    }).start();
  };

  return (
    <Animated.View
      style={[
        style,
        {
          transform: [{ scale: scaleAnim }],
          opacity: disabled ? 0.6 : 1,
        },
      ]}
    >
      <Animated.View
        onTouchStart={handlePressIn}
        onTouchEnd={handlePressOut}
        onTouchCancel={handlePressOut}
        onResponderGrant={handlePressIn}
        onResponderRelease={handlePressOut}
        onResponderTerminate={handlePressOut}
        onStartShouldSetResponder={() => !disabled}
        onResponderTerminationRequest={() => true}
        onPress={disabled ? undefined : onPress}
        onLongPress={disabled ? undefined : onLongPress}
      >
        {children}
      </Animated.View>
    </Animated.View>
  );
};

interface FadeInViewProps {
  children: React.ReactNode;
  duration?: number;
  delay?: number;
  style?: any;
  direction?: 'up' | 'down' | 'left' | 'right' | 'none';
  distance?: number;
}

export const FadeInView: React.FC<FadeInViewProps> = ({
  children,
  duration = 800,
  delay = 0,
  style,
  direction = 'up',
  distance = 30,
}) => {
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const translateAnim = useRef(new Animated.Value(distance)).current;

  useEffect(() => {
    const animation = Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration,
        delay,
        useNativeDriver: true,
        easing: Easing.out(Easing.quad),
      }),
      Animated.timing(translateAnim, {
        toValue: 0,
        duration,
        delay,
        useNativeDriver: true,
        easing: Easing.out(Easing.quad),
      }),
    ]);

    animation.start();
  }, [fadeAnim, translateAnim, duration, delay, distance]);

  const getTransform = () => {
    switch (direction) {
      case 'up':
        return [{ translateY: translateAnim }];
      case 'down':
        return [{ translateY: Animated.multiply(translateAnim, -1) }];
      case 'left':
        return [{ translateX: translateAnim }];
      case 'right':
        return [{ translateX: Animated.multiply(translateAnim, -1) }];
      default:
        return [];
    }
  };

  return (
    <Animated.View
      style={[
        style,
        {
          opacity: fadeAnim,
          transform: getTransform(),
        },
      ]}
    >
      {children}
    </Animated.View>
  );
};

interface PulseViewProps {
  children: React.ReactNode;
  style?: any;
  minScale?: number;
  maxScale?: number;
  duration?: number;
  loop?: boolean;
}

export const PulseView: React.FC<PulseViewProps> = ({
  children,
  style,
  minScale = 1,
  maxScale = 1.05,
  duration = 2000,
  loop = true,
}) => {
  const pulseAnim = useRef(new Animated.Value(minScale)).current;

  useEffect(() => {
    const createPulseAnimation = () => {
      return Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: maxScale,
          duration: duration / 2,
          useNativeDriver: true,
          easing: Easing.inOut(Easing.quad),
        }),
        Animated.timing(pulseAnim, {
          toValue: minScale,
          duration: duration / 2,
          useNativeDriver: true,
          easing: Easing.inOut(Easing.quad),
        }),
      ]);
    };

    if (loop) {
      const loopAnimation = Animated.loop(createPulseAnimation());
      loopAnimation.start();
      return () => loopAnimation.stop();
    } else {
      createPulseAnimation().start();
    }
  }, [pulseAnim, minScale, maxScale, duration, loop]);

  return (
    <Animated.View
      style={[
        style,
        {
          transform: [{ scale: pulseAnim }],
        },
      ]}
    >
      {children}
    </Animated.View>
  );
};

interface ShimmerViewProps {
  children: React.ReactNode;
  style?: any;
  shimmerColors?: string[];
  duration?: number;
  angle?: number;
}

export const ShimmerView: React.FC<ShimmerViewProps> = ({
  children,
  style,
  shimmerColors = ['transparent', 'rgba(255, 255, 255, 0.3)', 'transparent'],
  duration = 1500,
  angle = 45,
}) => {
  const shimmerAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const shimmerAnimation = Animated.loop(
      Animated.timing(shimmerAnim, {
        toValue: 1,
        duration,
        useNativeDriver: true,
        easing: Easing.linear,
      })
    );

    shimmerAnimation.start();
    return () => shimmerAnimation.stop();
  }, [shimmerAnim, duration]);

  const translateX = shimmerAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [-200, 200],
  });

  return (
    <Animated.View style={[style, { overflow: 'hidden' }]}>
      {children}
      <Animated.View
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          transform: [
            { translateX },
            { rotate: `${angle}deg` },
          ],
        }}
      >
        <Animated.View
          style={{
            flex: 1,
            width: 100,
            backgroundColor: shimmerColors[1],
            opacity: 0.6,
          }}
        />
      </Animated.View>
    </Animated.View>
  );
};

interface BouncyViewProps {
  children: React.ReactNode;
  style?: any;
  bounceValue?: number;
  duration?: number;
  trigger?: boolean;
}

export const BouncyView: React.FC<BouncyViewProps> = ({
  children,
  style,
  bounceValue = 1.2,
  duration = 300,
  trigger = false,
}) => {
  const bounceAnim = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    if (trigger) {
      Animated.sequence([
        Animated.timing(bounceAnim, {
          toValue: bounceValue,
          duration: duration / 2,
          useNativeDriver: true,
          easing: Easing.out(Easing.quad),
        }),
        Animated.spring(bounceAnim, {
          toValue: 1,
          useNativeDriver: true,
          tension: 300,
          friction: 10,
        }),
      ]).start();
    }
  }, [trigger, bounceAnim, bounceValue, duration]);

  return (
    <Animated.View
      style={[
        style,
        {
          transform: [{ scale: bounceAnim }],
        },
      ]}
    >
      {children}
    </Animated.View>
  );
};
