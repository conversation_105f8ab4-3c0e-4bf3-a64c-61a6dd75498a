#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Configuration
const config = {
  srcDir: path.join(__dirname, '../src'),
  outputFile: path.join(__dirname, '../ALL_IMPORT_PATHS_FIXES_REPORT.md'),
  excludePatterns: [
    /node_modules/,
    /\.git/,
    /\.expo/,
    /dist/,
    /build/,
    /coverage/,
    /\.DS_Store/,
    /\.env/,
    /package-lock\.json/,
    /yarn\.lock/,
    /\.log$/,
    /\.md$/,
    /\.json$/,
    /\.js\.map$/,
    /\.d\.ts$/,
    /scripts/,
    /assets/,
  ],
  fileExtensions: ['.tsx', '.ts', '.jsx', '.js'],
  
  // Import path fixes based on file location
  importFixes: [
    // For files in src/components/ui/ directory
    {
      name: 'uiComponentsRTLPath',
      pattern: /from\s+['"]\.\.\/components\/RTL['"]/g,
      replacement: "from '../RTL'",
      description: 'Fixed RTL import path for UI components',
      filePattern: /src[\/\\]components[\/\\]ui[\/\\]/
    },
    // For files in src/navigation/ directory  
    {
      name: 'navigationRTLPath',
      pattern: /from\s+['"]\.\.\/RTL['"]/g,
      replacement: "from '../components/RTL'",
      description: 'Fixed RTL import path for navigation files',
      filePattern: /src[\/\\]navigation[\/\\]/
    },
    // For files in src/screens/ directory
    {
      name: 'screensRTLPath',
      pattern: /from\s+['"]\.\.\/\.\.\/RTL['"]/g,
      replacement: "from '../../components/RTL'",
      description: 'Fixed RTL import path for screen files',
      filePattern: /src[\/\\]screens[\/\\]/
    },
    // For files in src/components/ directory (root level)
    {
      name: 'componentsRTLPath',
      pattern: /from\s+['"]\.\.\/RTL['"]/g,
      replacement: "from './RTL'",
      description: 'Fixed RTL import path for root components',
      filePattern: /src[\/\\]components[\/\\][^\/\\]*\.tsx?$/
    },
    // General fixes for any remaining issues
    {
      name: 'wrongRTLPathGeneral',
      pattern: /from\s+['"]\.\.\/components\/RTL['"]/g,
      replacement: "from '../RTL'",
      description: 'Fixed general incorrect RTL import path'
    },
    // Fix duplicate type keyword
    {
      name: 'duplicateTypeKeyword',
      pattern: /import\s+\{\s*type\s+\{\s*([^}]+)\s*\}\s*,/g,
      replacement: "import { type $1,",
      description: 'Fixed duplicate type keyword in import'
    },
    // Fix trailing commas in imports
    {
      name: 'trailingCommaInImport',
      pattern: /import\s+\{([^}]+),\s*\}\s+from/g,
      replacement: "import {$1 } from",
      description: 'Fixed trailing comma in import statement'
    }
  ]
};

class AllImportPathsFixer {
  constructor() {
    this.results = {
      filesProcessed: 0,
      filesFixed: 0,
      totalFixes: 0,
      fixesByType: {},
      fixedFiles: []
    };
  }

  scanDirectory(dirPath) {
    const items = fs.readdirSync(dirPath);
    
    for (const item of items) {
      const fullPath = path.join(dirPath, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        // Skip excluded directories
        if (config.excludePatterns.some(pattern => pattern.test(fullPath))) {
          continue;
        }
        this.scanDirectory(fullPath);
      } else if (stat.isFile()) {
        // Check if file should be processed
        if (this.shouldProcessFile(fullPath)) {
          this.processFile(fullPath);
        }
      }
    }
  }

  shouldProcessFile(filePath) {
    // Check file extension
    const ext = path.extname(filePath);
    if (!config.fileExtensions.includes(ext)) {
      return false;
    }
    
    // Check exclude patterns
    if (config.excludePatterns.some(pattern => pattern.test(filePath))) {
      return false;
    }
    
    return true;
  }

  processFile(filePath) {
    this.results.filesProcessed++;
    
    try {
      let content = fs.readFileSync(filePath, 'utf8');
      let originalContent = content;
      let fileFixed = false;
      let fileFixes = [];

      // Apply each fix pattern
      config.importFixes.forEach(fix => {
        // Check if this fix applies to this file location
        if (fix.filePattern && !fix.filePattern.test(filePath)) {
          return;
        }

        const matches = (content.match(fix.pattern) || []).length;
        
        if (matches > 0) {
          content = content.replace(fix.pattern, fix.replacement);
          fileFixed = true;
          fileFixes.push({
            type: fix.name,
            count: matches,
            description: fix.description
          });
          
          if (!this.results.fixesByType[fix.name]) {
            this.results.fixesByType[fix.name] = 0;
          }
          this.results.fixesByType[fix.name] += matches;
          this.results.totalFixes += matches;
        }
      });

      // Write file if changes were made
      if (fileFixed && content !== originalContent) {
        fs.writeFileSync(filePath, content, 'utf8');
        this.results.filesFixed++;
        this.results.fixedFiles.push({
          path: path.relative(process.cwd(), filePath),
          fixes: fileFixes,
          totalFixes: fileFixes.reduce((sum, fix) => sum + fix.count, 0)
        });
        
        console.log(`✅ Fixed: ${path.relative(process.cwd(), filePath)} (${fileFixes.reduce((sum, fix) => sum + fix.count, 0)} fixes)`);
      }
    } catch (error) {
      console.error(`❌ Error processing ${filePath}:`, error.message);
    }
  }

  generateReport() {
    const report = [
      '# All Import Paths Fixes Report',
      '',
      `**Generated:** ${new Date().toLocaleString()}`,
      `**Files Processed:** ${this.results.filesProcessed}`,
      `**Files Fixed:** ${this.results.filesFixed}`,
      `**Total Fixes Applied:** ${this.results.totalFixes}`,
      '',
      '## 📊 Fixes Summary',
      '',
      '| Fix Type | Count | Description |',
      '|----------|-------|-------------|'
    ];

    // Add fixes summary
    Object.entries(this.results.fixesByType).forEach(([type, count]) => {
      const fix = config.importFixes.find(f => f.name === type);
      const description = fix ? fix.description : type;
      report.push(`| ${type} | ${count} | ${description} |`);
    });

    if (this.results.fixedFiles.length > 0) {
      report.push('', '## 📁 Fixed Files', '');
      
      this.results.fixedFiles.forEach(file => {
        report.push(`### ${file.path}`, '');
        report.push(`**Total fixes:** ${file.totalFixes}`, '');
        
        file.fixes.forEach(fix => {
          report.push(`- **${fix.type}** (${fix.count} occurrences): ${fix.description}`);
        });
        
        report.push('');
      });
    }

    // Add correct import patterns
    report.push(
      '## 📋 Correct Import Patterns by Location',
      '',
      '### Components Directory Structure',
      '',
      '```',
      'src/',
      '├── components/',
      '│   ├── RTL/',
      '│   │   ├── index.ts',
      '│   │   ├── RTLView.tsx',
      '│   │   └── ...',
      '│   ├── ui/',
      '│   │   └── Component.tsx',
      '│   └── Component.tsx',
      '├── navigation/',
      '│   └── Navigator.tsx',
      '└── screens/',
      '    └── Screen.tsx',
      '```',
      '',
      '### Import Patterns',
      '',
      '```typescript',
      '// ✅ From src/components/Component.tsx',
      "import { RTLView } from './RTL';",
      '',
      '// ✅ From src/components/ui/Component.tsx',
      "import { RTLView } from '../RTL';",
      '',
      '// ✅ From src/navigation/Navigator.tsx',
      "import { RTLView } from '../components/RTL';",
      '',
      '// ✅ From src/screens/Screen.tsx',
      "import { RTLView } from '../../components/RTL';",
      '```'
    );

    return report.join('\n');
  }

  run() {
    console.log('🔍 Scanning for all incorrect import paths...');
    
    // Scan source directory
    if (fs.existsSync(config.srcDir)) {
      this.scanDirectory(config.srcDir);
    }

    // Generate and save report
    const report = this.generateReport();
    fs.writeFileSync(config.outputFile, report, 'utf8');
    
    console.log('\n📊 Summary:');
    console.log(`Files processed: ${this.results.filesProcessed}`);
    console.log(`Files fixed: ${this.results.filesFixed}`);
    console.log(`Total fixes: ${this.results.totalFixes}`);
    console.log(`Report saved: ${config.outputFile}`);
    
    if (this.results.totalFixes > 0) {
      console.log('\n✅ All import paths have been fixed!');
    } else {
      console.log('\n✅ No incorrect import paths found.');
    }
  }
}

// Run the fixer
const fixer = new AllImportPathsFixer();
fixer.run();
