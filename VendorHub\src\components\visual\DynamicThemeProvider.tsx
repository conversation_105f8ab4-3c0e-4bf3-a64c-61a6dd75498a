import React, { createContext, useContext, useState, useEffect } from 'react';
import { Animated } from 'react-native';
import { PREMIUM_GRADIENTS } from '../../constants/advancedColors';

interface DynamicTheme {
  primaryGradient: readonly string[];
  secondaryGradient: readonly string[];
  accentColor: string;
  textPrimary: string;
  textSecondary: string;
  backgroundOverlay: string;
  glowColor: string;
  intensity: number;
}

interface DynamicThemeContextType {
  currentTheme: DynamicTheme;
  setThemeMode: (mode: ThemeMode) => void;
  animateThemeTransition: (newTheme: DynamicTheme) => void;
  themeAnimation: Animated.Value;
}

type ThemeMode = 
  | 'elegant' 
  | 'vibrant' 
  | 'warm' 
  | 'cool' 
  | 'premium' 
  | 'seasonal' 
  | 'vendor-specific';

const DynamicThemeContext = createContext<DynamicThemeContextType | undefined>(undefined);

const THEME_PRESETS: Record<ThemeMode, DynamicTheme> = {
  elegant: {
    primaryGradient: PREMIUM_GRADIENTS.elegantDepth,
    secondaryGradient: PREMIUM_GRADIENTS.subtleElegance,
    accentColor: '#3B82F6',
    textPrimary: '#FFFFFF',
    textSecondary: 'rgba(255, 255, 255, 0.8)',
    backgroundOverlay: 'rgba(0, 0, 0, 0.3)',
    glowColor: '#3B82F6',
    intensity: 0.6,
  },
  vibrant: {
    primaryGradient: PREMIUM_GRADIENTS.royalSpotlight,
    secondaryGradient: PREMIUM_GRADIENTS.mysticAurora,
    accentColor: '#10B981',
    textPrimary: '#FFFFFF',
    textSecondary: 'rgba(255, 255, 255, 0.9)',
    backgroundOverlay: 'rgba(0, 0, 0, 0.2)',
    glowColor: '#10B981',
    intensity: 0.8,
  },
  warm: {
    primaryGradient: ['#F59E0B', '#D97706', '#92400E'],
    secondaryGradient: ['#FEF3C7', '#FDE68A', '#F59E0B'],
    accentColor: '#F59E0B',
    textPrimary: '#FFFFFF',
    textSecondary: 'rgba(255, 255, 255, 0.85)',
    backgroundOverlay: 'rgba(0, 0, 0, 0.25)',
    glowColor: '#F59E0B',
    intensity: 0.7,
  },
  cool: {
    primaryGradient: PREMIUM_GRADIENTS.oceanDepth,
    secondaryGradient: ['#3B82F6', '#1D4ED8', '#1E40AF'],
    accentColor: '#06B6D4',
    textPrimary: '#FFFFFF',
    textSecondary: 'rgba(255, 255, 255, 0.8)',
    backgroundOverlay: 'rgba(0, 0, 0, 0.3)',
    glowColor: '#06B6D4',
    intensity: 0.6,
  },
  premium: {
    primaryGradient: PREMIUM_GRADIENTS.royalElegance,
    secondaryGradient: ['#FFD700', '#F59E0B', '#D97706'],
    accentColor: '#FFD700',
    textPrimary: '#FFFFFF',
    textSecondary: 'rgba(255, 255, 255, 0.9)',
    backgroundOverlay: 'rgba(0, 0, 0, 0.4)',
    glowColor: '#FFD700',
    intensity: 0.9,
  },
  seasonal: {
    primaryGradient: ['#10B981', '#059669', '#047857'],
    secondaryGradient: ['#34D399', '#10B981', '#059669'],
    accentColor: '#10B981',
    textPrimary: '#FFFFFF',
    textSecondary: 'rgba(255, 255, 255, 0.85)',
    backgroundOverlay: 'rgba(0, 0, 0, 0.25)',
    glowColor: '#10B981',
    intensity: 0.7,
  },
  'vendor-specific': {
    primaryGradient: PREMIUM_GRADIENTS.elegantDepth,
    secondaryGradient: PREMIUM_GRADIENTS.subtleElegance,
    accentColor: '#8B5CF6',
    textPrimary: '#FFFFFF',
    textSecondary: 'rgba(255, 255, 255, 0.8)',
    backgroundOverlay: 'rgba(0, 0, 0, 0.3)',
    glowColor: '#8B5CF6',
    intensity: 0.6,
  },
};

interface DynamicThemeProviderProps {
  children: React.ReactNode;
  initialTheme?: ThemeMode;
}

export const DynamicThemeProvider: React.FC<DynamicThemeProviderProps> = ({
  children,
  initialTheme = 'elegant',
}) => {
  const [currentTheme, setCurrentTheme] = useState<DynamicTheme>(THEME_PRESETS[initialTheme]);
  const [themeAnimation] = useState(new Animated.Value(1));

  const setThemeMode = (mode: ThemeMode) => {
    const newTheme = THEME_PRESETS[mode];
    animateThemeTransition(newTheme);
  };

  const animateThemeTransition = (newTheme: DynamicTheme) => {
    Animated.sequence([
      Animated.timing(themeAnimation, {
        toValue: 0,
        duration: 200,
        useNativeDriver: false,
      }),
      Animated.timing(themeAnimation, {
        toValue: 1,
        duration: 300,
        useNativeDriver: false,
      }),
    ]).start();

    // Update theme at the midpoint of animation
    setTimeout(() => {
      setCurrentTheme(newTheme);
    }, 200);
  };

  // Auto-adapt theme based on time of day
  useEffect(() => {
    const hour = new Date().getHours();
    let autoTheme: ThemeMode = 'elegant';

    if (hour >= 6 && hour < 12) {
      autoTheme = 'warm'; // Morning
    } else if (hour >= 12 && hour < 18) {
      autoTheme = 'vibrant'; // Afternoon
    } else if (hour >= 18 && hour < 22) {
      autoTheme = 'cool'; // Evening
    } else {
      autoTheme = 'elegant'; // Night
    }

    // Only auto-change if it's the initial load
    if (currentTheme === THEME_PRESETS[initialTheme]) {
      setThemeMode(autoTheme);
    }
  }, []);

  const contextValue: DynamicThemeContextType = {
    currentTheme,
    setThemeMode,
    animateThemeTransition,
    themeAnimation,
  };

  return (
    <DynamicThemeContext.Provider value={contextValue}>
      {children}
    </DynamicThemeContext.Provider>
  );
};

export const useDynamicTheme = (): DynamicThemeContextType => {
  const context = useContext(DynamicThemeContext);
  if (!context) {
    throw new Error('useDynamicTheme must be used within a DynamicThemeProvider');
  }
  return context;
};

// Helper hook for vendor-specific theming
export const useVendorTheme = (vendorId?: string, vendorRating?: number) => {
  const { setThemeMode, animateThemeTransition } = useDynamicTheme();

  const applyVendorTheme = () => {
    if (!vendorId || !vendorRating) return;

    let vendorTheme: DynamicTheme;

    if (vendorRating >= 4.5) {
      // Premium vendors get gold theme
      vendorTheme = {
        ...THEME_PRESETS.premium,
        primaryGradient: ['#FFD700', '#F59E0B', '#D97706'],
        accentColor: '#FFD700',
        glowColor: '#FFD700',
        intensity: 0.9,
      };
    } else if (vendorRating >= 4.0) {
      // High-rated vendors get vibrant theme
      vendorTheme = {
        ...THEME_PRESETS.vibrant,
        intensity: 0.8,
      };
    } else {
      // Regular vendors get elegant theme
      vendorTheme = THEME_PRESETS.elegant;
    }

    animateThemeTransition(vendorTheme);
  };

  return { applyVendorTheme };
};

// Helper hook for seasonal theming
export const useSeasonalTheme = () => {
  const { setThemeMode } = useDynamicTheme();

  const applySeasonalTheme = () => {
    const month = new Date().getMonth();
    let seasonalMode: ThemeMode = 'elegant';

    if (month >= 2 && month <= 4) {
      seasonalMode = 'seasonal'; // Spring - Green
    } else if (month >= 5 && month <= 7) {
      seasonalMode = 'warm'; // Summer - Warm
    } else if (month >= 8 && month <= 10) {
      seasonalMode = 'cool'; // Autumn - Cool
    } else {
      seasonalMode = 'elegant'; // Winter - Elegant
    }

    setThemeMode(seasonalMode);
  };

  return { applySeasonalTheme };
};
