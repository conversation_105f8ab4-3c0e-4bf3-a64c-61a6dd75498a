import React, { useCallback } from 'react';
import { StyleSheet } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  withSequence,
  withRepeat,
  runOnJS,
  interpolate,
  Extrapolation,
} from 'react-native-reanimated';
import * as Haptics from 'expo-haptics';
import { useThemedStyles, useI18n } from '../../hooks';
import { Button, ButtonProps } from '../Button';
import { webCompatibleHaptics } from '../../utils/webCompatibility';
import { rtlAwareTranslateX, rtlAwareRotation } from '../../utils/rtlAnimations';
import { useAnimationPerformance, getOptimizedAnimationConfig } from '../../utils/animationPerformance';
import { SPACING, BORDER_RADIUS } from '../../constants/theme';
import type ThemeColors from '../../contexts/ThemeContext';

export interface ReanimatedButtonProps extends ButtonProps {
  animationType?: 'scale' | 'bounce' | 'pulse' | 'shake' | 'glow' | 'elastic' | 'wobble';
  hapticFeedback?: 'light' | 'medium' | 'heavy' | 'success' | 'warning' | 'error' | 'none';
  animationDuration?: number;
  disabled?: boolean;
  onAnimationComplete?: () => void;
  springConfig?: {
    damping?: number;
    stiffness?: number;
    mass?: number;
  };
}

export const ReanimatedButton: React.FC<ReanimatedButtonProps> = ({
  animationType = 'scale',
  hapticFeedback = 'light',
  animationDuration = 150,
  disabled = false,
  onPress,
  onAnimationComplete,
  style,
  springConfig = { damping: 15, stiffness: 150, mass: 1 },
  ...buttonProps
}) => {
  const styles = useThemedStyles(createStyles);
  const { isRTL } = useI18n();
  const { startAnimation, endAnimation } = useAnimationPerformance(`button-${animationType}`);

  // Shared values for animations
  const scale = useSharedValue(1);
  const rotation = useSharedValue(0);
  const opacity = useSharedValue(1);
  const glowIntensity = useSharedValue(0);
  const translateX = useSharedValue(0);
  const translateY = useSharedValue(0);

  const triggerHapticFeedback = useCallback(() => {
    if (hapticFeedback === 'none' || disabled) return;

    switch (hapticFeedback) {
      case 'light':
        webCompatibleHaptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
        break;
      case 'medium':
        webCompatibleHaptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
        break;
      case 'heavy':
        webCompatibleHaptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
        break;
      case 'success':
        webCompatibleHaptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
        break;
      case 'warning':
        webCompatibleHaptics.notificationAsync(Haptics.NotificationFeedbackType.Warning);
        break;
      case 'error':
        webCompatibleHaptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
        break;
    }
  }, [hapticFeedback, disabled]);

  const handleAnimationComplete = useCallback((animationId?: string) => {
    'worklet';
    if (animationId) {
      runOnJS(endAnimation)(animationId);
    }
    if (onAnimationComplete) {
      runOnJS(onAnimationComplete)();
    }
  }, [onAnimationComplete, endAnimation]);

  const playAnimation = useCallback(() => {
    'worklet';
    if (disabled) return;

    // Start performance monitoring
    const animationId = runOnJS(startAnimation)(true);

    // Get optimized animation config
    const optimizedConfig = runOnJS(getOptimizedAnimationConfig)(
      { duration: animationDuration },
      animationType
    );

    switch (animationType) {
      case 'scale':
        scale.value = withSequence(
          withTiming(0.95, { duration: optimizedConfig.duration / 2 }),
          withTiming(1, { duration: optimizedConfig.duration / 2 }, () => handleAnimationComplete(animationId))
        );
        break;

      case 'bounce':
        scale.value = withSequence(
          withTiming(0.9, { duration: animationDuration / 3 }),
          withSpring(1.05, springConfig),
          withSpring(1, springConfig, handleAnimationComplete)
        );
        break;

      case 'pulse':
        scale.value = withSequence(
          withTiming(1.1, { duration: animationDuration }),
          withTiming(1, { duration: animationDuration }, handleAnimationComplete)
        );
        break;

      case 'shake':
        // RTL-aware shake animation
        const shakeDistance = 5;
        translateX.value = withSequence(
          withTiming(rtlAwareTranslateX(shakeDistance, { isRTL }), { duration: 50 }),
          withTiming(rtlAwareTranslateX(-shakeDistance, { isRTL }), { duration: 50 }),
          withTiming(rtlAwareTranslateX(shakeDistance, { isRTL }), { duration: 50 }),
          withTiming(rtlAwareTranslateX(-shakeDistance, { isRTL }), { duration: 50 }),
          withTiming(0, { duration: 50 }, handleAnimationComplete)
        );
        break;

      case 'glow':
        glowIntensity.value = withSequence(
          withTiming(1, { duration: animationDuration }),
          withTiming(0, { duration: animationDuration }, handleAnimationComplete)
        );
        break;

      case 'elastic':
        scale.value = withSequence(
          withTiming(0.8, { duration: animationDuration / 4 }),
          withSpring(1.2, { damping: 8, stiffness: 200 }),
          withSpring(1, springConfig, handleAnimationComplete)
        );
        break;

      case 'wobble':
        // RTL-aware wobble animation
        rotation.value = withSequence(
          withTiming(rtlAwareRotation(15, { isRTL }), { duration: animationDuration / 4 }),
          withTiming(rtlAwareRotation(-10, { isRTL }), { duration: animationDuration / 4 }),
          withTiming(rtlAwareRotation(5, { isRTL }), { duration: animationDuration / 4 }),
          withTiming(0, { duration: animationDuration / 4 }, handleAnimationComplete)
        );
        break;
    }
  }, [animationType, animationDuration, disabled, springConfig, handleAnimationComplete]);

  const handlePress = useCallback(() => {
    triggerHapticFeedback();
    runOnJS(playAnimation)();
    onPress?.();
  }, [triggerHapticFeedback, playAnimation, onPress]);

  // Animated styles using worklets
  const animatedStyle = useAnimatedStyle(() => {
    const shadowOpacity = animationType === 'glow' 
      ? interpolate(glowIntensity.value, [0, 1], [0.2, 0.8], Extrapolation.CLAMP)
      : 0.2;

    const elevation = animationType === 'glow'
      ? interpolate(glowIntensity.value, [0, 1], [2, 12], Extrapolation.CLAMP)
      : 2;

    return {
      transform: [
        { scale: scale.value },
        { rotate: `${rotation.value}deg` },
        { translateX: translateX.value },
        { translateY: translateY.value },
      ],
      opacity: disabled ? 0.6 : opacity.value,
      shadowOpacity,
      elevation,
    };
  }, [animationType, disabled]);

  return (
    <Animated.View style={[animatedStyle, style]}>
      <Button
        {...buttonProps}
        onPress={handlePress}
        disabled={disabled}
        style={[
          animationType === 'glow' && styles.glowButton,
          style,
        ].filter(Boolean) as any}
      />
    </Animated.View>
  );
};

// Preset animated buttons for common use cases
export const ReanimatedSuccessButton: React.FC<Omit<ReanimatedButtonProps, 'animationType' | 'hapticFeedback'>> = (props) => (
  <ReanimatedButton
    {...props}
    animationType="bounce"
    hapticFeedback="success"
  />
);

export const ReanimatedErrorButton: React.FC<Omit<ReanimatedButtonProps, 'animationType' | 'hapticFeedback'>> = (props) => (
  <ReanimatedButton
    {...props}
    animationType="shake"
    hapticFeedback="error"
  />
);

export const ReanimatedPulseButton: React.FC<Omit<ReanimatedButtonProps, 'animationType' | 'hapticFeedback'>> = (props) => (
  <ReanimatedButton
    {...props}
    animationType="pulse"
    hapticFeedback="medium"
  />
);

export const ReanimatedGlowButton: React.FC<Omit<ReanimatedButtonProps, 'animationType' | 'hapticFeedback'>> = (props) => (
  <ReanimatedButton
    {...props}
    animationType="glow"
    hapticFeedback="light"
  />
);

export const ReanimatedElasticButton: React.FC<Omit<ReanimatedButtonProps, 'animationType' | 'hapticFeedback'>> = (props) => (
  <ReanimatedButton
    {...props}
    animationType="elastic"
    hapticFeedback="heavy"
  />
);

// Hook for programmatic animations using Reanimated 3
export const useReanimatedButtonAnimation = (
  animationType: ReanimatedButtonProps['animationType'] = 'scale',
  springConfig: ReanimatedButtonProps['springConfig'] = { damping: 15, stiffness: 150 }
) => {
  const scale = useSharedValue(1);
  const rotation = useSharedValue(0);
  const translateX = useSharedValue(0);
  const opacity = useSharedValue(1);

  const animate = useCallback((duration: number = 150) => {
    'worklet';
    switch (animationType) {
      case 'scale':
        scale.value = withSequence(
          withTiming(0.95, { duration: duration / 2 }),
          withTiming(1, { duration: duration / 2 })
        );
        break;
      case 'bounce':
        scale.value = withSequence(
          withTiming(0.9, { duration: duration / 3 }),
          withSpring(1.1, springConfig),
          withSpring(1, springConfig)
        );
        break;
      case 'elastic':
        scale.value = withSequence(
          withTiming(0.8, { duration: duration / 4 }),
          withSpring(1.2, { damping: 8, stiffness: 200 }),
          withSpring(1, springConfig)
        );
        break;
      case 'wobble':
        rotation.value = withSequence(
          withTiming(15, { duration: duration / 4 }),
          withTiming(-10, { duration: duration / 4 }),
          withTiming(5, { duration: duration / 4 }),
          withTiming(0, { duration: duration / 4 })
        );
        break;
    }
  }, [animationType, springConfig]);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [
      { scale: scale.value },
      { rotate: `${rotation.value}deg` },
      { translateX: translateX.value },
    ],
    opacity: opacity.value,
  }), []);

  return { animate: runOnJS(animate), animatedStyle };
};

const createStyles = (colors: ThemeColors) => StyleSheet.create({
  glowButton: {
    shadowColor: colors.primary,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowRadius: 8,
    elevation: 8,
  },
});
