import React, { useRef } from 'react';
import { StyleSheet, Animated, Dimensions } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { RTLView } from '../RTL';
import { useThemedStyles } from '../../hooks';
import { PREMIUM_GRADIENTS } from '../../constants/advancedColors';
import type { ThemeColors } from '../../contexts/ThemeContext';

const { height: screenHeight } = Dimensions.get('window');

interface ParallaxScrollViewProps {
  children: React.ReactNode;
  headerHeight?: number;
  parallaxHeaderComponent?: React.ReactNode;
  backgroundGradient?: readonly string[];
  onScroll?: (event: any) => void;
  scrollEventThrottle?: number;
  showsVerticalScrollIndicator?: boolean;
  contentContainerStyle?: any;
  refreshControl?: React.ReactElement;
}

export const ParallaxScrollView: React.FC<ParallaxScrollViewProps> = ({
  children,
  headerHeight = screenHeight * 0.3,
  parallaxHeaderComponent,
  backgroundGradient = PREMIUM_GRADIENTS.elegantDepth,
  onScroll,
  scrollEventThrottle = 16,
  showsVerticalScrollIndicator = false,
  contentContainerStyle,
  refreshControl,
}) => {
  const styles = useThemedStyles(createStyles);
  const scrollY = useRef(new Animated.Value(0)).current;

  // Parallax header animation
  const headerTranslateY = scrollY.interpolate({
    inputRange: [0, headerHeight],
    outputRange: [0, -headerHeight / 2],
    extrapolate: 'clamp',
  });

  const headerOpacity = scrollY.interpolate({
    inputRange: [0, headerHeight / 2, headerHeight],
    outputRange: [1, 0.8, 0.3],
    extrapolate: 'clamp',
  });

  const headerScale = scrollY.interpolate({
    inputRange: [-headerHeight, 0, headerHeight],
    outputRange: [1.5, 1, 0.8],
    extrapolate: 'clamp',
  });

  // Content fade-in animation
  const contentOpacity = scrollY.interpolate({
    inputRange: [0, headerHeight / 3, headerHeight],
    outputRange: [0.7, 0.9, 1],
    extrapolate: 'clamp',
  });

  // Background overlay animation
  const overlayOpacity = scrollY.interpolate({
    inputRange: [0, headerHeight / 2, headerHeight],
    outputRange: [0, 0.3, 0.6],
    extrapolate: 'clamp',
  });

  const handleScroll = Animated.event(
    [{ nativeEvent: { contentOffset: { y: scrollY } } }],
    {
      useNativeDriver: false,
      listener: onScroll,
    }
  );

  return (
    <RTLView style={styles.container}>
      {/* Parallax Header */}
      {parallaxHeaderComponent && (
        <Animated.View
          style={[
            styles.parallaxHeader,
            {
              height: headerHeight,
              transform: [
                { translateY: headerTranslateY },
                { scale: headerScale },
              ],
              opacity: headerOpacity,
            },
          ]}
        >
          <LinearGradient
            colors={backgroundGradient}
            style={styles.headerGradient}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          >
            {parallaxHeaderComponent}
            
            {/* Animated Overlay */}
            <Animated.View
              style={[
                styles.headerOverlay,
                { opacity: overlayOpacity }
              ]}
            />
          </LinearGradient>
        </Animated.View>
      )}

      {/* Scrollable Content */}
      <Animated.ScrollView
        style={styles.scrollView}
        contentContainerStyle={[
          styles.scrollContent,
          { paddingTop: parallaxHeaderComponent ? headerHeight : 0 },
          contentContainerStyle,
        ]}
        onScroll={handleScroll}
        scrollEventThrottle={scrollEventThrottle}
        showsVerticalScrollIndicator={showsVerticalScrollIndicator}
        refreshControl={refreshControl}
      >
        <Animated.View
          style={[
            styles.content,
            { opacity: contentOpacity }
          ]}
        >
          {children}
        </Animated.View>
      </Animated.ScrollView>

      {/* Floating Elements */}
      <Animated.View
        style={[
          styles.floatingElements,
          {
            transform: [
              {
                translateY: scrollY.interpolate({
                  inputRange: [0, headerHeight],
                  outputRange: [0, headerHeight / 4],
                  extrapolate: 'clamp',
                }),
              },
            ],
            opacity: scrollY.interpolate({
              inputRange: [0, headerHeight / 2],
              outputRange: [1, 0],
              extrapolate: 'clamp',
            }),
          },
        ]}
      >
        {/* Floating decorative elements can be added here */}
        <RTLView style={styles.floatingDot1} />
        <RTLView style={styles.floatingDot2} />
        <RTLView style={styles.floatingDot3} />
      </Animated.View>
    </RTLView>
  );
};

const createStyles = (colors: ThemeColors) => StyleSheet.create({
  container: {
    flex: 1,
  },
  parallaxHeader: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 1,
    overflow: 'hidden',
  },
  headerGradient: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
  },
  scrollView: {
    flex: 1,
    zIndex: 2,
  },
  scrollContent: {
    flexGrow: 1,
  },
  content: {
    flex: 1,
    backgroundColor: colors.background,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    marginTop: -20,
    paddingTop: 20,
  },
  floatingElements: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: screenHeight * 0.5,
    zIndex: 0,
    pointerEvents: 'none',
  },
  floatingDot1: {
    position: 'absolute',
    top: '20%',
    right: '15%',
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: 'rgba(59, 130, 246, 0.3)',
  },
  floatingDot2: {
    position: 'absolute',
    top: '40%',
    left: '10%',
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: 'rgba(16, 185, 129, 0.2)',
  },
  floatingDot3: {
    position: 'absolute',
    top: '60%',
    right: '25%',
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: 'rgba(245, 158, 11, 0.4)',
  },
});
