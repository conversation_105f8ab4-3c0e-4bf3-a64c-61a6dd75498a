import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  Modal,
  Alert } from 'react-native';
import { useThemedStyles, useAuth, useI18n } from '../../hooks';
import { Card } from '../Card';
import { Button } from '../Button';
import { EmptyState } from '../EmptyState';
import { RTLView, RTLText, RTLIcon, RTLTouchableOpacity, RTLFlatList } from '../RTL';
import {
  SPACING,
  FONT_SIZES,
  FONT_WEIGHTS,
  BORDER_RADIUS } from '../../constants/theme';
import { notificationService, NotificationData } from '../../services/NotificationService';
import type ThemeColors  from '../../contexts/ThemeContext';

export interface NotificationCenterProps {
  visible: boolean;
  onClose: () => void;
  onNotificationPress?: (notification: NotificationData) => void;
}

export const NotificationCenter: React.FC<NotificationCenterProps> = ({
  visible,
  onClose,
  onNotificationPress,
}) => {
  const styles = useThemedStyles(createStyles);
  const { user } = useAuth();
  const { t } = useI18n();
  const [notifications, setNotifications] = useState<NotificationData[]>([]);
  const [filter, setFilter] = useState<'all' | 'unread'>('all');

  useEffect(() => {
    if (visible) {
      loadNotifications();
    }
  }, [visible]);

  const loadNotifications = () => {
    try {
      if (!user?.id) {
        setNotifications([]);
        return;
      }

      if (!notificationService || typeof notificationService.getUserNotifications !== 'function') {
        console.warn('NotificationService not available');
        setNotifications([]);
        return;
      }

      const userNotifications = notificationService.getUserNotifications(user.id);
      setNotifications(userNotifications);
    } catch (error) {
      console.error('Error loading notifications:', error);
      setNotifications([]);
    }
  };

  const filteredNotifications = notifications.filter(notification => {
    if (filter === 'unread') {
      return !notification.read;
    }
    return true;
  });

  const handleNotificationPress = async (notification: NotificationData) => {
    if (!user?.id) return;

    // Mark as read
    await notificationService?.markAsRead(notification.id, user.id);
    setNotifications(prev =>
      prev.map(n => n.id === notification.id ? { ...n, read: true } : n)
    );

    // Call external handler
    onNotificationPress?.(notification);
  };

  const handleMarkAllRead = async () => {
    if (!user?.id) return;

    await notificationService?.markAllAsRead(user.id);
    setNotifications(prev => prev.map(n => ({ ...n, read: true })));
  };

  const handleDeleteNotification = (notificationId: string) => {
    Alert.alert(
      t('notifications.deleteNotification'),
      t('notifications.deleteConfirmation'),
      [
        { text: t('common.cancel'), style: 'cancel' },
        {
          text: t('common.delete'),
          style: 'destructive',
          onPress: () => {
            notificationService?.deleteNotification(notificationId);
            setNotifications(prev => prev.filter(n => n.id !== notificationId));
          },
        },
      ]
    );
  };

  const handleClearAll = () => {
    Alert.alert(
      t('notifications.clearAllNotifications'),
      t('notifications.clearAllConfirmation'),
      [
        { text: t('common.cancel'), style: 'cancel' },
        {
          text: t('notifications.clearAll'),
          style: 'destructive',
          onPress: () => {
            notificationService?.clearAllNotifications();
            setNotifications([]);
          },
        },
      ]
    );
  };

  const getNotificationIcon = (type: NotificationData['type']) => {
    switch (type) {
      case 'order':
        return 'receipt-outline';
      case 'vendor':
        return 'storefront-outline';
      case 'product':
        return 'cube-outline';
      case 'promotion':
        return 'pricetag-outline';
      case 'system':
        return 'settings-outline';
      default:
        return 'notifications-outline';
    }
  };

  const getNotificationColor = (type: NotificationData['type']) => {
    switch (type) {
      case 'order':
        return '#059669';
      case 'vendor':
        return '#0EA5E9';
      case 'product':
        return '#D97706';
      case 'promotion':
        return '#DC2626';
      case 'system':
        return '#1D4ED8';
      default:
        return '#1E3A8A';
    }
  };

  const getPriorityColor = (priority: NotificationData['priority']) => {
    switch (priority) {
      case 'high':
        return '#DC2626';
      case 'normal':
        return '#1E3A8A';
      case 'low':
        return '#94A3B8';
      default:
        return '#1E3A8A';
    }
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return t('notifications.justNow');
    if (diffInMinutes < 60) return `${diffInMinutes}${t('notifications.minutesAgo')}`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return `${Math.floor(diffInMinutes / 1440)}d ago`;
  };

  const renderNotification = ({ item }: { item: NotificationData }) => (
    <Card style={[styles.notificationCard, !item.read && styles.unreadCard].filter(Boolean) as any} variant="outlined">
      <RTLTouchableOpacity
        style={styles.notificationContent}
        onPress={() => handleNotificationPress(item)}
      >
        <RTLView style={styles.notificationHeader}>
          <RTLView style={styles.notificationIcon}>
            <RTLView style={[styles.iconContainer, { backgroundColor: getNotificationColor(item.type) }]}>
              <RTLIcon name={getNotificationIcon(item.type)} size={20} color="#FFFFFF" />
            </RTLView>
            {item.priority === 'high' && (
              <RTLView style={[styles.priorityBadge, { backgroundColor: getPriorityColor(item.priority) }]}>
                <RTLIcon name="alert" size={12} color="#FFFFFF" />
              </RTLView>
            )}
          </RTLView>

          <RTLView style={styles.notificationInfo}>
            <RTLText style={[styles.notificationTitle, !item.read && styles.unreadTitle]}>
              {item.title}
            </RTLText>
            <RTLText style={styles.notificationBody} numberOfLines={2}>
              {item.body}
            </RTLText>
            <RTLText style={styles.notificationTime}>
              {formatTimestamp(item.timestamp)}
            </RTLText>
          </RTLView>

          <RTLView style={styles.notificationActions}>
            {!item.read && <RTLView style={styles.unreadDot} />}
            <RTLTouchableOpacity
              style={styles.deleteButton}
              onPress={() => handleDeleteNotification(item.id)}
            >
              <RTLIcon name="close" size={16} color="#CCCCCC" />
            </RTLTouchableOpacity>
          </RTLView>
        </RTLView>
      </RTLTouchableOpacity>
    </Card>
  );

  const renderHeader = () => (
    <RTLView style={styles.header}>
      <RTLText style={styles.headerTitle}>Notifications</RTLText>
      <RTLTouchableOpacity onPress={onClose}>
        <RTLIcon name="close" size={24} color="#667eea" />
      </RTLTouchableOpacity>
    </RTLView>
  );

  const renderFilters = () => (
    <RTLView style={styles.filters}>
      <RTLView style={styles.filterButtons}>
        <RTLTouchableOpacity
          style={[styles.filterButton, filter === 'all' && styles.filterButtonActive]}
          onPress={() => setFilter('all')}
        >
          <RTLText style={[styles.filterButtonText, filter === 'all' && styles.filterButtonTextActive]}>
            {t('notifications.all')} ({notifications.length})
          </RTLText>
        </RTLTouchableOpacity>
        <RTLTouchableOpacity
          style={[styles.filterButton, filter === 'unread' && styles.filterButtonActive]}
          onPress={() => setFilter('unread')}
        >
          <RTLText style={[styles.filterButtonText, filter === 'unread' && styles.filterButtonTextActive]}>
            {t('notifications.unread')} ({notifications.filter(n => !n.read).length})
          </RTLText>
        </RTLTouchableOpacity>
      </RTLView>

      <RTLView style={styles.actionButtons}>
        {notifications.some(n => !n.read) && (
          <RTLTouchableOpacity style={styles.actionButton} onPress={handleMarkAllRead}>
            <RTLText style={styles.actionButtonText}>Mark All Read</RTLText>
          </RTLTouchableOpacity>
        )}
        {notifications.length > 0 && (
          <RTLTouchableOpacity style={styles.actionButton} onPress={handleClearAll}>
            <RTLText style={[styles.actionButtonText, styles.destructiveText]}>Clear All</RTLText>
          </RTLTouchableOpacity>
        )}
      </RTLView>
    </RTLView>
  );

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <RTLView style={styles.container}>
        {renderHeader()}
        {renderFilters()}

        <RTLFlatList
          data={filteredNotifications}
          renderItem={renderNotification}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.listContent}
          ListEmptyComponent={
            <EmptyState
              icon="notifications-outline"
              title={filter === 'unread' ? t('notifications.noUnreadNotifications') : t('notifications.noNotifications')}
              description={
                filter === 'unread'
                  ? t('notifications.allCaughtUp')
                  : t('notifications.noNotificationsDescription')
              }
            />
          }
          showsVerticalScrollIndicator={false}
        />
      </RTLView>
    </Modal>
  );
};

const createStyles = (colors: ThemeColors) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    header: {
      flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: SPACING.lg,
      paddingVertical: SPACING.md,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    headerTitle: {
      fontSize: FONT_SIZES.lg,
      fontWeight: FONT_WEIGHTS.bold,
      color: colors.textPrimary,
    },
    filters: {
      paddingHorizontal: SPACING.lg,
      paddingVertical: SPACING.md,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    filterButtons: {
      flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
      marginBottom: SPACING.sm,
    },
    filterButton: {
      paddingHorizontal: SPACING.md,
      paddingVertical: SPACING.sm,
      borderRadius: BORDER_RADIUS.full,
      backgroundColor: colors.surface,
      marginRight: SPACING.sm,
      borderWidth: 1,
      borderColor: colors.border,
    },
    filterButtonActive: {
      backgroundColor: '#667eea',
      borderColor: '#667eea',
    },
    filterButtonText: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
      fontWeight: FONT_WEIGHTS.medium,
    },
    filterButtonTextActive: {
      color: '#FFFFFF',
    },
    actionButtons: {
      flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
      gap: SPACING.md,
    },
    actionButton: {
      paddingVertical: SPACING.xs,
    },
    actionButtonText: {
      fontSize: FONT_SIZES.sm,
      color: '#667eea',
      fontWeight: FONT_WEIGHTS.medium,
    },
    destructiveText: {
      color: '#FF6B6B',
    },
    listContent: {
      padding: SPACING.lg,
    },
    notificationCard: {
      marginBottom: SPACING.md,
    },
    unreadCard: {
      borderLeftWidth: 4,
      borderLeftColor: '#667eea',
    },
    notificationContent: {
      padding: SPACING.md,
    },
    notificationHeader: {
      flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
      alignItems: 'flex-start',
    },
    notificationIcon: {
      position: 'relative',
      marginRight: SPACING.md,
    },
    iconContainer: {
      width: 40,
      height: 40,
      borderRadius: 20,
      justifyContent: 'center',
      alignItems: 'center',
    },
    priorityBadge: {
      position: 'absolute',
      top: -4,
      right: -4,
      width: 16,
      height: 16,
      borderRadius: 8,
      justifyContent: 'center',
      alignItems: 'center',
    },
    notificationInfo: {
      flex: 1,
      marginRight: SPACING.sm,
    },
    notificationTitle: {
      fontSize: FONT_SIZES.md,
      fontWeight: FONT_WEIGHTS.medium,
      color: colors.textPrimary,
      marginBottom: SPACING.xs,
    },
    unreadTitle: {
      fontWeight: FONT_WEIGHTS.bold,
    },
    notificationBody: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
      lineHeight: 18,
      marginBottom: SPACING.xs,
    },
    notificationTime: {
      fontSize: FONT_SIZES.xs,
      color: colors.textSecondary,
      opacity: 0.8,
    },
    notificationActions: {
      alignItems: 'center',
    },
    unreadDot: {
      width: 8,
      height: 8,
      borderRadius: 4,
      backgroundColor: colors.primary,
      marginBottom: SPACING.sm,
    },
    deleteButton: {
      padding: SPACING.xs,
    },
  });
