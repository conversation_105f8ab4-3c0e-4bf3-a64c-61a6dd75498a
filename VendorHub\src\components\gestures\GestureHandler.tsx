import React, { useRef, useState } from 'react';
import { Animated, PanResponder, Dimensions, Vibration } from 'react-native';
import { RTLView } from '../RTL';
import { useThemedStyles } from '../../hooks';
import type { ThemeColors } from '../../contexts/ThemeContext';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface GestureHandlerProps {
  children: React.ReactNode;
  onSwipeLeft?: () => void;
  onSwipeRight?: () => void;
  onSwipeUp?: () => void;
  onSwipeDown?: () => void;
  onPinchIn?: (scale: number) => void;
  onPinchOut?: (scale: number) => void;
  onLongPress?: () => void;
  onDoubleTap?: () => void;
  onPullToRefresh?: () => void;
  onShake?: () => void;
  enableSwipe?: boolean;
  enablePinch?: boolean;
  enableLongPress?: boolean;
  enableDoubleTap?: boolean;
  enablePullToRefresh?: boolean;
  enableShake?: boolean;
  swipeThreshold?: number;
  pinchThreshold?: number;
  longPressDelay?: number;
  doubleTapDelay?: number;
  shakeThreshold?: number;
  hapticFeedback?: boolean;
  style?: any;
}

export const GestureHandler: React.FC<GestureHandlerProps> = ({
  children,
  onSwipeLeft,
  onSwipeRight,
  onSwipeUp,
  onSwipeDown,
  onPinchIn,
  onPinchOut,
  onLongPress,
  onDoubleTap,
  onPullToRefresh,
  onShake,
  enableSwipe = true,
  enablePinch = false,
  enableLongPress = true,
  enableDoubleTap = true,
  enablePullToRefresh = false,
  enableShake = false,
  swipeThreshold = 50,
  pinchThreshold = 0.1,
  longPressDelay = 500,
  doubleTapDelay = 300,
  shakeThreshold = 1.5,
  hapticFeedback = true,
  style,
}) => {
  const styles = useThemedStyles(createStyles);
  
  // Animation values
  const translateX = useRef(new Animated.Value(0)).current;
  const translateY = useRef(new Animated.Value(0)).current;
  const scale = useRef(new Animated.Value(1)).current;
  const opacity = useRef(new Animated.Value(1)).current;
  
  // State management
  const [isLongPressing, setIsLongPressing] = useState(false);
  const [lastTap, setLastTap] = useState(0);
  const [initialDistance, setInitialDistance] = useState(0);
  const [isPinching, setIsPinching] = useState(false);
  
  // Timers
  const longPressTimer = useRef<NodeJS.Timeout>();
  const doubleTapTimer = useRef<NodeJS.Timeout>();

  // Helper functions
  const triggerHaptic = () => {
    if (hapticFeedback) {
      Vibration.vibrate(50);
    }
  };

  const resetAnimations = () => {
    Animated.parallel([
      Animated.spring(translateX, { toValue: 0, useNativeDriver: true }),
      Animated.spring(translateY, { toValue: 0, useNativeDriver: true }),
      Animated.spring(scale, { toValue: 1, useNativeDriver: true }),
      Animated.spring(opacity, { toValue: 1, useNativeDriver: true }),
    ]).start();
  };

  const getDistance = (touches: any[]) => {
    if (touches.length < 2) return 0;
    const [touch1, touch2] = touches;
    return Math.sqrt(
      Math.pow(touch2.pageX - touch1.pageX, 2) + 
      Math.pow(touch2.pageY - touch1.pageY, 2)
    );
  };

  // Shake detection
  React.useEffect(() => {
    if (!enableShake) return;

    let subscription: any;
    
    const startShakeDetection = async () => {
      try {
        // Fallback shake detection using gesture patterns
        // In a real implementation, you would use expo-sensors or react-native-sensors
        console.warn('Shake detection requires expo-sensors package. Install with: expo install expo-sensors');

        // Alternative: Use rapid gesture patterns as shake detection
        let rapidGestureCount = 0;
        const rapidGestureThreshold = 3;
        const rapidGestureWindow = 1000; // 1 second

        const detectRapidGestures = () => {
          rapidGestureCount++;
          if (rapidGestureCount >= rapidGestureThreshold) {
            triggerHaptic();
            onShake?.();
            rapidGestureCount = 0;
          }

          setTimeout(() => {
            rapidGestureCount = Math.max(0, rapidGestureCount - 1);
          }, rapidGestureWindow);
        };

        // Store the detection function for use in gesture handlers
        (window as any).__detectRapidGestures = detectRapidGestures;
      } catch (error) {
        console.warn('Shake detection not available:', error);
      }
    };

    startShakeDetection();
    
    return () => {
      subscription?.remove();
    };
  }, [enableShake, shakeThreshold, onShake]);

  // Pan responder for gesture handling
  const panResponder = PanResponder.create({
    onMoveShouldSetPanResponder: (_, gestureState) => {
      const { dx, dy, numberActiveTouches } = gestureState;
      
      // Handle pinch gestures
      if (enablePinch && numberActiveTouches === 2) {
        return true;
      }
      
      // Handle swipe gestures
      if (enableSwipe && (Math.abs(dx) > 10 || Math.abs(dy) > 10)) {
        return true;
      }
      
      return false;
    },

    onPanResponderGrant: (evt, gestureState) => {
      const { numberActiveTouches } = gestureState;
      
      // Handle long press
      if (enableLongPress && numberActiveTouches === 1) {
        longPressTimer.current = setTimeout(() => {
          setIsLongPressing(true);
          triggerHaptic();
          onLongPress?.();
        }, longPressDelay);
      }
      
      // Handle pinch start
      if (enablePinch && numberActiveTouches === 2) {
        const distance = getDistance(evt.nativeEvent.touches);
        setInitialDistance(distance);
        setIsPinching(true);
      }
    },

    onPanResponderMove: (evt, gestureState) => {
      const { dx, dy, numberActiveTouches } = gestureState;
      
      // Clear long press if moved too much
      if (Math.abs(dx) > 10 || Math.abs(dy) > 10) {
        if (longPressTimer.current) {
          clearTimeout(longPressTimer.current);
          setIsLongPressing(false);
        }
      }
      
      // Handle pinch gestures
      if (enablePinch && numberActiveTouches === 2 && isPinching) {
        const currentDistance = getDistance(evt.nativeEvent.touches);
        const scaleValue = currentDistance / initialDistance;
        
        scale.setValue(scaleValue);
        
        if (scaleValue > 1 + pinchThreshold) {
          onPinchOut?.(scaleValue);
        } else if (scaleValue < 1 - pinchThreshold) {
          onPinchIn?.(scaleValue);
        }
      }
      
      // Handle swipe preview
      if (enableSwipe && numberActiveTouches === 1) {
        translateX.setValue(dx * 0.3);
        translateY.setValue(dy * 0.3);
        
        // Add resistance at edges
        const resistanceX = Math.abs(dx) > swipeThreshold ? 0.1 : 0.3;
        const resistanceY = Math.abs(dy) > swipeThreshold ? 0.1 : 0.3;
        
        translateX.setValue(dx * resistanceX);
        translateY.setValue(dy * resistanceY);
      }
    },

    onPanResponderRelease: (_, gestureState) => {
      const { dx, dy, vx, vy } = gestureState;
      
      // Clear timers
      if (longPressTimer.current) {
        clearTimeout(longPressTimer.current);
      }
      
      setIsLongPressing(false);
      setIsPinching(false);
      
      // Handle swipe gestures
      if (enableSwipe) {
        const velocityThreshold = 0.5;
        const isSwipe = Math.abs(vx) > velocityThreshold || Math.abs(vy) > velocityThreshold;
        
        if (isSwipe || Math.abs(dx) > swipeThreshold || Math.abs(dy) > swipeThreshold) {
          if (Math.abs(dx) > Math.abs(dy)) {
            // Horizontal swipe
            if (dx > 0) {
              triggerHaptic();
              onSwipeRight?.();
            } else {
              triggerHaptic();
              onSwipeLeft?.();
            }
          } else {
            // Vertical swipe
            if (dy > 0) {
              if (enablePullToRefresh && dy > swipeThreshold * 2) {
                triggerHaptic();
                onPullToRefresh?.();
              } else {
                triggerHaptic();
                onSwipeDown?.();
              }
            } else {
              triggerHaptic();
              onSwipeUp?.();
            }
          }
        }
      }
      
      // Handle double tap
      if (enableDoubleTap && Math.abs(dx) < 10 && Math.abs(dy) < 10) {
        const now = Date.now();
        if (now - lastTap < doubleTapDelay) {
          triggerHaptic();
          onDoubleTap?.();
          setLastTap(0);
        } else {
          setLastTap(now);
        }
      }
      
      // Reset animations
      resetAnimations();
    },

    onPanResponderTerminate: () => {
      if (longPressTimer.current) {
        clearTimeout(longPressTimer.current);
      }
      setIsLongPressing(false);
      setIsPinching(false);
      resetAnimations();
    },
  });

  return (
    <Animated.View
      style={[
        styles.container,
        {
          transform: [
            { translateX },
            { translateY },
            { scale },
          ],
          opacity,
        },
        style,
      ]}
      {...panResponder.panHandlers}
    >
      {children}
    </Animated.View>
  );
};

const createStyles = (colors: ThemeColors) => ({
  container: {
    flex: 1,
  },
});
