import React, { useState, useEffect, useRef } from 'react';
import { StyleSheet, Dimensions, Animated, PanResponder } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { RTLView, RTLText, RTLTouchableOpacity, RTLIcon } from '../RTL';
import { useThemedStyles, useVendors, useI18n, useRTL } from '../../hooks';
import { SPACING, FONT_SIZES, FONT_WEIGHTS, BORDER_RADIUS } from '../../constants/theme';
import { PREMIUM_GRADIENTS } from '../../constants/advancedColors';
import { SubtleGlow } from '../MoonlightEffects';
import type { ThemeColors } from '../../contexts/ThemeContext';
import type { Vendor } from '../../contexts/DataContext';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');
const CAROUSEL_HEIGHT = screenHeight * 0.35;
const CARD_WIDTH = screenWidth * 0.85;
const CARD_SPACING = 20;

interface HeroCarouselProps {
  onVendorPress: (vendorId: string) => void;
  autoRotate?: boolean;
  autoRotateInterval?: number;
}

interface FeaturedVendor extends Vendor {
  heroImage?: string;
  heroTitle?: string;
  heroDescription?: string;
  isSpotlight?: boolean;
}

export const HeroCarousel: React.FC<HeroCarouselProps> = ({
  onVendorPress,
  autoRotate = true,
  autoRotateInterval = 5000,
}) => {
  const styles = useThemedStyles(createStyles);
  const { getApprovedVendors } = useVendors();
  const { t } = useI18n();
  const { isRTL } = useRTL();
  
  const [currentIndex, setCurrentIndex] = useState(0);
  const scrollX = useRef(new Animated.Value(0)).current;
  const autoRotateRef = useRef<NodeJS.Timeout>();
  
  // Get featured vendors (top rated with high product count)
  const allVendors = getApprovedVendors();
  const featuredVendors: FeaturedVendor[] = allVendors
    .filter(vendor => vendor.rating >= 4.0)
    .sort((a, b) => b.rating - a.rating)
    .slice(0, 5)
    .map(vendor => ({
      ...vendor,
      heroTitle: vendor.businessName,
      heroDescription: vendor.businessDescription || t('vendor.discoverProducts'),
      isSpotlight: vendor.rating >= 4.5,
    }));

  // Auto-rotation effect
  useEffect(() => {
    if (autoRotate && featuredVendors.length > 1) {
      autoRotateRef.current = setInterval(() => {
        setCurrentIndex(prev => (prev + 1) % featuredVendors.length);
      }, autoRotateInterval);
    }

    return () => {
      if (autoRotateRef.current) {
        clearInterval(autoRotateRef.current);
      }
    };
  }, [autoRotate, autoRotateInterval, featuredVendors.length]);

  // Animated scroll to index
  useEffect(() => {
    const targetX = currentIndex * (CARD_WIDTH + CARD_SPACING);
    Animated.spring(scrollX, {
      toValue: targetX,
      useNativeDriver: false,
      tension: 100,
      friction: 8,
    }).start();
  }, [currentIndex, scrollX]);

  const renderVendorCard = (vendor: FeaturedVendor, index: number) => {
    const inputRange = [
      (index - 1) * (CARD_WIDTH + CARD_SPACING),
      index * (CARD_WIDTH + CARD_SPACING),
      (index + 1) * (CARD_WIDTH + CARD_SPACING),
    ];

    const scale = scrollX.interpolate({
      inputRange,
      outputRange: [0.9, 1, 0.9],
      extrapolate: 'clamp',
    });

    const opacity = scrollX.interpolate({
      inputRange,
      outputRange: [0.7, 1, 0.7],
      extrapolate: 'clamp',
    });

    return (
      <Animated.View
        key={vendor.id}
        style={[
          styles.cardContainer,
          {
            transform: [{ scale }],
            opacity,
          },
        ]}
      >
        <RTLTouchableOpacity
          style={styles.card}
          onPress={() => onVendorPress(vendor.id)}
          activeOpacity={0.9}
        >
          <SubtleGlow intensity={vendor.isSpotlight ? 0.8 : 0.4}>
            <LinearGradient
              colors={vendor.isSpotlight ? PREMIUM_GRADIENTS.royalSpotlight : PREMIUM_GRADIENTS.elegantDepth}
              style={styles.cardGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
            >
              {/* Spotlight Badge */}
              {vendor.isSpotlight && (
                <RTLView style={styles.spotlightBadge}>
                  <RTLIcon name="star" size={16} color="#FFD700" />
                  <RTLText style={styles.spotlightText}>{t('vendor.spotlight')}</RTLText>
                </RTLView>
              )}

              {/* Vendor Content */}
              <RTLView style={styles.cardContent}>
                <RTLView style={styles.vendorHeader}>
                  <RTLView style={styles.vendorLogo}>
                    <RTLIcon name="storefront" size={48} color="#3B82F6" />
                  </RTLView>
                  <RTLView style={styles.vendorInfo}>
                    <RTLText style={styles.vendorName} numberOfLines={1}>
                      {vendor.heroTitle}
                    </RTLText>
                    <RTLText style={styles.vendorDescription} numberOfLines={2}>
                      {vendor.heroDescription}
                    </RTLText>
                  </RTLView>
                </RTLView>

                {/* Stats Row */}
                <RTLView style={styles.statsRow}>
                  <RTLView style={styles.statItem}>
                    <RTLIcon name="star" size={16} color="#FFD700" />
                    <RTLText style={styles.statText}>{vendor.rating.toFixed(1)}</RTLText>
                  </RTLView>
                  <RTLView style={styles.statItem}>
                    <RTLIcon name="cube-outline" size={16} color="#3B82F6" />
                    <RTLText style={styles.statText}>
                      {t('vendor.products', { count: vendor.productCount || 0 })}
                    </RTLText>
                  </RTLView>
                  <RTLView style={styles.statItem}>
                    <RTLIcon name="time-outline" size={16} color="#10B981" />
                    <RTLText style={styles.statText}>{t('vendor.active')}</RTLText>
                  </RTLView>
                </RTLView>

                {/* Action Button */}
                <RTLView style={styles.actionContainer}>
                  <LinearGradient
                    colors={['#3B82F6', '#1D4ED8']}
                    style={styles.actionButton}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 0 }}
                  >
                    <RTLText style={styles.actionText}>{t('vendor.exploreShop')}</RTLText>
                    <RTLIcon name="arrow-forward" size={16} color="#FFFFFF" />
                  </LinearGradient>
                </RTLView>
              </RTLView>
            </LinearGradient>
          </SubtleGlow>
        </RTLTouchableOpacity>
      </Animated.View>
    );
  };

  if (featuredVendors.length === 0) {
    return null;
  }

  return (
    <RTLView style={styles.container}>
      {/* Header */}
      <RTLView style={styles.header}>
        <RTLText style={styles.title}>{t('vendor.featuredVendors')}</RTLText>
        <RTLText style={styles.subtitle}>{t('vendor.discoverBestShops')}</RTLText>
      </RTLView>

      {/* Carousel */}
      <RTLView style={styles.carouselContainer}>
        <Animated.ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          scrollEventThrottle={16}
          snapToInterval={CARD_WIDTH + CARD_SPACING}
          decelerationRate="fast"
          contentContainerStyle={styles.scrollContent}
          onScroll={Animated.event(
            [{ nativeEvent: { contentOffset: { x: scrollX } } }],
            { useNativeDriver: false }
          )}
          onMomentumScrollEnd={(event) => {
            const newIndex = Math.round(event.nativeEvent.contentOffset.x / (CARD_WIDTH + CARD_SPACING));
            setCurrentIndex(newIndex);
          }}
        >
          {featuredVendors.map((vendor, index) => renderVendorCard(vendor, index))}
        </Animated.ScrollView>
      </RTLView>

      {/* Pagination Dots */}
      <RTLView style={styles.pagination}>
        {featuredVendors.map((_, index) => (
          <RTLTouchableOpacity
            key={index}
            style={[
              styles.paginationDot,
              index === currentIndex && styles.paginationDotActive,
            ]}
            onPress={() => setCurrentIndex(index)}
          />
        ))}
      </RTLView>
    </RTLView>
  );
};

const createStyles = (colors: ThemeColors) => StyleSheet.create({
  container: {
    marginBottom: SPACING.lg,
  },
  header: {
    paddingHorizontal: SPACING.md,
    marginBottom: SPACING.md,
  },
  title: {
    fontSize: FONT_SIZES.xl,
    fontWeight: FONT_WEIGHTS.bold,
    color: colors.textPrimary,
    marginBottom: SPACING.xs,
  },
  subtitle: {
    fontSize: FONT_SIZES.sm,
    color: colors.textSecondary,
  },
  carouselContainer: {
    height: CAROUSEL_HEIGHT,
  },
  scrollContent: {
    paddingHorizontal: SPACING.md,
  },
  cardContainer: {
    width: CARD_WIDTH,
    height: CAROUSEL_HEIGHT - 40,
    marginHorizontal: CARD_SPACING / 2,
  },
  card: {
    flex: 1,
    borderRadius: BORDER_RADIUS.xl,
    overflow: 'hidden',
  },
  cardGradient: {
    flex: 1,
    padding: SPACING.lg,
    justifyContent: 'space-between',
  },
  spotlightBadge: {
    position: 'absolute',
    top: SPACING.md,
    right: SPACING.md,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 215, 0, 0.2)',
    paddingHorizontal: SPACING.sm,
    paddingVertical: SPACING.xs,
    borderRadius: BORDER_RADIUS.md,
    borderWidth: 1,
    borderColor: '#FFD700',
  },
  spotlightText: {
    fontSize: FONT_SIZES.xs,
    fontWeight: FONT_WEIGHTS.semiBold,
    color: '#FFD700',
    marginLeft: SPACING.xs,
  },
  cardContent: {
    flex: 1,
    justifyContent: 'space-between',
  },
  vendorHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: SPACING.md,
  },
  vendorLogo: {
    width: 60,
    height: 60,
    borderRadius: BORDER_RADIUS.lg,
    backgroundColor: 'rgba(59, 130, 246, 0.1)',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: SPACING.md,
  },
  vendorInfo: {
    flex: 1,
  },
  vendorName: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.bold,
    color: '#FFFFFF',
    marginBottom: SPACING.xs,
  },
  vendorDescription: {
    fontSize: FONT_SIZES.sm,
    color: 'rgba(255, 255, 255, 0.8)',
    lineHeight: 20,
  },
  statsRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.lg,
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: SPACING.lg,
  },
  statText: {
    fontSize: FONT_SIZES.xs,
    color: 'rgba(255, 255, 255, 0.9)',
    marginLeft: SPACING.xs,
    fontWeight: FONT_WEIGHTS.medium,
  },
  actionContainer: {
    alignItems: 'flex-start',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
    borderRadius: BORDER_RADIUS.lg,
  },
  actionText: {
    fontSize: FONT_SIZES.sm,
    fontWeight: FONT_WEIGHTS.semiBold,
    color: '#FFFFFF',
    marginRight: SPACING.sm,
  },
  pagination: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: SPACING.md,
  },
  paginationDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: 'rgba(59, 130, 246, 0.3)',
    marginHorizontal: 4,
  },
  paginationDotActive: {
    backgroundColor: '#3B82F6',
    width: 24,
  },
});
