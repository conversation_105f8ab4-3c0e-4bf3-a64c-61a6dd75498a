# Optional Dependencies

This document lists optional dependencies that can enhance the VendorHub app functionality but are not required for basic operation.

## Gesture & Motion Features

### expo-sensors
**Purpose**: Enables shake gesture detection for the ShakeToShuffle component
**Installation**: 
```bash
expo install expo-sensors
```
**Features Enabled**:
- Real shake detection using device accelerometer
- Shake intensity measurement
- Automatic shake gesture recognition

**Fallback**: Without this package, shake detection uses rapid tap patterns as an alternative.

## Media Features

### expo-av
**Purpose**: Enables video playback in VideoPreview components
**Installation**:
```bash
expo install expo-av
```
**Features Enabled**:
- Video playback with controls
- Video thumbnails and previews
- Audio/video streaming
- Playback status tracking

**Fallback**: Without this package, video components show a placeholder with installation instructions.

## Installation Instructions

To install all optional dependencies at once:

```bash
expo install expo-sensors expo-av
```

## Development Notes

- All components gracefully handle missing dependencies
- Fallback implementations are provided for core functionality
- Development mode includes test buttons for gesture features when sensors are unavailable
- Console warnings will indicate when optional packages are missing

## Production Recommendations

For the best user experience in production, it's recommended to install all optional dependencies:

1. **expo-sensors**: Essential for intuitive shake gestures
2. **expo-av**: Important for rich media experiences

These packages are lightweight and significantly enhance the user experience without affecting app performance.
