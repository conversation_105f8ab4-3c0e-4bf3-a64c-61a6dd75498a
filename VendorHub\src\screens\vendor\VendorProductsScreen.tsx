import React, { useState, useCallback, useMemo } from 'react';
import { StyleSheet, RefreshControl, Alert } from 'react-native';
import { RTLFlatList, RTLIcon, RTLSafeAreaView, RTLText, RTLTouchableOpacity, RTLView } from '../../components/RTL';
import { useThemedStyles, useAuth, useProducts, useDebounce, useI18n } from '../../hooks';
import { Card, Button, Input, EmptyState, StatusBadge, SwipeableRow, SwipeActions, OptimizedFlatList, useOptimizedSearch, usePaginatedData, FloatingActionButton } from '../../components';
import {
  SPACING,
  FONT_SIZES,
  FONT_WEIGHTS,
  BORDER_RADIUS,
  ICON_SIZES } from '../../constants/theme';
import { formatCurrency } from '../../utils';
import type { ThemeColors } from '../../contexts/ThemeContext';
import type { Product } from '../../contexts/DataContext';

interface VendorProductsScreenProps {
  navigation: any;
}

export const VendorProductsScreen: React.FC<VendorProductsScreenProps> = ({ navigation }) => {
  const styles = useThemedStyles(createStyles);
  const { user } = useAuth();
  const { getProductsByVendor, deleteProduct } = useProducts();
  const { t } = useI18n();
  const [refreshing, setRefreshing] = useState(false);
  const [selectedFilter, setSelectedFilter] = useState<'all' | 'active' | 'inactive' | 'low_stock'>('all');

  // Get vendor's products
  const vendorProducts = user ? getProductsByVendor(user.id) : [];

  // Optimized search with debouncing
  const { searchQuery, setSearchQuery, filteredData: searchFilteredProducts } = useOptimizedSearch(
    vendorProducts,
    ['name', 'description', 'category'],
    300
  );

  // Apply additional filters to search results
  const filteredProducts = React.useMemo(() => {
    let result = searchFilteredProducts;

    // Apply status filter
    switch (selectedFilter) {
      case 'active':
        result = result.filter(product => product.isActive);
        break;
      case 'inactive':
        result = result.filter(product => !product.isActive);
        break;
      case 'low_stock':
        result = result.filter(product => product.inventory <= 10 && product.inventory > 0);
        break;
      default:
        break;
    }

    return result;
  }, [searchFilteredProducts, selectedFilter]);

  // Pagination for large product lists
  const { data: paginatedProducts, hasMore, loadMore } = usePaginatedData(filteredProducts, 20);

  const onRefresh = React.useCallback(async () => {
    setRefreshing(true);
    // Simulate refresh delay
    setTimeout(() => setRefreshing(false), 1000);
  }, []);

  const handleAddProduct = useCallback(() => {
    navigation.navigate('AddProduct');
  }, [navigation]);

  const handleEditProduct = useCallback((productId: string) => {
    navigation.navigate('EditProduct', { productId });
  }, [navigation]);

  const handleDeleteProduct = useCallback((product: Product) => {
    Alert.alert(
      t('products.deleteProduct'),
      t('products.deleteProductConfirmation', { productName: product.name }),
      [
        { text: t('common.cancel'), style: 'cancel' },
        {
          text: t('common.delete'),
          style: 'destructive',
          onPress: () => {
            deleteProduct(product.id);
            Alert.alert(t('common.success'), t('products.productDeletedSuccessfully'));
          },
        },
      ]
    );
  }, [t, deleteProduct]);

  const handleToggleStatus = useCallback((product: Product) => {
    // This would typically call updateProduct
    Alert.alert(
      product.isActive ? t('products.deactivateProduct') : t('products.activateProduct'),
      t('products.toggleProductStatusConfirmation', {
        action: product.isActive ? t('products.deactivate') : t('products.activate'),
        productName: product.name
      }),
      [
        { text: t('common.cancel'), style: 'cancel' },
        {
          text: product.isActive ? t('products.deactivate') : t('products.activate'),
          onPress: () => {
            // updateProduct(product.id, { isActive: !product.isActive });
            Alert.alert(t('common.success'), t('products.productStatusUpdatedSuccessfully'));
          },
        },
      ]
    );
  }, [t]);

  const getStockStatus = useCallback((product: Product) => {
    if (product.inventory === 0) return { status: t('products.outOfStock'), variant: 'error' as const };
    if (product.inventory <= 10) return { status: t('products.lowStock'), variant: 'warning' as const };
    return { status: t('products.inStock'), variant: 'success' as const };
  }, [t]);

  const renderProductItem = useCallback(({ item }: { item: Product }) => {
    const stockStatus = getStockStatus(item);

    // Define swipe actions for products
    const swipeActions = {
      leftActions: [
        SwipeActions.edit(() => handleEditProduct(item.id)),
      ],
      rightActions: [
        SwipeActions.delete(() => handleDeleteProduct(item)),
      ],
    };

    return (
      <SwipeableRow
        leftActions={swipeActions.leftActions}
        rightActions={swipeActions.rightActions}
        style={styles.swipeableContainer}
      >
        <Card style={styles.productCard} variant="elevated">
          <RTLTouchableOpacity
            style={styles.productContent}
            onPress={() => handleEditProduct(item.id)}
          >
            <RTLView style={styles.productHeader}>
              <RTLView style={styles.productImage}>
                <RTLIcon name="image-outline" size={32} color="#CCCCCC" />
              </RTLView>
              <RTLView style={styles.productInfo}>
                <RTLText style={styles.productName} numberOfLines={2}>
                  {item.name}
                </RTLText>
                <RTLText style={styles.productCategory}>{item.category}</RTLText>
                <RTLView style={styles.productPricing}>
                  <RTLText style={styles.productPrice}>
                    {formatCurrency(item.price)}
                  </RTLText>
                  {item.originalPrice && item.originalPrice > item.price && (
                    <RTLText style={styles.originalPrice}>
                      {formatCurrency(item.originalPrice)}
                    </RTLText>
                  )}
                </RTLView>
              </RTLView>
              <RTLView style={styles.productStatus}>
                <StatusBadge
                  status={item.isActive ? t('vendor.active') : t('vendor.inactive')}
                  customColor={item.isActive ? '#4CAF50' : '#F44336'}
                  style={styles.statusBadge}
                />
              </RTLView>
            </RTLView>

            <RTLView style={styles.productDetails}>
              <RTLView style={styles.productStats}>
                <RTLView style={styles.statItem}>
                  <RTLIcon name="cube-outline" size={16} color="#667eea" />
                  <RTLText style={styles.statText}>{t('products.inventoryCount', { count: item.inventory })}</RTLText>
                </RTLView>
                <RTLView style={styles.statItem}>
                  <RTLIcon name="star-outline" size={16} color="#FFD700" />
                  <RTLText style={styles.statText}>{(item.rating || 0).toFixed(1)} ({item.reviewCount || 0})</RTLText>
                </RTLView>
              </RTLView>

              <StatusBadge
                status={stockStatus.status}
                customColor={stockStatus.variant === 'success' ? '#4CAF50' : stockStatus.variant === 'warning' ? '#FF9800' : '#F44336'}
                style={styles.stockBadge}
              />
            </RTLView>

            {/* Swipe hint */}
            <RTLText style={styles.swipeHint}>
              ← Swipe to edit or delete →
            </RTLText>

            <RTLView style={styles.productActions}>
              <Button
                title={t('common.edit')}
                onPress={() => handleEditProduct(item.id)}
                variant="outline"
                size="small"
                style={styles.actionButton}
                leftIcon={<RTLIcon name="pencil-outline" size={16} color="#667eea" />}
              />
              <Button
                title={item.isActive ? t('products.deactivate') : t('products.activate')}
                onPress={() => handleToggleStatus(item)}
                variant="outline"
                size="small"
                style={styles.actionButton}
                leftIcon={
                  <RTLIcon
                    name={item.isActive ? "pause-outline" : "play-outline"}
                    size={16}
                    color="#667eea"
                  />
                }
              />
              <Button
                title={t('common.delete')}
                onPress={() => handleDeleteProduct(item)}
                variant="outline"
                size="small"
                style={[styles.actionButton, styles.deleteButton].filter(Boolean) as any}
                leftIcon={<RTLIcon name="trash-outline" size={16} color="#FF6B6B" />}
              />
            </RTLView>
          </RTLTouchableOpacity>
        </Card>
      </SwipeableRow>
    );
  }, [t, formatCurrency, handleEditProduct, handleDeleteProduct, handleToggleStatus]);

  const filterOptions = useMemo(() => [
    { label: t('common.all'), value: 'all' as const },
    { label: t('vendor.active'), value: 'active' as const },
    { label: t('vendor.inactive'), value: 'inactive' as const },
    { label: t('vendor.lowStock'), value: 'low_stock' as const },
  ], [t]);

  const renderHeader = useCallback(() => (
    <RTLView style={styles.header}>
      {/* Search */}
      <Input
        placeholder={t('vendor.searchProducts')}
        value={searchQuery}
        onChangeText={setSearchQuery}
        leftIcon="search-outline"
        style={styles.searchInput}
      />

      {/* Filter Chips */}
      <RTLView style={styles.filterContainer}>
        {filterOptions.map((option) => (
          <RTLTouchableOpacity
            key={option.value}
            style={[
              styles.filterChip,
              selectedFilter === option.value && styles.filterChipActive,
            ]}
            onPress={() => setSelectedFilter(option.value)}
          >
            <RTLText
              style={[
                styles.filterChipText,
                selectedFilter === option.value && styles.filterChipTextActive,
              ].filter(Boolean) as any}
            >
              {option.label}
            </RTLText>
          </RTLTouchableOpacity>
        ))}
      </RTLView>

      {/* Stats */}
      <RTLView style={styles.statsContainer}>
        <RTLText style={styles.resultsText}>
          {t('vendor.productsFound', { count: filteredProducts.length })}
        </RTLText>
      </RTLView>
    </RTLView>
  ), [t, searchQuery, setSearchQuery, filterOptions, selectedFilter, setSelectedFilter, filteredProducts.length]);

  return (
    <RTLSafeAreaView style={styles.container}>
      <OptimizedFlatList
        data={paginatedProducts}
        renderItem={renderProductItem}
        keyExtractor={(item) => item.id}
        estimatedItemSize={180}
        enableVirtualization={true}
        enableMemoryOptimization={true}
        enableScrollOptimization={true}
        ListHeaderComponent={renderHeader}
        ListEmptyComponent={
          <EmptyState
            icon="cube-outline"
            title={t('vendor.noProductsFound')}
            description={
              searchQuery || selectedFilter !== 'all'
                ? t('vendor.adjustSearchFilters')
                : t('vendor.startByAddingProduct')
            }
            actionLabel={!searchQuery && selectedFilter === 'all' ? t('vendor.addProduct') : undefined}
            onAction={!searchQuery && selectedFilter === 'all' ? handleAddProduct : undefined}
          />
        }
        onEndReached={hasMore ? loadMore : undefined}
        onEndReachedThreshold={0.1}
        contentContainerStyle={styles.listContent}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      />

      {/* Floating Action Button */}
      <FloatingActionButton
        icon="add"
        onPress={handleAddProduct}
        size="medium"
        backgroundColor="#667eea"
        position="bottom-right"
      />
    </RTLSafeAreaView>
  );
};

const createStyles = (colors: ThemeColors) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    listContent: {
      padding: SPACING.md,
      paddingBottom: 80, // Space for FAB
    },
    header: {
      marginBottom: SPACING.lg,
    },
    searchInput: {
      marginBottom: SPACING.md,
    },
    filterContainer: {
      flexDirection: 'row', // RTL handled by RTLView
      marginBottom: SPACING.md,
    },
    filterChip: {
      paddingHorizontal: SPACING.md,
      paddingVertical: SPACING.sm,
      borderRadius: BORDER_RADIUS.full,
      backgroundColor: colors.surface,
      marginHorizontal: SPACING.sm,
      borderWidth: 1,
      borderColor: colors.border,
    },
    filterChipActive: {
      backgroundColor: colors.primary,
      borderColor: colors.primary,
    },
    filterChipText: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
      fontWeight: FONT_WEIGHTS.medium,
    },
    filterChipTextActive: {
      color: '#FFFFFF',
    },
    statsContainer: {
      marginBottom: SPACING.sm,
    },
    resultsText: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
    },
    productCard: {
      marginBottom: SPACING.md,
    },
    productContent: {
      // No additional styles needed, Card handles the touch
    },
    productHeader: {
      flexDirection: 'row', // RTL handled by RTLView
      alignItems: 'flex-start',
      marginBottom: SPACING.sm,
    },
    productImage: {
      width: 60,
      height: 60,
      backgroundColor: colors.surface,
      borderRadius: BORDER_RADIUS.sm,
      justifyContent: 'center',
      alignItems: 'center',
      marginHorizontal: SPACING.sm,
    },
    productInfo: {
      flex: 1,
      marginHorizontal: SPACING.sm,
    },
    productName: {
      fontSize: FONT_SIZES.md,
      fontWeight: FONT_WEIGHTS.semibold,
      color: colors.textPrimary,
      marginBottom: SPACING.xs,
    },
    productCategory: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
      marginBottom: SPACING.xs,
    },
    productPricing: {
      flexDirection: 'row', // RTL handled by RTLView
      alignItems: 'center',
    },
    productPrice: {
      fontSize: FONT_SIZES.md,
      fontWeight: FONT_WEIGHTS.bold,
      color: '#667eea',
      marginHorizontal: SPACING.sm,
    },
    originalPrice: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
      textDecorationLine: 'line-through',
    },
    productStatus: {
      alignItems: 'flex-end',
    },
    statusBadge: {
      marginBottom: SPACING.xs,
    },
    productDetails: {
      flexDirection: 'row', // RTL handled by RTLView
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: SPACING.md,
      paddingTop: SPACING.sm,
      borderTopWidth: 1,
      borderTopColor: colors.border,
    },
    productStats: {
      flexDirection: 'row', // RTL handled by RTLView
      flex: 1,
    },
    statItem: {
      flexDirection: 'row', // RTL handled by RTLView
      alignItems: 'center',
      marginHorizontal: SPACING.md,
    },
    statText: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
      marginHorizontal: SPACING.xs,
    },
    stockBadge: {
      // Positioned on the right
    },
    swipeHint: {
      fontSize: FONT_SIZES.xs,
      color: colors.primary,
      textAlign: 'center',
      fontStyle: 'italic',
      opacity: 0.7,
      marginVertical: SPACING.xs,
    },
    productActions: {
      flexDirection: 'row', // RTL handled by RTLView
      justifyContent: 'space-between',
      gap: SPACING.sm,
    },
    actionButton: {
      flex: 1,
    },
    deleteButton: {
      borderColor: '#FF6B6B',
    },
    swipeableContainer: {
      marginBottom: SPACING.md,
    },
  });
