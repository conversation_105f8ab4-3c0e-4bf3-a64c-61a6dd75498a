import React, { useState, useEffect, useCallback } from 'react';
import { StyleSheet, Dimensions } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { RTLView, RTLText, RTLTouchableOpacity, RTLIcon, RTLScrollView } from '../RTL';
import { useThemedStyles, useI18n } from '../../hooks';
import { useSmartRecommendationEngine } from './SmartRecommendationEngine';
import { useBehavioralAnalytics } from './BehavioralAnalytics';
import { SPACING, FONT_SIZES, FONT_WEIGHTS, BORDER_RADIUS } from '../../constants/theme';
import { PREMIUM_GRADIENTS } from '../../constants/advancedColors';
import { FadeInView, AnimatedPressable, ShimmerView } from '../visual/MicroAnimations';
import { GlassmorphismCard } from '../visual/GlassmorphismCard';
import { OptimizedImageLoader } from '../performance/OptimizedImageLoader';
import type { ThemeColors } from '../../contexts/ThemeContext';
import type { Vendor, Product } from '../../contexts/DataContext';

const { width: screenWidth } = Dimensions.get('window');

interface SmartRecommendation {
  id: string;
  type: 'vendor' | 'product';
  title: string;
  subtitle: string;
  score: number;
  confidence: number;
  reasons: string[];
  data: Vendor | Product;
  metadata: {
    algorithm: string;
    category?: string;
    trending?: boolean;
    personalized?: boolean;
    cultural?: boolean;
  };
}

interface SmartRecommendationDisplayProps {
  type?: 'vendors' | 'products' | 'mixed';
  title?: string;
  subtitle?: string;
  limit?: number;
  showReasons?: boolean;
  showConfidence?: boolean;
  layout?: 'horizontal' | 'vertical' | 'grid';
  onRecommendationPress?: (recommendation: SmartRecommendation) => void;
  onRefresh?: () => void;
  style?: any;
}

export const SmartRecommendationDisplay: React.FC<SmartRecommendationDisplayProps> = ({
  type = 'mixed',
  title,
  subtitle,
  limit = 10,
  showReasons = true,
  showConfidence = false,
  layout = 'horizontal',
  onRecommendationPress,
  onRefresh,
  style,
}) => {
  const styles = useThemedStyles(createStyles);
  const { t } = useI18n();
  const recommendationEngine = useSmartRecommendationEngine();
  const behavioralAnalytics = useBehavioralAnalytics();

  const [recommendations, setRecommendations] = useState<SmartRecommendation[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);

  const loadRecommendations = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      let results: SmartRecommendation[] = [];

      switch (type) {
        case 'vendors':
          results = await recommendationEngine.getPersonalizedVendors(limit);
          break;
        case 'products':
          results = await recommendationEngine.getPersonalizedProducts(limit);
          break;
        case 'mixed':
          const [vendors, products] = await Promise.all([
            recommendationEngine.getPersonalizedVendors(Math.ceil(limit / 2)),
            recommendationEngine.getPersonalizedProducts(Math.floor(limit / 2)),
          ]);
          results = [...vendors, ...products].sort((a, b) => b.confidence - a.confidence);
          break;
      }

      setRecommendations(results);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load recommendations');
      console.warn('Failed to load recommendations:', err);
    } finally {
      setIsLoading(false);
    }
  }, [type, limit, recommendationEngine]);

  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    await recommendationEngine.refreshRecommendations();
    await loadRecommendations();
    setRefreshing(false);
    onRefresh?.();
  }, [recommendationEngine, loadRecommendations, onRefresh]);

  const handleRecommendationPress = useCallback(async (recommendation: SmartRecommendation) => {
    // Track interaction
    await behavioralAnalytics.trackInteraction({
      type: 'tap',
      target: `recommendation_${recommendation.type}_${recommendation.id}`,
      metadata: {
        algorithm: recommendation.metadata.algorithm,
        confidence: recommendation.confidence,
        reasons: recommendation.reasons,
      },
    });

    onRecommendationPress?.(recommendation);
  }, [behavioralAnalytics, onRecommendationPress]);

  useEffect(() => {
    loadRecommendations();
  }, [loadRecommendations]);

  const renderRecommendationCard = (recommendation: SmartRecommendation, index: number) => {
    const isVendor = recommendation.type === 'vendor';
    const vendorData = isVendor ? recommendation.data as Vendor : null;
    const productData = !isVendor ? recommendation.data as Product : null;

    return (
      <FadeInView key={recommendation.id} duration={400} delay={index * 100}>
        <AnimatedPressable
          style={[
            styles.recommendationCard,
            layout === 'horizontal' && styles.horizontalCard,
            layout === 'grid' && styles.gridCard,
          ]}
          onPress={() => handleRecommendationPress(recommendation)}
        >
          <GlassmorphismCard intensity="medium" style={styles.cardGlass}>
            <LinearGradient
              colors={getRecommendationGradient(recommendation)}
              style={styles.cardGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
            >
              {/* Image */}
              <RTLView style={styles.imageContainer}>
                <OptimizedImageLoader
                  source={{ 
                    uri: isVendor ? vendorData?.logoUrl || '' : productData?.imageUrl || '',
                  }}
                  style={styles.recommendationImage}
                  enableCache={true}
                  enableLazyLoading={true}
                  borderRadius={BORDER_RADIUS.md}
                  fallback={
                    <RTLView style={styles.imageFallback}>
                      <RTLIcon 
                        name={isVendor ? 'storefront' : 'cube-outline'} 
                        size={32} 
                        color="rgba(255, 255, 255, 0.6)" 
                      />
                    </RTLView>
                  }
                />
                
                {/* Confidence Badge */}
                {showConfidence && (
                  <RTLView style={styles.confidenceBadge}>
                    <RTLText style={styles.confidenceText}>
                      {Math.round(recommendation.confidence * 100)}%
                    </RTLText>
                  </RTLView>
                )}

                {/* Algorithm Badge */}
                <RTLView style={styles.algorithmBadge}>
                  <RTLIcon 
                    name={getAlgorithmIcon(recommendation.metadata.algorithm)} 
                    size={12} 
                    color="#FFFFFF" 
                  />
                </RTLView>
              </RTLView>

              {/* Content */}
              <RTLView style={styles.cardContent}>
                <RTLText style={styles.recommendationTitle} numberOfLines={2}>
                  {recommendation.title}
                </RTLText>
                
                <RTLText style={styles.recommendationSubtitle} numberOfLines={1}>
                  {recommendation.subtitle}
                </RTLText>

                {/* Metadata Tags */}
                <RTLView style={styles.metadataTags}>
                  {recommendation.metadata.trending && (
                    <RTLView style={[styles.tag, styles.trendingTag]}>
                      <RTLIcon name="trending-up" size={10} color="#FFD700" />
                      <RTLText style={styles.tagText}>{t('ai.trending')}</RTLText>
                    </RTLView>
                  )}
                  
                  {recommendation.metadata.personalized && (
                    <RTLView style={[styles.tag, styles.personalizedTag]}>
                      <RTLIcon name="person" size={10} color="#3B82F6" />
                      <RTLText style={styles.tagText}>{t('ai.forYou')}</RTLText>
                    </RTLView>
                  )}
                  
                  {recommendation.metadata.cultural && (
                    <RTLView style={[styles.tag, styles.culturalTag]}>
                      <RTLIcon name="globe" size={10} color="#10B981" />
                      <RTLText style={styles.tagText}>{t('ai.cultural')}</RTLText>
                    </RTLView>
                  )}
                </RTLView>

                {/* Reasons */}
                {showReasons && recommendation.reasons.length > 0 && (
                  <RTLView style={styles.reasonsContainer}>
                    <RTLText style={styles.reasonText} numberOfLines={2}>
                      {recommendation.reasons[0]}
                    </RTLText>
                  </RTLView>
                )}
              </RTLView>
            </LinearGradient>
          </GlassmorphismCard>
        </AnimatedPressable>
      </FadeInView>
    );
  };

  const getRecommendationGradient = (recommendation: SmartRecommendation): string[] => {
    if (recommendation.metadata.trending) {
      return PREMIUM_GRADIENTS.royalSpotlight;
    }
    if (recommendation.metadata.personalized) {
      return PREMIUM_GRADIENTS.elegantDepth;
    }
    if (recommendation.metadata.cultural) {
      return PREMIUM_GRADIENTS.vibrantEnergy;
    }
    return PREMIUM_GRADIENTS.elegantDepth;
  };

  const getAlgorithmIcon = (algorithm: string): string => {
    if (algorithm.includes('personalized')) return 'person';
    if (algorithm.includes('trending')) return 'trending-up';
    if (algorithm.includes('content')) return 'layers';
    if (algorithm.includes('seasonal')) return 'leaf';
    if (algorithm.includes('cultural')) return 'globe';
    return 'bulb';
  };

  const renderHeader = () => {
    if (!title && !subtitle) return null;

    return (
      <RTLView style={styles.header}>
        <RTLView style={styles.headerContent}>
          {title && (
            <RTLText style={styles.headerTitle}>{title}</RTLText>
          )}
          {subtitle && (
            <RTLText style={styles.headerSubtitle}>{subtitle}</RTLText>
          )}
        </RTLView>
        
        <RTLTouchableOpacity
          style={styles.refreshButton}
          onPress={handleRefresh}
          disabled={refreshing}
        >
          <RTLIcon 
            name="refresh" 
            size={20} 
            color={refreshing ? "rgba(255, 255, 255, 0.5)" : "#3B82F6"} 
          />
        </RTLTouchableOpacity>
      </RTLView>
    );
  };

  const renderContent = () => {
    if (isLoading) {
      return (
        <RTLView style={styles.loadingContainer}>
          {Array.from({ length: 3 }).map((_, index) => (
            <ShimmerView key={index} style={styles.loadingCard}>
              <RTLView style={styles.loadingImage} />
              <RTLView style={styles.loadingText} />
              <RTLView style={styles.loadingSubtext} />
            </ShimmerView>
          ))}
        </RTLView>
      );
    }

    if (error) {
      return (
        <RTLView style={styles.errorContainer}>
          <RTLIcon name="alert-circle" size={48} color="rgba(239, 68, 68, 0.7)" />
          <RTLText style={styles.errorText}>{error}</RTLText>
          <RTLTouchableOpacity style={styles.retryButton} onPress={loadRecommendations}>
            <RTLText style={styles.retryButtonText}>{t('common.retry')}</RTLText>
          </RTLTouchableOpacity>
        </RTLView>
      );
    }

    if (recommendations.length === 0) {
      return (
        <RTLView style={styles.emptyContainer}>
          <RTLIcon name="bulb-outline" size={48} color="rgba(255, 255, 255, 0.5)" />
          <RTLText style={styles.emptyText}>{t('ai.noRecommendations')}</RTLText>
          <RTLText style={styles.emptySubtext}>{t('ai.noRecommendationsDesc')}</RTLText>
        </RTLView>
      );
    }

    if (layout === 'horizontal') {
      return (
        <RTLScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.horizontalContainer}
        >
          {recommendations.map((recommendation, index) => 
            renderRecommendationCard(recommendation, index)
          )}
        </RTLScrollView>
      );
    }

    if (layout === 'grid') {
      return (
        <RTLView style={styles.gridContainer}>
          {recommendations.map((recommendation, index) => 
            renderRecommendationCard(recommendation, index)
          )}
        </RTLView>
      );
    }

    // Vertical layout
    return (
      <RTLView style={styles.verticalContainer}>
        {recommendations.map((recommendation, index) => 
          renderRecommendationCard(recommendation, index)
        )}
      </RTLView>
    );
  };

  return (
    <RTLView style={[styles.container, style]}>
      {renderHeader()}
      {renderContent()}
    </RTLView>
  );
};

const createStyles = (colors: ThemeColors) => StyleSheet.create({
  container: {
    marginVertical: SPACING.md,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: SPACING.lg,
    marginBottom: SPACING.md,
  },
  headerContent: {
    flex: 1,
  },
  headerTitle: {
    fontSize: FONT_SIZES.xl,
    fontWeight: FONT_WEIGHTS.bold,
    color: '#FFFFFF',
    marginBottom: SPACING.xs,
  },
  headerSubtitle: {
    fontSize: FONT_SIZES.md,
    color: 'rgba(255, 255, 255, 0.8)',
  },
  refreshButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(59, 130, 246, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
  },

  // Layout containers
  horizontalContainer: {
    paddingHorizontal: SPACING.lg,
    gap: SPACING.md,
  },
  verticalContainer: {
    paddingHorizontal: SPACING.lg,
    gap: SPACING.md,
  },
  gridContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: SPACING.lg,
    gap: SPACING.md,
  },

  // Card styles
  recommendationCard: {
    borderRadius: BORDER_RADIUS.xl,
    overflow: 'hidden',
  },
  horizontalCard: {
    width: screenWidth * 0.7,
  },
  gridCard: {
    width: (screenWidth - SPACING.lg * 2 - SPACING.md) / 2,
  },
  cardGlass: {
    flex: 1,
  },
  cardGradient: {
    flex: 1,
    padding: SPACING.md,
  },

  // Image styles
  imageContainer: {
    position: 'relative',
    height: 120,
    borderRadius: BORDER_RADIUS.md,
    overflow: 'hidden',
    marginBottom: SPACING.md,
  },
  recommendationImage: {
    width: '100%',
    height: '100%',
  },
  imageFallback: {
    width: '100%',
    height: '100%',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  confidenceBadge: {
    position: 'absolute',
    top: SPACING.sm,
    right: SPACING.sm,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    paddingHorizontal: SPACING.sm,
    paddingVertical: SPACING.xs,
    borderRadius: BORDER_RADIUS.sm,
  },
  confidenceText: {
    fontSize: FONT_SIZES.xs,
    fontWeight: FONT_WEIGHTS.bold,
    color: '#FFFFFF',
  },
  algorithmBadge: {
    position: 'absolute',
    top: SPACING.sm,
    left: SPACING.sm,
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: 'rgba(59, 130, 246, 0.8)',
    alignItems: 'center',
    justifyContent: 'center',
  },

  // Content styles
  cardContent: {
    flex: 1,
  },
  recommendationTitle: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.semiBold,
    color: '#FFFFFF',
    marginBottom: SPACING.xs,
  },
  recommendationSubtitle: {
    fontSize: FONT_SIZES.sm,
    color: 'rgba(255, 255, 255, 0.8)',
    marginBottom: SPACING.sm,
  },

  // Metadata tags
  metadataTags: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: SPACING.xs,
    marginBottom: SPACING.sm,
  },
  tag: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: SPACING.sm,
    paddingVertical: SPACING.xs,
    borderRadius: BORDER_RADIUS.sm,
    gap: SPACING.xs,
  },
  trendingTag: {
    backgroundColor: 'rgba(255, 215, 0, 0.2)',
    borderColor: '#FFD700',
    borderWidth: 1,
  },
  personalizedTag: {
    backgroundColor: 'rgba(59, 130, 246, 0.2)',
    borderColor: '#3B82F6',
    borderWidth: 1,
  },
  culturalTag: {
    backgroundColor: 'rgba(16, 185, 129, 0.2)',
    borderColor: '#10B981',
    borderWidth: 1,
  },
  tagText: {
    fontSize: FONT_SIZES.xs,
    fontWeight: FONT_WEIGHTS.medium,
    color: '#FFFFFF',
  },

  // Reasons
  reasonsContainer: {
    marginTop: SPACING.xs,
  },
  reasonText: {
    fontSize: FONT_SIZES.xs,
    color: 'rgba(255, 255, 255, 0.7)',
    fontStyle: 'italic',
  },

  // Loading states
  loadingContainer: {
    flexDirection: 'row',
    paddingHorizontal: SPACING.lg,
    gap: SPACING.md,
  },
  loadingCard: {
    width: screenWidth * 0.7,
    height: 200,
    borderRadius: BORDER_RADIUS.xl,
    padding: SPACING.md,
  },
  loadingImage: {
    height: 120,
    borderRadius: BORDER_RADIUS.md,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    marginBottom: SPACING.md,
  },
  loadingText: {
    height: 16,
    borderRadius: BORDER_RADIUS.sm,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    marginBottom: SPACING.sm,
  },
  loadingSubtext: {
    height: 12,
    width: '70%',
    borderRadius: BORDER_RADIUS.sm,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },

  // Error states
  errorContainer: {
    alignItems: 'center',
    paddingVertical: SPACING.xl,
    paddingHorizontal: SPACING.lg,
  },
  errorText: {
    fontSize: FONT_SIZES.md,
    color: 'rgba(239, 68, 68, 0.8)',
    textAlign: 'center',
    marginVertical: SPACING.md,
  },
  retryButton: {
    backgroundColor: 'rgba(59, 130, 246, 0.2)',
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
    borderRadius: BORDER_RADIUS.lg,
    borderWidth: 1,
    borderColor: '#3B82F6',
  },
  retryButtonText: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.semiBold,
    color: '#3B82F6',
  },

  // Empty states
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: SPACING.xl,
    paddingHorizontal: SPACING.lg,
  },
  emptyText: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.semiBold,
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
    marginVertical: SPACING.md,
  },
  emptySubtext: {
    fontSize: FONT_SIZES.md,
    color: 'rgba(255, 255, 255, 0.6)',
    textAlign: 'center',
  },
});
