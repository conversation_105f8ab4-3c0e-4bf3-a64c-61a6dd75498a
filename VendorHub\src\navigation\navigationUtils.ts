import { type RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import type { StackNavigationProp } from '@react-navigation/stack';
import type { BottomTabNavigationProp } from '@react-navigation/bottom-tabs';
import type {
  CustomerStackParamList,
  CustomerTabParamList,
  VendorStackParamList,
  VendorTabParamList,
  AdminStackParamList,
  AdminTabParamList,
  AuthStackParamList,
} from './types';

// Navigation hook types for Customer screens
export type CustomerStackNavigationProp<T extends keyof CustomerStackParamList> = 
  StackNavigationProp<CustomerStackParamList, T>;

export type CustomerTabNavigationProp<T extends keyof CustomerTabParamList> = 
  BottomTabNavigationProp<CustomerTabParamList, T>;

export type CustomerStackRouteProp<T extends keyof CustomerStackParamList> = 
  RouteProp<CustomerStackParamList, T>;

export type CustomerTabRouteProp<T extends keyof CustomerTabParamList> = 
  RouteProp<CustomerTabParamList, T>;

// Navigation hook types for Vendor screens
export type VendorStackNavigationProp<T extends keyof VendorStackParamList> = 
  StackNavigationProp<VendorStackParamList, T>;

export type VendorTabNavigationProp<T extends keyof VendorTabParamList> = 
  BottomTabNavigationProp<VendorTabParamList, T>;

export type VendorStackRouteProp<T extends keyof VendorStackParamList> = 
  RouteProp<VendorStackParamList, T>;

export type VendorTabRouteProp<T extends keyof VendorTabParamList> = 
  RouteProp<VendorTabParamList, T>;

// Navigation hook types for Admin screens
export type AdminStackNavigationProp<T extends keyof AdminStackParamList> = 
  StackNavigationProp<AdminStackParamList, T>;

export type AdminTabNavigationProp<T extends keyof AdminTabParamList> = 
  BottomTabNavigationProp<AdminTabParamList, T>;

// Navigation hook types for Auth screens
export type AuthStackNavigationProp<T extends keyof AuthStackParamList> = 
  StackNavigationProp<AuthStackParamList, T>;

export type AuthStackRouteProp<T extends keyof AuthStackParamList> = 
  RouteProp<AuthStackParamList, T>;

// Utility hooks for type-safe navigation
export const useCustomerStackNavigation = <T extends keyof CustomerStackParamList>() =>
  useNavigation<CustomerStackNavigationProp<T>>();

export const useCustomerTabNavigation = <T extends keyof CustomerTabParamList>() =>
  useNavigation<CustomerTabNavigationProp<T>>();

export const useVendorStackNavigation = <T extends keyof VendorStackParamList>() =>
  useNavigation<VendorStackNavigationProp<T>>();

export const useVendorTabNavigation = <T extends keyof VendorTabParamList>() =>
  useNavigation<VendorTabNavigationProp<T>>();

export const useAdminStackNavigation = <T extends keyof AdminStackParamList>() =>
  useNavigation<AdminStackNavigationProp<T>>();

export const useAdminTabNavigation = <T extends keyof AdminTabParamList>() =>
  useNavigation<AdminTabNavigationProp<T>>();

export const useAuthStackNavigation = <T extends keyof AuthStackParamList>() =>
  useNavigation<AuthStackNavigationProp<T>>();

// Route hooks
export const useCustomerStackRoute = <T extends keyof CustomerStackParamList>() =>
  useRoute<CustomerStackRouteProp<T>>();

export const useCustomerTabRoute = <T extends keyof CustomerTabParamList>() =>
  useRoute<CustomerTabRouteProp<T>>();

export const useVendorStackRoute = <T extends keyof VendorStackParamList>() =>
  useRoute<VendorStackRouteProp<T>>();

export const useVendorTabRoute = <T extends keyof VendorTabParamList>() =>
  useRoute<VendorTabRouteProp<T>>();

export const useAuthStackRoute = <T extends keyof AuthStackParamList>() =>
  useRoute<AuthStackRouteProp<T>>();
