import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  RefreshControl,
  Image,
  Alert } from 'react-native';
import { useThemedStyles, useAuth, useI18n } from '../../hooks';
import { Card, EmptyState, Input } from '../../components';
import { RTLView, RTLText, RTLIcon, RTLSafeAreaView, RTLFlatList, RTLTouchableOpacity } from '../../components/RTL';
import ChatService, { ChatConversation } from '../../services/ChatService';
import {
  SPACING,
  FONT_SIZES,
  FONT_WEIGHTS,
  BORDER_RADIUS } from '../../constants/theme';
import type ThemeColors  from '../../contexts/ThemeContext';

interface ChatListScreenProps {
  navigation: any;
}

export const ChatListScreen: React.FC<ChatListScreenProps> = ({ navigation }) => {
  const styles = useThemedStyles(createStyles);
  const { user } = useAuth();
  const { t } = useI18n();

  const [conversations, setConversations] = useState<ChatConversation[]>([]);
  const [filteredConversations, setFilteredConversations] = useState<ChatConversation[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [refreshing, setRefreshing] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (user) {
      initializeChat();
      setupEventListeners();
    }

    return () => {
      ChatService.removeAllListeners();
    };
  }, [user]);

  useEffect(() => {
    filterConversations();
  }, [conversations, searchQuery]);

  const initializeChat = async () => {
    if (!user) return;

    try {
      await ChatService.connect(user.id);
      loadConversations();
    } catch (error) {
      console.error('Error initializing chat:', error);
      setIsLoading(false);
    }
  };

  const setupEventListeners = () => {
    ChatService.on('conversationsLoaded', handleConversationsLoaded);
    ChatService.on('conversationUpdated', handleConversationUpdated);
    ChatService.on('conversationCreated', handleConversationCreated);
    ChatService.on('messageReceived', handleMessageReceived);
  };

  const handleConversationsLoaded = (loadedConversations: ChatConversation[]) => {
    setConversations(loadedConversations);
    setIsLoading(false);
  };

  const handleConversationUpdated = (updatedConversation: ChatConversation) => {
    setConversations(prev => 
      prev.map(conv => 
        conv.id === updatedConversation.id ? updatedConversation : conv
      )
    );
  };

  const handleConversationCreated = (newConversation: ChatConversation) => {
    setConversations(prev => [newConversation, ...prev]);
  };

  const handleMessageReceived = () => {
    // Refresh conversations to update last message and unread counts
    if (user) {
      loadConversations();
    }
  };

  const loadConversations = () => {
    if (!user) return;
    
    const userConversations = ChatService.getConversations(user.id);
    setConversations(userConversations);
  };

  const filterConversations = () => {
    if (!searchQuery.trim()) {
      setFilteredConversations(conversations);
      return;
    }

    const query = searchQuery.toLowerCase();
    const filtered = conversations.filter(conversation => {
      // Search in participant names
      const participantNames = Object.values(conversation.participantNames).join(' ').toLowerCase();
      if (participantNames.includes(query)) return true;

      // Search in last message
      if (conversation.lastMessage?.content.toLowerCase().includes(query)) return true;

      return false;
    });

    setFilteredConversations(filtered);
  };

  const onRefresh = React.useCallback(async () => {
    setRefreshing(true);
    loadConversations();
    setTimeout(() => setRefreshing(false), 1000);
  }, []);

  const handleConversationPress = (conversation: ChatConversation) => {
    navigation.navigate('Chat', { chatId: conversation.id });
  };

  const handleLongPress = (conversation: ChatConversation) => {
    Alert.alert(
      t('chat.conversationOptions'),
      t('chat.whatWouldYouLikeToDo'),
      [
        { text: t('common.cancel'), style: 'cancel' },
        {
          text: conversation.muted[user?.id || ''] ? t('chat.unmute') : t('chat.mute'),
          onPress: () => toggleMute(conversation),
        },
        {
          text: t('chat.archive'),
          onPress: () => archiveConversation(conversation),
        },
        {
          text: t('chat.delete'),
          style: 'destructive',
          onPress: () => deleteConversation(conversation),
        },
      ]
    );
  };

  const toggleMute = (conversation: ChatConversation) => {
    // Implementation would update mute status
    console.log('Toggle mute for conversation:', conversation.id);
  };

  const archiveConversation = (conversation: ChatConversation) => {
    // Implementation would archive conversation
    console.log('Archive conversation:', conversation.id);
  };

  const deleteConversation = (conversation: ChatConversation) => {
    Alert.alert(
      'Delete Conversation',
      'Are you sure you want to delete this conversation? This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            // Implementation would delete conversation
            console.log('Delete conversation:', conversation.id);
          },
        },
      ]
    );
  };

  const getOtherParticipant = (conversation: ChatConversation) => {
    if (!user) return null;
    
    const otherParticipantId = conversation.participants.find(id => id !== user.id);
    if (!otherParticipantId) return null;

    return {
      id: otherParticipantId,
      name: conversation.participantNames[otherParticipantId] || 'Unknown',
      avatar: conversation.participantAvatars[otherParticipantId],
    };
  };

  const formatLastMessageTime = (timestamp: string) => {
    const now = new Date();
    const messageTime = new Date(timestamp);
    const diffInHours = (now.getTime() - messageTime.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 1) {
      return messageTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffInHours < 24) {
      return messageTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffInHours < 168) { // 7 days
      return messageTime.toLocaleDateString([], { weekday: 'short' });
    } else {
      return messageTime.toLocaleDateString([], { month: 'short', day: 'numeric' });
    }
  };

  const renderConversationItem = ({ item }: { item: ChatConversation }) => {
    const otherParticipant = getOtherParticipant(item);
    if (!otherParticipant || !user) return null;

    const unreadCount = item.unreadCount[user.id] || 0;
    const isMuted = item.muted[user.id] || false;

    return (
      <RTLTouchableOpacity
        onPress={() => handleConversationPress(item)}
        onLongPress={() => handleLongPress(item)}
      >
        <Card style={styles.conversationCard} variant="outlined">
          <RTLView style={styles.conversationContent}>
            <RTLView style={styles.avatarContainer}>
              {otherParticipant.avatar ? (
                <Image source={{ uri: otherParticipant.avatar }} style={styles.avatar} />
              ) : (
                <RTLView style={styles.avatarPlaceholder}>
                  <RTLIcon name="person" size={24} color="#666" />
                </RTLView>
              )}
              {unreadCount > 0 && (
                <RTLView style={styles.unreadBadge}>
                  <RTLText style={styles.unreadText}>
                    {unreadCount > 99 ? '99+' : unreadCount}
                  </RTLText>
                </RTLView>
              )}
            </RTLView>

            <RTLView style={styles.conversationInfo}>
              <RTLView style={styles.conversationHeader}>
                <RTLText style={[styles.participantName, unreadCount > 0 && styles.unreadName].filter(Boolean) as any}>
                  {otherParticipant.name}
                </RTLText>
                <RTLView style={styles.conversationMeta}>
                  {isMuted && (
                    <RTLIcon name="volume-mute" size={14} color="#999" style={styles.muteIcon} />
                  )}
                  {item.lastMessage && (
                    <RTLText style={styles.lastMessageTime}>
                      {formatLastMessageTime(item.lastMessage.timestamp)}
                    </RTLText>
                  )}
                </RTLView>
              </RTLView>

              {item.lastMessage && (
                <RTLText
                  style={[styles.lastMessage, unreadCount > 0 && styles.unreadMessage].filter(Boolean) as any}
                  numberOfLines={1}
                >
                  {item.lastMessage.senderId === user.id ? t('chat.you') : ''}
                  {item.lastMessage.content}
                </RTLText>
              )}
            </RTLView>
          </RTLView>
        </Card>
      </RTLTouchableOpacity>
    );
  };

  const renderHeader = () => (
    <RTLView style={styles.header}>
      <Input
        placeholder={t('chat.searchConversations')}
        value={searchQuery}
        onChangeText={setSearchQuery}
        leftIcon="search-outline"
        style={styles.searchInput}
      />
    </RTLView>
  );

  const renderEmptyState = () => (
    <EmptyState
      icon="chatbubbles-outline"
      title={t('chat.noConversationsYet')}
      description={t('chat.startConversationDescription')}
      actionLabel={t('chat.browseVendors')}
      onAction={() => navigation.navigate('VendorList')}
    />
  );

  if (isLoading) {
    return (
      <RTLSafeAreaView style={styles.container}>
        <RTLView style={styles.loadingContainer}>
          <RTLText style={styles.loadingText}>{t('chat.loadingConversations')}</RTLText>
        </RTLView>
      </RTLSafeAreaView>
    );
  }

  return (
    <RTLSafeAreaView style={styles.container}>
      <RTLFlatList
        data={filteredConversations}
        renderItem={renderConversationItem}
        keyExtractor={(item) => item.id}
        ListHeaderComponent={renderHeader}
        ListEmptyComponent={renderEmptyState}
        contentContainerStyle={styles.listContent}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      />
    </RTLSafeAreaView>
  );
};

const createStyles = (colors: ThemeColors) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: FONT_SIZES.md,
    color: colors.textSecondary,
  },
  header: {
    paddingHorizontal: SPACING.lg,
    paddingTop: SPACING.md,
    paddingBottom: SPACING.lg,
  },
  searchInput: {
    marginBottom: 0,
  },
  listContent: {
    paddingBottom: SPACING.xl,
  },
  conversationCard: {
    marginHorizontal: SPACING.lg,
    marginBottom: SPACING.sm,
    padding: SPACING.md,
  },
  conversationContent: {
    flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
    alignItems: 'center',
  },
  avatarContainer: {
    position: 'relative',
    marginRight: SPACING.md,
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
  },
  avatarPlaceholder: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: colors.surface,
    justifyContent: 'center',
    alignItems: 'center',
  },
  unreadBadge: {
    position: 'absolute',
    top: -5,
    right: -5,
    backgroundColor: '#FF4444',
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: colors.background,
  },
  unreadText: {
    fontSize: FONT_SIZES.xs,
    fontWeight: FONT_WEIGHTS.bold,
    color: '#FFFFFF',
  },
  conversationInfo: {
    flex: 1,
  },
  conversationHeader: {
    flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.xs,
  },
  participantName: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.medium,
    color: colors.text,
    flex: 1,
  },
  unreadName: {
    fontWeight: FONT_WEIGHTS.semiBold,
  },
  conversationMeta: {
    flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
    alignItems: 'center',
  },
  muteIcon: {
    marginRight: SPACING.xs,
  },
  lastMessageTime: {
    fontSize: FONT_SIZES.xs,
    color: colors.textSecondary,
  },
  lastMessage: {
    fontSize: FONT_SIZES.sm,
    color: colors.textSecondary,
    lineHeight: 18,
  },
  unreadMessage: {
    color: colors.text,
    fontWeight: FONT_WEIGHTS.medium,
  },
  startChatButton: {
    backgroundColor: '#667eea',
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
    borderRadius: BORDER_RADIUS.md,
  },
  startChatText: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.medium,
    color: '#FFFFFF',
  },
});
