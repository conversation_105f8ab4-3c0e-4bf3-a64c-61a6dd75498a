import React, { createContext, useContext, useRef, useCallback, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useIntelligentCache } from '../performance/IntelligentCache';
import { useAuth } from '../../contexts/AuthContext';
import type { Vendor, Product } from '../../contexts/DataContext';

interface UserBehavior {
  userId: string;
  timestamp: number;
  action: 'view' | 'search' | 'favorite' | 'purchase' | 'share' | 'contact' | 'filter';
  entityType: 'vendor' | 'product' | 'category';
  entityId: string;
  metadata: {
    duration?: number;
    searchQuery?: string;
    category?: string;
    price?: number;
    rating?: number;
    location?: string;
    timeOfDay?: 'morning' | 'afternoon' | 'evening' | 'night';
    dayOfWeek?: string;
    sessionId?: string;
  };
}

interface UserPreferences {
  categories: Record<string, number>; // category -> preference score
  priceRanges: Record<string, number>; // price range -> preference score
  vendors: Record<string, number>; // vendor ID -> preference score
  features: Record<string, number>; // feature -> preference score
  timePatterns: Record<string, number>; // time pattern -> activity score
  culturalPreferences: {
    language: 'en' | 'ar';
    currency: 'BHD';
    localBrands: number; // preference for local brands (0-1)
    traditionalProducts: number; // preference for traditional products (0-1)
    modernProducts: number; // preference for modern products (0-1);
  };
}

interface PersonalizationConfig {
  learningRate: number;
  decayFactor: number;
  minInteractions: number;
  maxHistoryDays: number;
  enableRealTimeUpdates: boolean;
  enableCulturalAdaptation: boolean;
  enableTimeBasedRecommendations: boolean;
  enableCollaborativeFiltering: boolean;
}

interface RecommendationRequest {
  type: 'vendors' | 'products' | 'categories';
  context?: 'home' | 'search' | 'category' | 'vendor';
  limit?: number;
  excludeIds?: string[];
  includeReasons?: boolean;
}

interface Recommendation {
  id: string;
  type: 'vendor' | 'product' | 'category';
  score: number;
  confidence: number;
  reasons: string[];
  data: Vendor | Product | any;
}

interface PersonalizationEngineContextType {
  trackBehavior: (behavior: Omit<UserBehavior, 'userId' | 'timestamp'>) => Promise<void>;
  getRecommendations: (request: RecommendationRequest) => Promise<Recommendation[]>;
  getUserPreferences: () => Promise<UserPreferences>;
  updatePreferences: (preferences: Partial<UserPreferences>) => Promise<void>;
  getPersonalizedContent: (contentType: string, options?: any) => Promise<any[]>;
  resetPersonalization: () => Promise<void>;
  exportUserData: () => Promise<string>;
  getInsights: () => Promise<any>;
}

const DEFAULT_CONFIG: PersonalizationConfig = {
  learningRate: 0.1,
  decayFactor: 0.95,
  minInteractions: 5,
  maxHistoryDays: 90,
  enableRealTimeUpdates: true,
  enableCulturalAdaptation: true,
  enableTimeBasedRecommendations: true,
  enableCollaborativeFiltering: false, // Requires backend support
};

const PersonalizationEngineContext = createContext<PersonalizationEngineContextType | undefined>(undefined);

export const usePersonalizationEngine = (): PersonalizationEngineContextType => {
  const context = useContext(PersonalizationEngineContext);
  if (!context) {
    throw new Error('usePersonalizationEngine must be used within a PersonalizationEngineProvider');
  }
  return context;
};

interface PersonalizationEngineProviderProps {
  children: React.ReactNode;
  config?: Partial<PersonalizationConfig>;
}

export const PersonalizationEngineProvider: React.FC<PersonalizationEngineProviderProps> = ({
  children,
  config = {},
}) => {
  const finalConfig = useRef({ ...DEFAULT_CONFIG, ...config });
  const cache = useIntelligentCache();
  const { user } = useAuth();
  
  // Behavior tracking
  const behaviorHistory = useRef<UserBehavior[]>([]);
  const sessionId = useRef<string>(Date.now().toString());
  
  // User preferences
  const userPreferences = useRef<UserPreferences>({
    categories: {},
    priceRanges: {},
    vendors: {},
    features: {},
    timePatterns: {},
    culturalPreferences: {
      language: 'en',
      currency: 'BHD',
      localBrands: 0.5,
      traditionalProducts: 0.5,
      modernProducts: 0.5,
    },
  });

  // Learning algorithms
  const updateTimer = useRef<NodeJS.Timeout>();

  useEffect(() => {
    if (user?.id) {
      initializePersonalization();
      startPeriodicUpdates();
    }

    return () => {
      if (updateTimer.current) {
        clearInterval(updateTimer.current);
      }
    };
  }, [user?.id]);

  const initializePersonalization = async () => {
    try {
      // Load existing preferences
      const savedPreferences = await AsyncStorage.getItem(`preferences:${user?.id}`);
      if (savedPreferences) {
        userPreferences.current = JSON.parse(savedPreferences);
      }

      // Load behavior history
      const savedHistory = await cache.get<UserBehavior[]>(`behavior:${user?.id}`);
      if (savedHistory) {
        behaviorHistory.current = savedHistory;
        await processHistoricalData();
      }
    } catch (error) {
      console.warn('Failed to initialize personalization:', error);
    }
  };

  const startPeriodicUpdates = () => {
    if (!finalConfig.current.enableRealTimeUpdates) return;

    updateTimer.current = setInterval(() => {
      updatePreferencesFromBehavior();
      cleanupOldData();
    }, 60000); // Update every minute
  };

  const trackBehavior = useCallback(async (
    behavior: Omit<UserBehavior, 'userId' | 'timestamp'>
  ): Promise<void> => {
    if (!user?.id) return;

    const now = new Date();
    const timeOfDay = getTimeOfDay(now);
    const dayOfWeek = now.toLocaleDateString('en-US', { weekday: 'long' });

    const fullBehavior: UserBehavior = {
      ...behavior,
      userId: user.id,
      timestamp: Date.now(),
      metadata: {
        ...behavior.metadata,
        timeOfDay,
        dayOfWeek,
        sessionId: sessionId.current,
      },
    };

    // Add to behavior history
    behaviorHistory.current.push(fullBehavior);

    // Update preferences immediately for high-impact actions
    if (['favorite', 'purchase', 'contact'].includes(behavior.action)) {
      await updatePreferencesFromBehavior();
    }

    // Cache behavior data
    await cache.set(`behavior:${user.id}`, behaviorHistory.current, {
      ttl: finalConfig.current.maxHistoryDays * 24 * 60 * 60 * 1000,
      tags: ['user-behavior'],
      priority: 'medium',
    });
  }, [user?.id, cache]);

  const getTimeOfDay = (date: Date): 'morning' | 'afternoon' | 'evening' | 'night' => {
    const hour = date.getHours();
    if (hour >= 6 && hour < 12) return 'morning';
    if (hour >= 12 && hour < 17) return 'afternoon';
    if (hour >= 17 && hour < 22) return 'evening';
    return 'night';
  };

  const updatePreferencesFromBehavior = async () => {
    const recentBehaviors = behaviorHistory.current.filter(
      b => Date.now() - b.timestamp < 7 * 24 * 60 * 60 * 1000 // Last 7 days
    );

    if (recentBehaviors.length < finalConfig.current.minInteractions) return;

    const preferences = { ...userPreferences.current };

    // Update category preferences
    recentBehaviors.forEach(behavior => {
      if (behavior.metadata.category) {
        const weight = getActionWeight(behavior.action);
        const currentScore = preferences.categories[behavior.metadata.category] || 0;
        preferences.categories[behavior.metadata.category] = 
          currentScore + (weight * finalConfig.current.learningRate);
      }
    });

    // Update price range preferences
    recentBehaviors.forEach(behavior => {
      if (behavior.metadata.price) {
        const priceRange = getPriceRange(behavior.metadata.price);
        const weight = getActionWeight(behavior.action);
        const currentScore = preferences.priceRanges[priceRange] || 0;
        preferences.priceRanges[priceRange] = 
          currentScore + (weight * finalConfig.current.learningRate);
      }
    });

    // Update vendor preferences
    recentBehaviors.forEach(behavior => {
      if (behavior.entityType === 'vendor') {
        const weight = getActionWeight(behavior.action);
        const currentScore = preferences.vendors[behavior.entityId] || 0;
        preferences.vendors[behavior.entityId] = 
          currentScore + (weight * finalConfig.current.learningRate);
      }
    });

    // Update time pattern preferences
    recentBehaviors.forEach(behavior => {
      if (behavior.metadata.timeOfDay) {
        const pattern = `${behavior.metadata.dayOfWeek}-${behavior.metadata.timeOfDay}`;
        const currentScore = preferences.timePatterns[pattern] || 0;
        preferences.timePatterns[pattern] = currentScore + finalConfig.current.learningRate;
      }
    });

    // Apply decay to all preferences
    Object.keys(preferences.categories).forEach(key => {
      preferences.categories[key] *= finalConfig.current.decayFactor;
    });
    Object.keys(preferences.priceRanges).forEach(key => {
      preferences.priceRanges[key] *= finalConfig.current.decayFactor;
    });
    Object.keys(preferences.vendors).forEach(key => {
      preferences.vendors[key] *= finalConfig.current.decayFactor;
    });

    userPreferences.current = preferences;

    // Save preferences
    await AsyncStorage.setItem(
      `preferences:${user?.id}`,
      JSON.stringify(preferences)
    );
  };

  const getActionWeight = (action: string): number => {
    const weights = {
      view: 1,
      search: 2,
      favorite: 5,
      purchase: 10,
      share: 3,
      contact: 7,
      filter: 1,
    };
    return weights[action as keyof typeof weights] || 1;
  };

  const getPriceRange = (price: number): string => {
    if (price < 10) return '0-10';
    if (price < 25) return '10-25';
    if (price < 50) return '25-50';
    if (price < 100) return '50-100';
    return '100+';
  };

  const processHistoricalData = async () => {
    // Process historical behavior to build initial preferences
    await updatePreferencesFromBehavior();
  };

  const cleanupOldData = () => {
    const cutoffTime = Date.now() - (finalConfig.current.maxHistoryDays * 24 * 60 * 60 * 1000);
    behaviorHistory.current = behaviorHistory.current.filter(
      behavior => behavior.timestamp > cutoffTime
    );
  };

  const getRecommendations = useCallback(async (
    request: RecommendationRequest
  ): Promise<Recommendation[]> => {
    // This would typically call a backend ML service
    // For now, we'll implement basic collaborative filtering
    
    const preferences = userPreferences.current;
    const recommendations: Recommendation[] = [];

    // Simple recommendation logic based on preferences
    if (request.type === 'vendors') {
      const vendorScores = Object.entries(preferences.vendors)
        .sort(([, a], [, b]) => b - a)
        .slice(0, request.limit || 10);

      vendorScores.forEach(([vendorId, score]) => {
        if (!request.excludeIds?.includes(vendorId)) {
          recommendations.push({
            id: vendorId,
            type: 'vendor',
            score,
            confidence: Math.min(score / 10, 1),
            reasons: ['Based on your previous interactions'],
            data: null, // Would be populated with actual vendor data
          });
        }
      });
    }

    return recommendations;
  }, []);

  const getUserPreferences = useCallback(async (): Promise<UserPreferences> => {
    return { ...userPreferences.current };
  }, []);

  const updatePreferences = useCallback(async (
    preferences: Partial<UserPreferences>
  ): Promise<void> => {
    userPreferences.current = { ...userPreferences.current, ...preferences };
    await AsyncStorage.setItem(
      `preferences:${user?.id}`,
      JSON.stringify(userPreferences.current)
    );
  }, [user?.id]);

  const getPersonalizedContent = useCallback(async (
    contentType: string,
    options: any = {}
  ): Promise<any[]> => {
    // Implement personalized content retrieval
    return [];
  }, []);

  const resetPersonalization = useCallback(async (): Promise<void> => {
    behaviorHistory.current = [];
    userPreferences.current = {
      categories: {},
      priceRanges: {},
      vendors: {},
      features: {},
      timePatterns: {},
      culturalPreferences: {
        language: 'en',
        currency: 'BHD',
        localBrands: 0.5,
        traditionalProducts: 0.5,
        modernProducts: 0.5,
      },
    };

    await AsyncStorage.removeItem(`preferences:${user?.id}`);
    await cache.remove(`behavior:${user?.id}`);
  }, [user?.id, cache]);

  const exportUserData = useCallback(async (): Promise<string> => {
    const data = {
      preferences: userPreferences.current,
      behaviorHistory: behaviorHistory.current,
      exportDate: new Date().toISOString(),
    };
    return JSON.stringify(data, null, 2);
  }, []);

  const getInsights = useCallback(async (): Promise<any> => {
    const totalInteractions = behaviorHistory.current.length;
    const categoryDistribution = Object.entries(userPreferences.current.categories)
      .sort(([, a], [, b]) => b - a);
    
    return {
      totalInteractions,
      topCategories: categoryDistribution.slice(0, 5),
      activityPattern: userPreferences.current.timePatterns,
      culturalProfile: userPreferences.current.culturalPreferences,
    };
  }, []);

  const contextValue: PersonalizationEngineContextType = {
    trackBehavior,
    getRecommendations,
    getUserPreferences,
    updatePreferences,
    getPersonalizedContent,
    resetPersonalization,
    exportUserData,
    getInsights,
  };

  return (
    <PersonalizationEngineContext.Provider value={contextValue}>
      {children}
    </PersonalizationEngineContext.Provider>
  );
};
