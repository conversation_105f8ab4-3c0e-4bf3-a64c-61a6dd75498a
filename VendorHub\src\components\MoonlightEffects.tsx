import React, { useEffect, useRef } from 'react';
import { Animated, Dimensions, Easing, StyleSheet, ViewStyle } from 'react-native';
import { RTLView } from './RTL';
import { getAnimationConfig } from '../utils/webCompatibility';

const { width, height } = Dimensions.get('window');

export interface MoonlightEffectsProps {
  intensity?: 'subtle' | 'gentle' | 'moderate';
  particleCount?: number;
  style?: ViewStyle;
  children?: React.ReactNode;
}

export const MoonlightEffects: React.FC<MoonlightEffectsProps> = ({
  intensity = 'subtle',
  particleCount = 12,
  style,
  children,
}) => {
  const particles = useRef<Animated.Value[]>([]);
  const opacities = useRef<Animated.Value[]>([]);
  const scales = useRef<Animated.Value[]>([]);

  // إنشاء الجسيمات المضيئة
  useEffect(() => {
    // إنشاء القيم المتحركة للجسيمات
    for (let i = 0; i < particleCount; i++) {
      particles.current[i] = new Animated.Value(Math.random());
      opacities.current[i] = new Animated.Value(0);
      scales.current[i] = new Animated.Value(0.5);
    }

    // بدء الرسوم المتحركة
    startAnimations();

    return () => {
      // تنظيف الرسوم المتحركة عند إلغاء التحميل
      particles.current.forEach(particle => particle.stopAnimation());
      opacities.current.forEach(opacity => opacity.stopAnimation());
      scales.current.forEach(scale => scale.stopAnimation());
    };
  }, [particleCount]);

  const startAnimations = () => {
    particles.current.forEach((particle, index) => {
      // رسوم متحركة للوميض
      const flickerAnimation = () => {
        const delay = Math.random() * 3000; // تأخير عشوائي
        const duration = 2000 + Math.random() * 2000; // مدة عشوائية

        Animated.sequence([
          Animated.delay(delay),
          Animated.timing(opacities.current[index], getAnimationConfig({
            toValue: getIntensityValue(),
            duration: duration / 2,
            easing: Easing.bezier(0.4, 0, 0.6, 1),
            useNativeDriver: true,
          })),
          Animated.timing(opacities.current[index], getAnimationConfig({
            toValue: 0,
            duration: duration / 2,
            easing: Easing.bezier(0.4, 0, 0.6, 1),
            useNativeDriver: true,
          })),
        ]).start(() => flickerAnimation());
      };

      // رسوم متحركة للحجم
      const scaleAnimation = () => {
        const duration = 3000 + Math.random() * 2000;
        
        Animated.timing(scales.current[index], getAnimationConfig({
          toValue: 0.8 + Math.random() * 0.4,
          duration,
          easing: Easing.inOut(Easing.sin),
          useNativeDriver: true,
        })).start(() => {
          Animated.timing(scales.current[index], getAnimationConfig({
            toValue: 0.5 + Math.random() * 0.3,
            duration,
            easing: Easing.inOut(Easing.sin),
            useNativeDriver: true,
          })).start(() => scaleAnimation());
        });
      };

      // رسوم متحركة للحركة البطيئة
      const floatAnimation = () => {
        const duration = 8000 + Math.random() * 4000;
        
        Animated.timing(particle, getAnimationConfig({
          toValue: Math.random(),
          duration,
          easing: Easing.inOut(Easing.sin),
          useNativeDriver: true,
        })).start(() => floatAnimation());
      };

      // بدء جميع الرسوم المتحركة
      flickerAnimation();
      scaleAnimation();
      floatAnimation();
    });
  };

  const getIntensityValue = () => {
    switch (intensity) {
      case 'subtle':
        return 0.15 + Math.random() * 0.1; // 0.15 - 0.25
      case 'gentle':
        return 0.2 + Math.random() * 0.15; // 0.2 - 0.35
      case 'moderate':
        return 0.25 + Math.random() * 0.2; // 0.25 - 0.45
      default:
        return 0.15 + Math.random() * 0.1;
    }
  };

  const getParticleStyle = (index: number) => {
    const baseSize = 60 + Math.random() * 40; // حجم أساسي عشوائي
    const left = Math.random() * width;
    const top = Math.random() * height;

    return {
      position: 'absolute' as const,
      left,
      top,
      width: baseSize,
      height: baseSize,
      borderRadius: baseSize / 2,
      backgroundColor: '#F8FAFC', // لون القمر الناعم
      opacity: opacities.current[index],
      transform: [
        {
          translateX: particles.current[index].interpolate({
            inputRange: [0, 1],
            outputRange: [-20, 20],
          }),
        },
        {
          translateY: particles.current[index].interpolate({
            inputRange: [0, 1],
            outputRange: [-30, 30],
          }),
        },
        {
          scale: scales.current[index],
        },
      ],
      shadowColor: '#F1F5F9',
      shadowOffset: { width: 0, height: 0 },
      shadowOpacity: 0.8,
      shadowRadius: 20,
      elevation: 0,
    };
  };

  return (
    <RTLView style={[styles.container, style]}>
      {/* الجسيمات المضيئة */}
      {particles.current.map((_, index) => (
        <Animated.View
          key={index}
          style={[getParticleStyle(index), { pointerEvents: 'none' }]}
        />
      ))}
      
      {/* المحتوى */}
      {children}
    </RTLView>
  );
};

// تأثير الوميض الخفيف للخلفية
export interface SubtleGlowProps {
  children?: React.ReactNode;
  style?: ViewStyle;
}

export const SubtleGlow: React.FC<SubtleGlowProps> = ({ children, style }) => {
  const glowOpacity = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const glowAnimation = () => {
      Animated.sequence([
        Animated.timing(glowOpacity, getAnimationConfig({
          toValue: 0.08,
          duration: 4000,
          easing: Easing.inOut(Easing.sin),
          useNativeDriver: true,
        })),
        Animated.timing(glowOpacity, getAnimationConfig({
          toValue: 0.02,
          duration: 4000,
          easing: Easing.inOut(Easing.sin),
          useNativeDriver: true,
        })),
      ]).start(() => glowAnimation());
    };

    glowAnimation();

    return () => glowOpacity.stopAnimation();
  }, []);

  return (
    <RTLView style={[styles.container, style]}>
      {/* طبقة الوهج الخفيف */}
      <Animated.View
        style={[
          styles.glowLayer,
          {
            opacity: glowOpacity,
            pointerEvents: 'none',
          },
        ]}
      />
      
      {children}
    </RTLView>
  );
};

// تأثير النجوم المتلألئة
export interface TwinklingStarsProps {
  starCount?: number;
  children?: React.ReactNode;
  style?: ViewStyle;
}

export const TwinklingStars: React.FC<TwinklingStarsProps> = ({
  starCount = 8,
  children,
  style,
}) => {
  const stars = useRef<Animated.Value[]>([]).current;

  useEffect(() => {
    // إنشاء النجوم
    for (let i = 0; i < starCount; i++) {
      stars[i] = new Animated.Value(0);
    }

    // بدء رسوم النجوم المتحركة
    stars.forEach((star, index) => {
      const twinkleAnimation = () => {
        const delay = Math.random() * 2000;
        const duration = 1500 + Math.random() * 1000;

        Animated.sequence([
          Animated.delay(delay),
          Animated.timing(star, getAnimationConfig({
            toValue: 1,
            duration: duration / 2,
            easing: Easing.out(Easing.quad),
            useNativeDriver: true,
          })),
          Animated.timing(star, getAnimationConfig({
            toValue: 0,
            duration: duration / 2,
            easing: Easing.in(Easing.quad),
            useNativeDriver: true,
          })),
        ]).start(() => twinkleAnimation());
      };

      twinkleAnimation();
    });

    return () => {
      stars.forEach(star => star.stopAnimation());
    };
  }, [starCount]);

  const getStarStyle = (index: number) => {
    const size = 3 + Math.random() * 4; // حجم صغير للنجوم
    const left = Math.random() * width;
    const top = Math.random() * (height * 0.6); // النجوم في الجزء العلوي

    return {
      position: 'absolute' as const,
      left,
      top,
      width: size,
      height: size,
      borderRadius: size / 2,
      backgroundColor: '#F8FAFC',
      opacity: stars[index],
      shadowColor: '#F1F5F9',
      shadowOffset: { width: 0, height: 0 },
      shadowOpacity: 1,
      shadowRadius: 4,
      elevation: 0,
    };
  };

  return (
    <RTLView style={[styles.container, style]}>
      {/* النجوم المتلألئة */}
      {stars.map((_, index) => (
        <Animated.View
          key={index}
          style={[getStarStyle(index), { pointerEvents: 'none' }]}
        />
      ))}
      
      {children}
    </RTLView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    position: 'relative',
  },
  glowLayer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: '#F8FAFC',
    zIndex: 1,
  },
});

export default MoonlightEffects;
