import React, { useState } from 'react';
import { StyleSheet, Alert } from 'react-native';
import { useThemedStyles } from '../../hooks';
import {
  Card,
  AnimatedButton,
  SuccessButton,
  <PERSON>rrorButton,
  PulseButton,
  GlowButton,
  <PERSON>loatingActionButton,
  AnimatedLoader,
  SwipeableRow,
  SwipeActions,
  LongPressMenu,
  MenuActions,
  ZoomableImage,
  SingleImagePicker,
  ScreenTransition,
  SlideUpTransition,
  ScaleInTransition } from '../../components';
import { RTLSafeAreaView, RTLView, RTLText, RTLScrollView, RTLIcon } from '../../components/RTL';
import {
  SPACING,
  FONT_SIZES,
  FONT_WEIGHTS,
  BORDER_RADIUS } from '../../constants/theme';
import type ThemeColors  from '../../contexts/ThemeContext';

interface FeatureShowcaseScreenProps {
  navigation: any;
}

export const FeatureShowcaseScreen: React.FC<FeatureShowcaseScreenProps> = ({ navigation }) => {
  const styles = useThemedStyles(createStyles);
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [loaderType, setLoaderType] = useState<'dots' | 'bars' | 'pulse' | 'wave' | 'spinner' | 'skeleton'>('dots');

  const handleImageChange = (image: string | null) => {
    setSelectedImage(image);
    if (image) {
      Alert.alert('Image Selected', 'Image picker working perfectly!');
    }
  };

  const demoActions = [
    {
      id: 'camera',
      icon: 'camera',
      label: 'Camera',
      onPress: () => Alert.alert('Camera', 'Camera action triggered!'),
    },
    {
      id: 'gallery',
      icon: 'images',
      label: 'Gallery',
      onPress: () => Alert.alert('Gallery', 'Gallery action triggered!'),
    },
    {
      id: 'settings',
      icon: 'settings',
      label: 'Settings',
      onPress: () => Alert.alert('Settings', 'Settings action triggered!'),
    },
  ];

  const renderAnimatedButtons = () => (
    <SlideUpTransition delay={100}>
      <Card style={styles.section} variant="elevated">
        <RTLText style={styles.sectionTitle}>🎯 Animated Buttons</RTLText>
        <RTLText style={styles.sectionDescription}>
          Buttons with haptic feedback and smooth animations
        </RTLText>

        <RTLView style={styles.buttonGrid}>
          <AnimatedButton
            title="Scale"
            animationType="scale"
            hapticFeedback="light"
            onPress={() => Alert.alert('Scale Button', 'Scale animation with light haptic!')}
            style={styles.demoButton}
          />
          
          <AnimatedButton
            title="Bounce"
            animationType="bounce"
            hapticFeedback="medium"
            onPress={() => Alert.alert('Bounce Button', 'Bounce animation with medium haptic!')}
            style={styles.demoButton}
          />
          
          <PulseButton
            title="Pulse"
            onPress={() => Alert.alert('Pulse Button', 'Pulse animation!')}
            style={styles.demoButton}
          />
          
          <GlowButton
            title="Glow"
            onPress={() => Alert.alert('Glow Button', 'Glow effect!')}
            style={styles.demoButton}
          />
          
          <SuccessButton
            title="Success"
            onPress={() => Alert.alert('Success!', 'Success animation with haptic!')}
            style={styles.demoButton}
          />
          
          <ErrorButton
            title="Error"
            onPress={() => Alert.alert('Error!', 'Error animation with haptic!')}
            style={styles.demoButton}
          />
        </RTLView>
      </Card>
    </SlideUpTransition>
  );

  const renderSwipeActions = () => (
    <SlideUpTransition delay={200}>
      <Card style={styles.section} variant="elevated">
        <RTLText style={styles.sectionTitle}>👆 Swipe Actions</RTLText>
        <RTLText style={styles.sectionDescription}>
          Swipe left or right to reveal actions with haptic feedback
        </RTLText>

        <SwipeableRow
          leftActions={[
            SwipeActions.approve(() => Alert.alert('Approved!', 'Item approved with haptic feedback!')),
          ]}
          rightActions={[
            SwipeActions.delete(() => Alert.alert('Deleted!', 'Item deleted with haptic feedback!')),
          ]}
        >
          <Card style={styles.swipeCard} variant="outlined">
            <RTLView style={styles.swipeContent}>
              <RTLIcon name="document-text" size={24} color="#667eea" />
              <RTLText style={styles.swipeText}>Swipe me left or right!</RTLText>
              <RTLText style={styles.swipeHint}>← Approve | Delete →</RTLText>
            </RTLView>
          </Card>
        </SwipeableRow>
      </Card>
    </SlideUpTransition>
  );

  const renderLongPressMenu = () => (
    <SlideUpTransition delay={300}>
      <Card style={styles.section} variant="elevated">
        <RTLText style={styles.sectionTitle}>⏰ Long Press Menu</RTLText>
        <RTLText style={styles.sectionDescription}>
          Long press the card below to see context menu with haptic feedback
        </RTLText>

        <LongPressMenu
          actions={[
            MenuActions.edit(() => Alert.alert('Edit', 'Edit action triggered!')),
            MenuActions.share(() => Alert.alert('Share', 'Share action triggered!')),
            MenuActions.delete(() => Alert.alert('Delete', 'Delete action triggered!')),
          ]}
        >
          <Card style={styles.longPressCard} variant="outlined">
            <RTLView style={styles.longPressContent}>
              <RTLIcon name="hand-left" size={32} color="#667eea" />
              <RTLText style={styles.longPressText}>Long press me!</RTLText>
              <RTLText style={styles.longPressHint}>Hold for 500ms to see menu</RTLText>
            </RTLView>
          </Card>
        </LongPressMenu>
      </Card>
    </SlideUpTransition>
  );

  const renderLoaders = () => (
    <SlideUpTransition delay={400}>
      <Card style={styles.section} variant="elevated">
        <RTLText style={styles.sectionTitle}>⏳ Loading Animations</RTLText>
        <RTLText style={styles.sectionDescription}>
          Various loading animation styles
        </RTLText>

        <RTLView style={styles.loaderControls}>
          {(['dots', 'bars', 'pulse', 'wave', 'spinner', 'skeleton'] as const).map((type) => (
            <AnimatedButton
              key={type}
              title={type.charAt(0).toUpperCase() + type.slice(1)}
              onPress={() => setLoaderType(type)}
              variant={loaderType === type ? 'primary' : 'outline'}
              size="small"
              style={styles.loaderButton}
            />
          ))}
        </RTLView>

        <RTLView style={styles.loaderDisplay}>
          <AnimatedLoader
            type={loaderType}
            size="large"
            color="#667eea"
            speed="normal"
          />
        </RTLView>
      </Card>
    </SlideUpTransition>
  );

  const renderImagePicker = () => (
    <SlideUpTransition delay={500}>
      <Card style={styles.section} variant="elevated">
        <RTLText style={styles.sectionTitle}>📷 Image Picker</RTLText>
        <RTLText style={styles.sectionDescription}>
          Camera and gallery access with image optimization
        </RTLText>

        <SingleImagePicker
          image={selectedImage}
          onImageChange={handleImageChange}
          title="Demo Image"
          subtitle="Tap to select from camera or gallery"
          size="large"
          shape="square"
        />
      </Card>
    </SlideUpTransition>
  );

  const renderZoomableImage = () => (
    <SlideUpTransition delay={600}>
      <Card style={styles.section} variant="elevated">
        <RTLText style={styles.sectionTitle}>🔍 Zoomable Image</RTLText>
        <RTLText style={styles.sectionDescription}>
          Pinch to zoom, double tap, and fullscreen support
        </RTLText>

        <ZoomableImage
          source={{ uri: 'https://picsum.photos/400/300' }}
          style={styles.zoomableImage}
          enableFullscreen={true}
          maxZoom={3}
          onZoomStart={() => console.log('Zoom started')}
          onZoomEnd={() => console.log('Zoom ended')}
        />
      </Card>
    </SlideUpTransition>
  );

  return (
    <RTLSafeAreaView style={styles.container}>
      <ScreenTransition type="fade" duration={300}>
        <RTLScrollView
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          <ScaleInTransition>
            <RTLView style={styles.header}>
              <RTLText style={styles.title}>🚀 Feature Showcase</RTLText>
              <RTLText style={styles.subtitle}>
                Explore all the advanced mobile features implemented in Phase 3
              </RTLText>
            </RTLView>
          </ScaleInTransition>

          {renderAnimatedButtons()}
          {renderSwipeActions()}
          {renderLongPressMenu()}
          {renderLoaders()}
          {renderImagePicker()}
          {renderZoomableImage()}

          <RTLView style={styles.footer}>
            <RTLText style={styles.footerText}>
              All features include haptic feedback and smooth animations! 🎉
            </RTLText>
          </RTLView>
        </RTLScrollView>

        {/* Floating Action Button */}
        <FloatingActionButton
          icon="rocket"
          actions={demoActions}
          position="bottom-right"
          size="large"
          backgroundColor="#667eea"
        />
      </ScreenTransition>
    </RTLSafeAreaView>
  );
};

const createStyles = (colors: ThemeColors) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  scrollContent: {
    padding: SPACING.lg,
    paddingBottom: 100, // Space for FAB
  },
  header: {
    alignItems: 'center',
    marginBottom: SPACING.xl,
  },
  title: {
    fontSize: FONT_SIZES.xxl,
    fontWeight: FONT_WEIGHTS.bold,
    color: colors.text,
    textAlign: 'center',
    marginBottom: SPACING.sm,
  },
  subtitle: {
    fontSize: FONT_SIZES.md,
    color: colors.textSecondary,
    textAlign: 'center',
    lineHeight: 22,
  },
  section: {
    marginBottom: SPACING.lg,
  },
  sectionTitle: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.semibold,
    color: colors.text,
    marginBottom: SPACING.xs,
  },
  sectionDescription: {
    fontSize: FONT_SIZES.sm,
    color: colors.textSecondary,
    marginBottom: SPACING.md,
    lineHeight: 20,
  },
  buttonGrid: {
    flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
    flexWrap: 'wrap',
    gap: SPACING.sm,
  },
  demoButton: {
    flex: 1,
    minWidth: '45%',
  },
  swipeCard: {
    marginVertical: SPACING.sm,
  },
  swipeContent: {
    flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
    alignItems: 'center',
    padding: SPACING.md,
    gap: SPACING.sm,
  },
  swipeText: {
    flex: 1,
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.medium,
    color: colors.text,
  },
  swipeHint: {
    fontSize: FONT_SIZES.xs,
    color: colors.textSecondary,
    fontStyle: 'italic',
  },
  longPressCard: {
    marginVertical: SPACING.sm,
  },
  longPressContent: {
    alignItems: 'center',
    padding: SPACING.lg,
    gap: SPACING.sm,
  },
  longPressText: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.medium,
    color: colors.text,
  },
  longPressHint: {
    fontSize: FONT_SIZES.xs,
    color: colors.textSecondary,
    fontStyle: 'italic',
  },
  loaderControls: {
    flexDirection: 'row', // RTL: Consider using 'row-reverse' for RTL layouts
    flexWrap: 'wrap',
    gap: SPACING.xs,
    marginBottom: SPACING.md,
  },
  loaderButton: {
    flex: 1,
    minWidth: '30%',
  },
  loaderDisplay: {
    alignItems: 'center',
    padding: SPACING.xl,
  },
  zoomableImage: {
    width: '100%',
    height: 200,
    borderRadius: BORDER_RADIUS.md,
  },
  footer: {
    alignItems: 'center',
    marginTop: SPACING.xl,
    marginBottom: SPACING.lg,
  },
  footerText: {
    fontSize: FONT_SIZES.md,
    color: colors.textSecondary,
    textAlign: 'center',
    fontStyle: 'italic',
  },
});
